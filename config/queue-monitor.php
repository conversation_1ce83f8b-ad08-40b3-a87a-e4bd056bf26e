<?php

return [
    /*
     |--------------------------------------------------------------------------
     | Queue Monitor Configuration
     |--------------------------------------------------------------------------
     |
     | This package monitors jobs processed by the queue. The default setup
     | stores job information in the database. Adjust the options as needed.
     |
     */

    // Enable or disable the monitor.
    'enabled' => env('QUEUE_MONITOR_ENABLED', true),

    // Database connection to use for the monitor. Null uses the default connection.
    'connection' => env('QUEUE_MONITOR_DB_CONNECTION', null),

    // Table name where job entries are stored.
    'table' => env('QUEUE_MONITOR_TABLE', 'queue_monitor_jobs'),

    // List the queues that should be monitored. Use an empty array to monitor all queues.
    'queues' => [
        // Empty array means monitor ALL queues
    ],

    'monitor_queued_jobs' => true,

    // The optional UI settings.
    'ui' => [
        // Enable the UI
        'enabled' => false,

        // Accepts route group configuration
        'route' => [
            'prefix' => 'jobs-queue',
            // 'middleware' => [],
        ],

        // Set the monitored jobs count to be displayed per page.
        'per_page' => 35,

        // Show custom data stored on model
        'show_custom_data' => false,

        // Allow the deletion of single monitor items.
        'allow_deletion' => true,

        // Allow retry for a single failed monitor item.
        'allow_retry' => true,

        // Allow purging all monitor entries.
        'allow_purge' => true,

        'show_metrics' => true,

        // Time frame used to calculate metrics values (in days).
        'metrics_time_frame' => 14,

        // The interval before refreshing the dashboard (in seconds).
        'refresh_interval' => null,

        // Order the queued but not started jobs first
        'order_queued_first' => false,
    ],

    // Cleanup settings
    'cleanup' => [
        // Enable automatic cleanup
        'enabled' => true,

        // Cleanup mode: 'days' or 'count'
        'mode' => 'days',

        // Keep records for X days (when mode is 'days') - Reduced for all-job monitoring
        'keep' => 3,

        // Keep X records (when mode is 'count')
        'count' => 1000,
    ],
];
