# Queue Monitor Documentation

## Overview

The Queue Monitor system provides comprehensive tracking and management of Laravel queue jobs, specifically designed for monitoring the RepriceAllJob and other background tasks in the ClickItFame application.

## Documentation Structure

### 📋 [Setup Checklist](QUEUE_MONITOR_SETUP_CHECKLIST.md)
**Quick reference checklist for installation and verification**
- Pre-deployment checklist
- Post-deployment verification
- Production monitoring tasks
- Troubleshooting quick reference

### 📖 [Deployment Guide](DEPLOYMENT_GUIDE_QUEUE_MONITOR.md)
**Comprehensive installation and configuration guide**
- Package installation steps
- Configuration details
- Production setup
- Security considerations
- Performance optimization

### 🚀 [Main Deployment Guide](DEPLOYMENT_GUIDE.md)
**Integrated deployment process including queue monitor**
- Complete application deployment
- Queue monitor as part of Phase 5
- Verification procedures
- Health checks

## Quick Start

### 1. Installation
```bash
composer require romanzipp/laravel-queue-monitor
php artisan vendor:publish --provider="romanzipp\QueueMonitor\Providers\QueueMonitorProvider" --tag="config"
php artisan vendor:publish --provider="romanzipp\QueueMonitor\Providers\QueueMonitorProvider" --tag="migrations"
php artisan migrate
```

### 2. Configuration
```php
// config/queue-monitor.php
return [
    'enabled' => true,
    'queues' => ['default'],
    'cleanup' => ['enabled' => true, 'keep' => 7],
];
```

### 3. Enable Job Monitoring
```php
// In your job class
use romanzipp\QueueMonitor\Traits\IsMonitored;

class RepriceAllJob implements ShouldQueue
{
    use IsMonitored; // Add this trait
}
```

### 4. Access Dashboard
Navigate to: `/admin/queue-monitor`

## Features

### 📊 **Dashboard**
- Real-time job statistics
- Performance metrics and charts
- Recent job activity
- One-click repricing job dispatch

### 📋 **Job Management**
- Comprehensive job listing with filters
- Detailed job information and timing
- Error traces for failed jobs
- Retry functionality for failed jobs

### 🔍 **Monitoring**
- Automatic job tracking
- Performance metrics collection
- Error logging and analysis
- Historical data retention

### 🛠 **Administration**
- Admin-only access control
- Job cleanup and maintenance
- Performance optimization tools
- Security features

## Architecture

### Components
1. **Queue Monitor Package**: Core monitoring functionality
2. **Custom Admin Dashboard**: User interface for job management
3. **Database Table**: `queue_monitor_jobs` for storing job data
4. **Job Traits**: `IsMonitored` trait for automatic tracking

### Data Flow
```
Job Dispatch → Queue Worker → Monitor Tracking → Database Storage → Admin Dashboard
```

## Production Considerations

### Performance
- Database indexing for large datasets
- Automatic cleanup of old records
- Memory-efficient job processing
- Optimized queries for dashboard

### Security
- Admin-only access control
- CSRF protection on all forms
- Secure data handling
- Access logging

### Monitoring
- Failed job alerting
- Performance metrics tracking
- Queue health monitoring
- Resource usage tracking

## Troubleshooting

### Common Issues

**Jobs Not Being Monitored:**
- Verify `IsMonitored` trait is added to job
- Check queue workers are running
- Confirm table structure is correct

**Dashboard Access Issues:**
- Verify admin middleware is working
- Check route configuration
- Clear route and config caches

**Performance Issues:**
- Add database indexes
- Configure cleanup settings
- Monitor memory usage

### Support Resources
- Package documentation: [GitHub](https://github.com/romanzipp/Laravel-Queue-Monitor)
- Laravel queue docs: [Laravel.com](https://laravel.com/docs/queues)
- Custom implementation details in deployment guides

## Maintenance

### Daily Tasks
- Monitor failed job rates
- Check queue worker health
- Review performance metrics

### Weekly Tasks
- Analyze job trends
- Review error patterns
- Capacity planning

### Monthly Tasks
- Database optimization
- Security audits
- Performance reviews

## Getting Help

1. **Check Documentation**: Start with the setup checklist
2. **Review Logs**: Check Laravel logs for errors
3. **Verify Configuration**: Ensure all settings are correct
4. **Test Components**: Use provided verification commands

## File Locations

```
docs/
├── QUEUE_MONITOR_README.md              # This file
├── QUEUE_MONITOR_SETUP_CHECKLIST.md    # Quick setup checklist
├── DEPLOYMENT_GUIDE_QUEUE_MONITOR.md   # Detailed deployment guide
└── DEPLOYMENT_GUIDE.md                 # Main deployment guide

app/
├── Http/Controllers/Admin/QueueMonitorController.php
└── Jobs/RepriceAllJob.php (with IsMonitored trait)

resources/views/admin/queue-monitor/
├── dashboard.blade.php
├── index.blade.php
└── show.blade.php

config/
└── queue-monitor.php
```

## Version Information

- **Laravel Queue Monitor Package**: Latest stable version
- **Custom Dashboard**: v1.0 (integrated with ClickItFame admin)
- **Database Schema**: Compatible with package v4.x+
- **Laravel Version**: 8.x+ compatible

---

**Note**: This documentation is specific to the ClickItFame application implementation. For general package documentation, refer to the official package repository.
