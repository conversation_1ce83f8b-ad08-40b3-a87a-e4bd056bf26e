# ClickItFame Global Constants

This document describes the global constants system used throughout the ClickItFame application.

## 📁 Available Constants Classes

All constants are located in `app/Constants/` directory.

### 🎯 Campaign Related
- **`CampaignType`** - Campaign types (Boost Me, Brand Partnership, etc.)
- **`CampaignStatus`** - Campaign statuses (Active, Completed, Cancelled, etc.)

### 👥 User Related  
- **`UserType`** - User types (Admin, User, Influencer, Brand)

### 📱 Social Media
- **`PostType`** - Instagram post types (Story, Reel, Post/Feed)

### 💰 Payment Related
- **`PaymentStatus`** - Payment statuses (Pending, Completed, Failed, etc.)

## 🔧 Basic Usage Examples

### Simple Usage
```php
use App\Constants\CampaignType;
use App\Constants\UserType;
use App\Constants\PostType;

// Use constants directly
$campaignType = CampaignType::BOOST_ME;
$userType = UserType::INFLUENCER;
$postType = PostType::STORY;
```

### In Models
```php
// In Campaign model
public function scopeBoostMe($query)
{
    return $query->where('type', CampaignType::BOOST_ME);
}

// In User model  
public function isInfluencer(): bool
{
    return $this->user_type === UserType::INFLUENCER;
}
```

### In Controllers
```php
use App\Constants\CampaignStatus;

public function index()
{
    $activeCampaigns = Campaign::whereIn('status', CampaignStatus::active())->get();
    $finishedCampaigns = Campaign::whereIn('status', CampaignStatus::finished())->get();
}
```

### In Blade Views
```php
@if($campaign->type === App\Constants\CampaignType::BOOST_ME)
    <span class="badge badge-primary">{{ App\Constants\CampaignType::BOOST_ME }}</span>
@endif
```

### In Validation Rules
```php
use App\Constants\CampaignType;
use App\Constants\CampaignStatus;
use App\Constants\UserType;
use App\Constants\PostType;
use App\Constants\PaymentStatus;

public function rules()
{
    return [
        // ✅ Good: Using constant arrays for validation
        'campaign_type' => ['required', Rule::in(CampaignType::all())],
        'campaign_status' => ['required', Rule::in(CampaignStatus::all())],
        'user_type' => ['required', Rule::in(UserType::all())],
        'post_type' => ['required', Rule::in(PostType::all())],
        'payment_status' => ['required', Rule::in(PaymentStatus::all())],
        
        // ❌ Bad: Hardcoded validation arrays
        // 'campaign_type' => ['required', Rule::in(['Boost Me', 'Brand Partnership'])],
    ];
}
```

### In Database Seeders
```php
use App\Constants\CampaignType;
use App\Constants\CampaignStatus;
use App\Constants\UserType;

Campaign::create([
    'name' => 'Summer Campaign',
    'type' => CampaignType::BRAND_PARTNERSHIP,
    'status' => CampaignStatus::ACTIVE,
]);

User::create([
    'name' => 'John Doe',
    'user_type' => UserType::INFLUENCER,
]);
```

## 🎨 Helper Methods

Each constants class includes helpful methods:

- **`all()`** - Get all constants as array
- **`isValid($value)`** - Validate if value is a valid constant
- **`displayNames()`** - Get human-readable names (where applicable)
- **`options()`** - Get key-value pairs for dropdowns

### Helper Methods Examples
```php
// Get all campaign types
$allTypes = CampaignType::all();
// Returns: ['Boost Me', 'Brand Partnership', 'Product Review', ...]

// Validate a campaign type
$isValid = CampaignType::isValid('Boost Me'); // true
$isValid = CampaignType::isValid('Invalid Type'); // false

// Get display names for UI
$displayNames = UserType::displayNames();
// Returns: ['admin' => 'Administrator', 'user' => 'Regular User', ...]

// Get options for dropdowns
$options = CampaignType::options();
// Returns: ['BOOST_ME' => 'Boost Me', 'BRAND_PARTNERSHIP' => 'Brand Partnership', ...]
```

## 🚀 Advanced Usage Examples

### Database Queries with Constants
```php
use App\Constants\CampaignType;
use App\Constants\CampaignStatus;
use App\Constants\UserType;

// ✅ Good: Using constants in queries
$influencers = User::where('user_type', UserType::INFLUENCER)
    ->whereHas('campaigns', function ($query) {
        $query->where('type', CampaignType::BOOST_ME)
              ->whereIn('status', CampaignStatus::active());
    })
    ->get();

// Get campaigns by multiple types
$brandCampaigns = Campaign::whereIn('type', [
    CampaignType::BRAND_PARTNERSHIP,
    CampaignType::SPONSORED_CONTENT,
])->get();
```

### Conditional Logic with Constants
```php
use App\Constants\CampaignType;
use App\Constants\UserType;
use App\Constants\CampaignStatus;

// ✅ Good: Using constants in conditions
if ($campaign->type === CampaignType::BOOST_ME) {
    // Handle Boost Me campaign logic
    $this->handleBoostMeCampaign($campaign);
}

if ($user->user_type === UserType::INFLUENCER) {
    // Handle influencer-specific logic
    $this->handleInfluencerUser($user);
}

// Using helper methods from constants
if (CampaignStatus::isValid($campaign->status)) {
    // Process valid campaign status
}

// Check if campaign is in active state
if (in_array($campaign->status, CampaignStatus::active())) {
    // Handle active campaign
}
```

### Switch Statements with Constants
```php
use App\Constants\CampaignType;

switch ($campaignType) {
    case CampaignType::BOOST_ME:
        return $this->handleBoostMe();

    case CampaignType::BRAND_PARTNERSHIP:
        return $this->handleBrandPartnership();

    case CampaignType::PRODUCT_REVIEW:
        return $this->handleProductReview();

    default:
        throw new \InvalidArgumentException("Unknown campaign type: {$campaignType}");
}
```

### API Responses with Constants
```php
use App\Constants\CampaignType;
use App\Constants\CampaignStatus;
use App\Constants\UserType;
use App\Constants\PostType;

public function getConstantsForFrontend()
{
    return response()->json([
        'available_campaign_types' => CampaignType::all(),
        'campaign_type_options' => CampaignType::options(),
        'campaign_statuses' => CampaignStatus::displayInfo(),
        'user_types' => UserType::displayNames(),
        'post_types_with_analytics' => PostType::withReachAnalytics(),
    ]);
}
```

### Eloquent Relationships with Constants
```php
use App\Constants\CampaignType;
use App\Constants\UserType;
use App\Constants\CampaignStatus;

// Get all boost me campaigns with their influencers
$campaigns = Campaign::where('type', CampaignType::BOOST_ME)
    ->with(['user' => function ($query) {
        $query->where('user_type', UserType::INFLUENCER);
    }])
    ->whereIn('status', CampaignStatus::active())
    ->get();
```

### Form Options for Blade Views
```php
// In Controller
public function create()
{
    return view('campaigns.create', [
        'campaign_types' => CampaignType::options(),
        'campaign_statuses' => CampaignStatus::displayInfo(),
        'user_types' => UserType::displayNames(),
        'post_types' => PostType::displayNames(),
    ]);
}
```

```blade
{{-- In Blade View --}}
<select name="campaign_type" class="form-control">
    @foreach($campaign_types as $key => $value)
        <option value="{{ $value }}">{{ $value }}</option>
    @endforeach
</select>
```

## 📝 Adding New Constants

When you need to add new constants to the application:

1. **Create a new PHP class** in `app/Constants/`
2. **Use the namespace** `App\Constants`
3. **Add comprehensive PHPDoc comments** explaining the purpose
4. **Include helper methods** like `all()` and `isValid()`
5. **Update this documentation** with the new constants class

### Example New Constants Class
```php
<?php

declare(strict_types=1);

namespace App\Constants;

/**
 * Notification Type Constants
 *
 * @package App\Constants
 */
class NotificationType
{
    const EMAIL = 'email';
    const SMS = 'sms';
    const PUSH = 'push';

    public static function all(): array
    {
        return [self::EMAIL, self::SMS, self::PUSH];
    }

    public static function isValid(string $type): bool
    {
        return in_array($type, self::all(), true);
    }
}
```

## 🔄 Migration from Hardcoded Values

When replacing hardcoded strings with constants throughout the codebase:

1. **Find and replace** hardcoded values with constants
2. **Update database seeders** to use constants
3. **Update validation rules** to use constant arrays
4. **Update tests** to use constants instead of hardcoded strings
5. **Update Blade views** to use constants
6. **Update API responses** to use constants

### Migration Checklist
- [ ] Controllers updated to use constants
- [ ] Models updated to use constants in scopes and methods
- [ ] Validation rules updated to use constant arrays
- [ ] Database seeders updated
- [ ] Blade views updated
- [ ] API responses updated
- [ ] Tests updated to use constants

## 🧪 Testing Constants

Constants can be easily tested to ensure they work correctly:

```php
use App\Constants\CampaignType;
use App\Constants\UserType;

public function test_campaign_type_constants()
{
    // Test validation
    $this->assertTrue(CampaignType::isValid(CampaignType::BOOST_ME));
    $this->assertFalse(CampaignType::isValid('invalid_type'));

    // Test array contains expected values
    $this->assertContains(CampaignType::BOOST_ME, CampaignType::all());
    $this->assertContains(CampaignType::BRAND_PARTNERSHIP, CampaignType::all());

    // Test helper methods
    $this->assertIsArray(CampaignType::all());
    $this->assertIsArray(CampaignType::options());
}

public function test_user_type_constants()
{
    $this->assertTrue(UserType::isValid(UserType::INFLUENCER));
    $this->assertArrayHasKey(UserType::ADMIN, UserType::displayNames());
}
```

## 🎯 Benefits of Using Constants

- ✅ **Centralized management** - Change once, update everywhere
- ✅ **IDE autocompletion** - No more typos in string values
- ✅ **Type safety** - Constants are checked at compile time
- ✅ **Easy refactoring** - Rename constants safely across the entire app
- ✅ **Consistent naming** - Standardized values across the application
- ✅ **Self-documenting code** - Clear, readable constant names
- ✅ **Validation helpers** - Built-in validation methods
- ✅ **Better maintainability** - Easier to update and maintain code

## 🚫 What NOT to Do

### ❌ Bad Practices
```php
// Don't use hardcoded strings
$campaigns = Campaign::where('type', 'Boost Me')->get();
$users = User::where('user_type', 'influencer')->get();

// Don't use magic numbers or unclear values
if ($status === 1) { /* unclear what 1 means */ }

// Don't duplicate validation arrays
'type' => ['required', Rule::in(['Boost Me', 'Brand Partnership'])], // duplicated elsewhere
```

### ✅ Good Practices
```php
// Use constants for clarity and maintainability
$campaigns = Campaign::where('type', CampaignType::BOOST_ME)->get();
$users = User::where('user_type', UserType::INFLUENCER)->get();

// Use descriptive constants
if ($status === CampaignStatus::ACTIVE) { /* clear meaning */ }

// Use constant arrays for validation
'type' => ['required', Rule::in(CampaignType::all())], // centralized and reusable
```

## 📍 Constants Location

All constants are located in:
```
app/Constants/
├── CampaignType.php
├── CampaignStatus.php
├── UserType.php
├── PostType.php
└── PaymentStatus.php
```

Each file follows the same pattern with helper methods and comprehensive documentation.
