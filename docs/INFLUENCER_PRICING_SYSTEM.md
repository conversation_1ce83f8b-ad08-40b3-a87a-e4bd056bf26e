# Influencer Pricing System Documentation

## Overview

The Influencer Pricing System is a dynamic, performance-based pricing engine that automatically calculates influencer campaign prices based on real reach data, follower counts, and gamification metrics. The system uses 60-day average reach data with outlier trimming to provide accurate, data-driven pricing.

## Table of Contents
- [Requirements](#requirements)
  - [Goal](#goal)
  - [Conventions](#conventions)
  - [Data Model & Configuration](#data-model--configuration)
  - [Average Reach Computation](#average-reach-computation)
  - [Pricing Engine](#pricing-engine)
  - [Admin: Reprice All](#admin-reprice-all)
  - [Marketplace Visibility](#marketplace-visibility)
  - [Routes](#routes)
  - [Seeders](#seeders)
  - [Tests](#tests)
  - [Deliverables](#deliverables)
  - [Output Format](#output-format)

- [Implementation](#implementation)
  - [Database Schema](#database-schema)
  - [Pricing Formula](#pricing-formula)
  - [Tier-Based Pricing](#tier-based-pricing)
  - [Core Components](#core-components)
  - [Campaign Type Mapping](#campaign-type-mapping)
  - [Average Reach Calculation](#average-reach-calculation)
  - [Marketplace Visibility](#marketplace-visibility)
  - [Admin Features](#admin-features)
  - [Automation & Scheduling](#automation--scheduling)
  - [API Usage](#api-usage)
  - [Testing](#testing)
  - [Troubleshooting](#troubleshooting)

# Requirements

## Goal
Implement an influencer pricing system for a marketing app with:
- Follower-tier CPT + estimated reach rates (Story/Reel).
- Average reach computation from the last 60 days (trim outliers).
- Pricing calculation with gamification and reach adjustment.
- Admin “Reprice all” pipeline.
- Marketplace visibility hiding when insights are missing, with admin override.
- Tests, routes, seeders, commands, jobs, services, and small admin UI hooks.

## Conventions
- PHP 8.2+, Laravel 10.
- PSR-4 namespacing; place services in app/Services, jobs in app/Jobs, console in app/Console/Commands, etc.
- Use Eloquent and Query Builder only; no external packages.
- Use strict types and typed properties where possible.
- Write unit/feature tests with PHPUnit + Laravel test helpers.
- Provide small, focused commits with messages.

## Data model & configuration
A. Migrations
1. Modify `influencers` table (or create `influencer_insights` if cleaner—choose one, but keep coupling simple).
   - ig_story_avg_reach INT NULL
   - ig_reel_avg_reach INT NULL
   - ig_feed_avg_reach INT NULL
   - flag_story_insight_missing TINYINT(1) DEFAULT 0
   - flag_reel_insight_missing  TINYINT(1) DEFAULT 0
   - flag_feed_insight_missing  TINYINT(1) DEFAULT 0
   - admin_override_show_hidden TINYINT(1) DEFAULT 0
   - avg_reach_computed_at DATETIME NULL

2. `influencer_prices` table:
   - id, influencer_id FK
   - campaign_type VARCHAR(64)   // e.g., "Boost Me", "Reaction Video", "Survey"
   - post_type VARCHAR(16)       // "story" | "reel" | "feed"
   - price DECIMAL(10,2)
   - breakdown JSON               // CPT tier, rank %, avg reach, estimated reach, multipliers, timestamps
   - priced_at DATETIME
   - indexes on influencer_id + campaign_type

B. Config class
- app/Config/PricingConfig.php (a simple class, not a Laravel config file) exposing:

  public static function storyTiers(): array {
    return [
      ["min"=>1000,"max"=>10000,"rate"=>0.06,"cpt"=>5.00],
      ["min"=>10000,"max"=>50000,"rate"=>0.05,"cpt"=>4.50],
      ["min"=>50000,"max"=>500000,"rate"=>0.04,"cpt"=>3.00],
      ["min"=>500000,"max"=>1000000,"rate"=>0.03,"cpt"=>2.50],
      ["min"=>1000000,"max"=>null,"rate"=>0.02,"cpt"=>2.00],
    ];
  }

  public static function reelTiers(): array {
    return [
      ["min"=>1000,"max"=>10000,"rate"=>0.11,"cpt"=>14.00],
      ["min"=>10000,"max"=>50000,"rate"=>0.10,"cpt"=>13.00],
      ["min"=>50000,"max"=>500000,"rate"=>0.09,"cpt"=>12.00],
      ["min"=>500000,"max"=>1000000,"rate"=>0.08,"cpt"=>11.00],
      ["min"=>1000000,"max"=>null,"rate"=>0.07,"cpt"=>10.00],
    ];
  }

C. Helper
- app/Support/CampaignPostType.php with:
  - static function forCampaignType(string $campaignType): string
    - "Reaction Video" => "reel"
    - "Survey" => "story"
    - "Boost Me" => "story"
  - throw for unknown types.

## Average reach computation
A. Job: app/Jobs/ComputeAverageReachJob.php
- Input: Influencer model (id).
- For the last 60 days, gather IG posts per type (stories, reels, feed). Assume a table `ig_posts` with columns:
  - influencer_id, post_type ("story"|"reel"|"feed"), reach INT, posted_at DATETIME.
- Thresholds:
  - stories: require >=15, otherwise set flag_story_insight_missing=1.
  - reels:   require >=5,  otherwise set flag_reel_insight_missing=1.
  - feed:    require >=5,  otherwise set flag_feed_insight_missing=1.
- If threshold met:
  - Sort by reach; drop top 20% and bottom 20%; average remaining; store in corresponding avg field.
  - Clear the flag for that post type.
- Set avg_reach_computed_at = now().

B. Scheduled command: app/Console/Commands/ClearInsightFlagsCommand.php
- Runs every 4 hours.
- Finds influencers with any *_insight_missing = 1.
- Dispatch ComputeAverageReachJob for each.

C. Kernel schedule
- Add `->cron('0 */4 * * *)` for the command.
- (Optional) Weekly schedule to recompute for all influencers.

## Pricing engine
A. Service: app/Services/PricingCalculator.php
- public function calculatePrice(Influencer $inf, string $campaignType): array
  Steps:
  1) Determine post type via CampaignPostType.
  2) Select tier from PricingConfig based on $inf->followers (min/max; max null means unbounded).
  3) Base price = tier.cpt * ($inf->followers / 1000).
  4) Gamification multiplier:
     - Assume $inf->gamification_percentage decimal (0.10 = +10%).
     - base *= (1 + $inf->gamification_percentage).
  5) Reach adjustment:
     - estimated = $inf->followers * tier.rate
     - avg = pick $inf->ig_story_avg_reach / ig_reel_avg_reach / ig_feed_avg_reach based on post type
     - If avg == 0 OR corresponding flag is set => skip adjustment.
     - Else base *= (avg / estimated).
  6) Return [
        'price' => round(base, 2),
        'breakdown' => [
          'followers' => ...,
          'tier' => [...],
          'gamification_percentage' => ...,
          'estimated_reach' => ...,
          'avg_reach' => ...,
          'reach_multiplier' => (avg>0 ? avg/estimated : null),
          'post_type' => ...,
          'campaign_type' => ...
        ]
     ]

## Admin: Reprice all
A. Controller: app/Http/Controllers/Admin/RepriceController.php
- Route POST /admin/reprice-all
- Dispatch a queue pipeline job `RepriceAllJob`.

B. Job: app/Jobs/RepriceAllJob.php
- Chunks influencers (e.g., 500).
- For each chunk:
  1) Dispatch ComputeAverageReachJob (sync or chained).
  2) For each campaign type: ["Boost Me","Reaction Video","Survey"]
     - Use PricingCalculator to compute.
     - Upsert into influencer_prices (by influencer_id + campaign_type).
     - Persist breakdown + priced_at.

- Log progress with info-level logs and catch/report failures.

C. Simple Blade admin button (place in an existing admin page):
- POST form with CSRF to /admin/reprice-all.

## Marketplace visibility
- Query scope on Influencer: scopeVisibleForCampaign($query, string $campaignType)
  - If admin_override_show_hidden=1 => do not filter.
  - Else: hide influencers where the corresponding *_insight_missing flag for that post type is 1.

- Controller method to toggle admin_override_show_hidden.

## Routes
- routes/web.php (behind admin middleware):
  - Route::post('/admin/reprice-all', [RepriceController::class, 'repriceAll'])->name('admin.reprice.all');
  - Route::post('/admin/influencers/{influencer}/override-visibility', [AdminInfluencerController::class, 'toggleOverride'])->name('admin.influencers.override');

## Seeders
- DatabaseSeeder to create (optional but helpful):
  - A few influencers (multiple follower bands, different gamification %).
  - 60 days of ig_posts per influencer with random reaches to exercise the trimming.
  - One admin user (if your app uses auth scaffolding).

## Tests
- tests/Unit/PricingCalculatorTest.php
  - normal calc
  - missing-insight => skip reach adj
  - avg=0 => skip reach adj
  - higher/lower avg => price scales up/down
  - boundary follower tiers (min, max, null max)

- tests/Feature/ComputeAverageReachJobTest.php
  - thresholds + flags set/cleared
  - outlier trimming (20% top/bottom dropped)
  - averages stored, timestamp set

- tests/Feature/RepriceAllJobTest.php
  - creates/updates influencer_prices with breakdown
  - chunky processing
  - idempotency (same inputs => same outputs)

- tests/Feature/MarketplaceVisibilityTest.php
  - hidden when flag set
  - visible with override
  - visible when flags cleared

## Deliverables
- All PHP files, migrations, seeders, tests, Blade snippet.
- Register command in Kernel.php; register routes.
- Example .env note: QUEUE_CONNECTION=database or redis.
- Run instructions in README section:
  - php artisan migrate --seed
  - php artisan queue:work
  - php artisan schedule:work (for local)
  - Visit admin page to click “Reprice all”.

## Output format
Return:
1) A patch-style file tree showing all new/modified files.
2) Full source code for each new file.
3) SQL of the migrations.
4) Example commit messages, e.g.:
   - chore(db): add insights + prices tables
   - feat(job): compute average reach with outlier trimming
   - feat(service): pricing calculator with gamification + reach adj
   - feat(admin): reprice-all pipeline + UI
   - feat(marketplace): visibility filter with admin override
   - test: unit/feature tests for pricing, jobs, visibility
5) A short README snippet with setup/run steps.

Assume the app already has basic Influencer & Post models; if not present, create minimal models/factories for tests.
Use strict_types=1 and add PHPDoc where helpful.



# Implementation

## Database Schema

### New Tables

#### `influencers` Table
```sql
CREATE TABLE influencers (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    followers INT UNSIGNED NOT NULL,
    gamification_percentage DECIMAL(5,2) DEFAULT 0,
    ig_story_avg_reach INT UNSIGNED NULL,
    ig_reel_avg_reach INT UNSIGNED NULL,
    ig_feed_avg_reach INT UNSIGNED NULL,
    flag_story_insight_missing BOOLEAN DEFAULT FALSE,
    flag_reel_insight_missing BOOLEAN DEFAULT FALSE,
    flag_feed_insight_missing BOOLEAN DEFAULT FALSE,
    admin_override_show_hidden BOOLEAN DEFAULT FALSE,
    avg_reach_computed_at TIMESTAMP NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

#### `ig_posts` Table
```sql
CREATE TABLE ig_posts (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    influencer_id BIGINT UNSIGNED NOT NULL,
    post_type VARCHAR(16) NOT NULL, -- 'story'|'reel'|'feed'
    reach INT UNSIGNED NOT NULL,
    posted_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    FOREIGN KEY (influencer_id) REFERENCES influencers(id) ON DELETE CASCADE
);
```

#### `influencer_prices` Table
```sql
CREATE TABLE influencer_prices (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    influencer_id BIGINT UNSIGNED NOT NULL,
    campaign_type VARCHAR(64) NOT NULL,
    post_type VARCHAR(16) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    breakdown JSON NOT NULL,
    priced_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    FOREIGN KEY (influencer_id) REFERENCES influencers(id) ON DELETE CASCADE,
    UNIQUE KEY unique_influencer_campaign (influencer_id, campaign_type)
);
```

## Pricing Formula

The pricing calculation follows this formula:

```
1. Base Price = CPT × (Followers ÷ 1000)
2. Gamification Bonus = Base × (1 + gamification_percentage)
3. Reach Adjustment = Base × (Average Reach ÷ Estimated Reach)
4. Final Price = Base with all adjustments applied
```

### Example Calculation
```php
// Influencer with 20K followers, 10% gamification, 1000 avg story reach
// Using story tier: CPT = $4.50, Rate = 5%

$base = 4.50 * (20000 / 1000) = $90
$withGamification = 90 * (1 + 0.10) = $99
$estimatedReach = 20000 * 0.05 = 1000
$reachMultiplier = 1000 / 1000 = 1.0
$finalPrice = 99 * 1.0 = $99.00
```

## Tier-Based Pricing

### Story Pricing Tiers
| Followers | CPT | Estimated Reach Rate |
|-----------|-----|---------------------|
| 1K - 10K | $5.00 | 6% |
| 10K - 50K | $4.50 | 5% |
| 50K - 500K | $3.00 | 4% |
| 500K - 1M | $2.50 | 3% |
| 1M+ | $2.00 | 2% |

### Reel Pricing Tiers
| Followers | CPT | Estimated Reach Rate |
|-----------|-----|---------------------|
| 1K - 10K | $14.00 | 11% |
| 10K - 50K | $13.00 | 10% |
| 50K - 500K | $12.00 | 9% |
| 500K - 1M | $11.00 | 8% |
| 1M+ | $10.00 | 7% |

## Core Components

### Configuration Classes

#### `App\Config\PricingConfig`
Defines pricing tiers and rates for different post types.

```php
PricingConfig::storyTiers(); // Returns story pricing tiers
PricingConfig::reelTiers();  // Returns reel pricing tiers
```

#### `App\Support\CampaignPostType`
Maps campaign types to post types.

```php
CampaignPostType::forCampaignType('Boost Me'); // Returns 'story'
CampaignPostType::forCampaignType('Reaction Video'); // Returns 'reel'
```

### Services

#### `App\Services\PricingCalculator`
Core pricing calculation service.

```php
$calculator = new PricingCalculator();
$result = $calculator->calculatePrice($influencer, 'Boost Me');
// Returns: ['price' => 99.00, 'breakdown' => [...]]
```

### Jobs

#### `App\Jobs\ComputeAverageReachJob`
Calculates 60-day average reach with outlier trimming.

```php
ComputeAverageReachJob::dispatch($influencerId);
```

#### `App\Jobs\RepriceAllJob`
Bulk repricing for all influencers.

```php
RepriceAllJob::dispatch();
```

### Commands

#### `App\Console\Commands\ClearInsightFlagsCommand`
Scheduled command that runs every 4 hours to recompute missing insights.

```bash
php artisan influencers:clear-insight-flags
```

### Controllers
#### RepriceController - Admin reprice-all functionality
`app/Http/Controllers/Admin/RepriceController.php`

#### AdminInfluencerController - Toggle visibility override
`app/Http/Controllers/Admin/AdminInfluencerController.php`

## Campaign Type Mapping

| Campaign Type | Post Type | Pricing Tier Used |
|---------------|-----------|-------------------|
| Boost Me | story | Story Tiers |
| Survey | story | Story Tiers |
| Reaction Video | reel | Reel Tiers |

## Average Reach Calculation

### Data Requirements
- **Stories:** Minimum 15 posts in last 60 days
- **Reels:** Minimum 5 posts in last 60 days
- **Feed Posts:** Minimum 5 posts in last 60 days

### Outlier Trimming Process
1. Collect all reach values for post type in last 60 days
2. Sort values in ascending order
3. Remove top 20% (highest values)
4. Remove bottom 20% (lowest values)
5. Calculate average of remaining 60%
6. Store result and clear missing insight flag

### Missing Data Handling
- If insufficient posts: Set `flag_*_insight_missing = true`
- If flag is set: Skip reach adjustment in pricing
- Admin can override visibility with `admin_override_show_hidden = true`

## Marketplace Visibility

### Visibility Rules
Influencers are hidden from marketplace when:
- `flag_story_insight_missing = true` (for story campaigns)
- `flag_reel_insight_missing = true` (for reel campaigns)
- `flag_feed_insight_missing = true` (for feed campaigns)

### Query Scope Usage
```php
// Get visible influencers for a campaign type
$visibleInfluencers = Influencer::visibleForCampaign('Boost Me')->get();

// Admin override shows all influencers
$influencer->admin_override_show_hidden = true;
$influencer->save();
```

## Admin Features

### Reprice All Functionality
- **Location:** Admin → Manage Influencers page
- **Action:** Dispatches `RepriceAllJob` for bulk repricing
- **Processing:** 500 influencers per chunk
- **Route:** `POST /admin/reprice-all`

### Visibility Override
- **Purpose:** Show/hide influencers missing insights
- **Route:** `POST /admin/influencers/{influencer}/override-visibility`
- **Effect:** Toggles `admin_override_show_hidden` flag

## Automation & Scheduling

### Scheduled Tasks
```php
// In app/Console/Kernel.php
$schedule->command('influencers:clear-insight-flags')->cron('0 */4 * * *');
```

### Background Processing
- All reach calculations run in background jobs
- Bulk repricing processes in chunks to avoid timeouts
- Error handling with comprehensive logging

## API Usage

### Calculate Price for Influencer
```php
use App\Services\PricingCalculator;

$calculator = new PricingCalculator();
$result = $calculator->calculatePrice($influencer, 'Boost Me');

echo $result['price']; // Final price
print_r($result['breakdown']); // Detailed breakdown
```

### Get Influencer Price
```php
$price = InfluencerPrice::where('influencer_id', $influencerId)
                       ->where('campaign_type', 'Boost Me')
                       ->first();
```

### Trigger Reach Calculation
```php
ComputeAverageReachJob::dispatch($influencerId);
```

## Testing

### Test Coverage
- **Unit Tests:** `PricingCalculatorTest`
- **Feature Tests:** `ComputeAverageReachJobTest`, `RepriceAllJobTest`, `MarketplaceVisibilityTest`

### Running Tests
```bash
php artisan test --filter="PricingCalculatorTest|ComputeAverageReachJobTest|RepriceAllJobTest|MarketplaceVisibilityTest"
```

### Test Scenarios Covered
- ✅ Normal pricing calculation
- ✅ Missing insights handling
- ✅ Zero average reach handling
- ✅ Higher/lower reach adjustments
- ✅ Follower tier boundaries
- ✅ Outlier trimming accuracy
- ✅ Marketplace visibility rules
- ✅ Bulk repricing functionality

## Troubleshooting

### Common Issues

#### Prices Not Updating
1. Check if reach calculation job ran: `avg_reach_computed_at` timestamp
2. Verify insight flags: `flag_*_insight_missing` should be false
3. Run manual repricing: `RepriceAllJob::dispatch()`

#### Influencers Hidden from Marketplace
1. Check insight flags: `flag_*_insight_missing`
2. Verify sufficient post data in `ig_posts` table
3. Use admin override: Set `admin_override_show_hidden = true`

#### Reach Calculation Not Running
1. Check queue worker is running: `php artisan queue:work`
2. Verify scheduled command: `php artisan schedule:list`
3. Check logs for job failures

### Debug Commands
```bash
# Check migration status
php artisan migrate:status

# View scheduled commands
php artisan schedule:list

# Check queue status
php artisan queue:work --once

# Manual reach calculation
php artisan tinker
ComputeAverageReachJob::dispatch(1);
```

### Performance Optimization
- Ensure database indexes on `influencer_id` and `campaign_type`
- Use chunked processing for large datasets
- Monitor queue performance and scale workers as needed
- Consider caching frequently accessed pricing data

## Testing

### Phase 1: Automated Tests (Run First)

#### Unit & Feature Tests
```bash
# Run all pricing system tests
php artisan test --filter="PricingCalculatorTest|ComputeAverageReachJobTest|RepriceAllJobTest|MarketplaceVisibilityTest|SocialConnectTest"

# Run with detailed output
php artisan test --filter="PricingCalculatorTest|ComputeAverageReachJobTest|RepriceAllJobTest|MarketplaceVisibilityTest|SocialConnectTest" --verbose
```

**Expected Result:** All tests should pass (10+ tests)

#### Database Migration Test
```bash
# Check all migrations are applied
php artisan migrate:status

# Verify table structures exist
php artisan tinker --execute="
echo 'Checking required tables:';
echo 'influencers: ' . (Schema::hasTable('influencers') ? 'EXISTS' : 'MISSING');
echo 'ig_posts: ' . (Schema::hasTable('ig_posts') ? 'EXISTS' : 'MISSING');
echo 'influencer_prices: ' . (Schema::hasTable('influencer_prices') ? 'EXISTS' : 'MISSING');
echo 'social_connects token type: ' . Schema::getColumnType('social_connects', 'token');
"
```

### Phase 2: Semi-Automated Setup Tests

#### Create Test Data
```bash
php artisan tinker --execute="
// Create test influencer
\$user = App\Models\User::create([
    'first_name' => 'Test',
    'last_name' => 'Influencer',
    'email' => '<EMAIL>',
    'password' => bcrypt('password'),
    'user_type' => 'influencer'
]);

\$influencer = App\Models\Influencer::create([
    'followers' => 25000,
    'gamification_percentage' => 0.15
]);

echo 'Created test influencer ID: ' . \$influencer->id;
"
```

#### Create Sample IG Posts Data
```bash
php artisan tinker --execute="
// Create sample posts for reach calculation (use the influencer ID from above)
\$influencerId = 1; // Replace with actual ID

// Create 20 story posts with varied reach
for (\$i = 0; \$i < 20; \$i++) {
    App\Models\IgPost::create([
        'influencer_id' => \$influencerId,
        'post_type' => 'story',
        'reach' => rand(800, 1500),
        'posted_at' => now()->subDays(rand(1, 60))
    ]);
}

// Create 10 reel posts with varied reach
for (\$i = 0; \$i < 10; \$i++) {
    App\Models\IgPost::create([
        'influencer_id' => \$influencerId,
        'post_type' => 'reel',
        'reach' => rand(2000, 4000),
        'posted_at' => now()->subDays(rand(1, 60))
    ]);
}

echo 'Created sample IG posts for testing';
"
```

#### Test Reach Calculation Job
```bash
php artisan tinker --execute="
// Test reach calculation
\$job = new App\Jobs\ComputeAverageReachJob(1); // Use your influencer ID
\$job->handle();

// Check results
\$influencer = App\Models\Influencer::find(1);
echo 'Story avg reach: ' . \$influencer->ig_story_avg_reach;
echo 'Reel avg reach: ' . \$influencer->ig_reel_avg_reach;
echo 'Story flag missing: ' . (\$influencer->flag_story_insight_missing ? 'YES' : 'NO');
echo 'Reel flag missing: ' . (\$influencer->flag_reel_insight_missing ? 'YES' : 'NO');
"
```

#### Test Pricing Calculation
```bash
php artisan tinker --execute="
\$calculator = new App\Services\PricingCalculator();
\$influencer = App\Models\Influencer::find(1);

// Test different campaign types
\$campaigns = ['Boost Me', 'Survey', 'Reaction Video'];
foreach (\$campaigns as \$campaign) {
    \$result = \$calculator->calculatePrice(\$influencer, \$campaign);
    echo \$campaign . ' price: $' . \$result['price'];
    echo 'Post type: ' . \$result['breakdown']['post_type'];
}
"
```

### Phase 3: Manual Testing Scenarios

#### Test Case 1: Admin Reprice All Feature

**Steps:**
1. Login as admin user
2. Navigate to Admin → Manage Influencers
3. Look for "Reprice All" button
4. Click the button
5. Check that job was dispatched (check logs or queue)

**Expected Results:**
- Button exists and is clickable
- Success message appears
- Background job processes influencers
- `influencer_prices` table gets populated

**Verification:**
```bash
# Check if prices were created
php artisan tinker --execute="
echo 'Total prices calculated: ' . App\Models\InfluencerPrice::count();
App\Models\InfluencerPrice::with('influencer')->take(5)->get()->each(function(\$price) {
    echo 'Influencer ' . \$price->influencer_id . ' - ' . \$price->campaign_type . ': $' . \$price->price;
});
"
```

#### Test Case 2: Marketplace Visibility

**Steps:**
1. Create influencer with insufficient posts (< 15 stories, < 5 reels)
2. Run reach calculation job
3. Check that insight flags are set to true
4. Query marketplace with `visibleForCampaign` scope
5. Verify influencer is hidden
6. Set admin override to true
7. Verify influencer now appears

**Test Commands:**
```bash
# Create influencer with insufficient data
php artisan tinker --execute="
\$influencer = App\Models\Influencer::create([
    'followers' => 10000,
    'gamification_percentage' => 0.10
]);

// Create only 3 story posts (insufficient)
for (\$i = 0; \$i < 3; \$i++) {
    App\Models\IgPost::create([
        'influencer_id' => \$influencer->id,
        'post_type' => 'story',
        'reach' => rand(400, 800),
        'posted_at' => now()->subDays(rand(1, 60))
    ]);
}

echo 'Created influencer with insufficient data: ' . \$influencer->id;
"

# Run reach calculation
php artisan tinker --execute="
\$job = new App\Jobs\ComputeAverageReachJob(2); // Use new influencer ID
\$job->handle();

\$influencer = App\Models\Influencer::find(2);
echo 'Story flag missing: ' . (\$influencer->flag_story_insight_missing ? 'YES' : 'NO');
"

# Test visibility
php artisan tinker --execute="
// Should be hidden
\$visible = App\Models\Influencer::visibleForCampaign('Boost Me')->where('id', 2)->exists();
echo 'Visible without override: ' . (\$visible ? 'YES' : 'NO');

// Enable override
\$influencer = App\Models\Influencer::find(2);
\$influencer->admin_override_show_hidden = true;
\$influencer->save();

// Should now be visible
\$visible = App\Models\Influencer::visibleForCampaign('Boost Me')->where('id', 2)->exists();
echo 'Visible with override: ' . (\$visible ? 'YES' : 'NO');
"
```

#### Test Case 3: Pricing Accuracy

**Manual Calculation Test:**
1. Take an influencer with known data
2. Calculate price manually using the formula
3. Compare with system calculation

**Example:**
```bash
php artisan tinker --execute="
\$influencer = App\Models\Influencer::find(1);
echo 'Followers: ' . \$influencer->followers;
echo 'Gamification: ' . \$influencer->gamification_percentage;
echo 'Story avg reach: ' . \$influencer->ig_story_avg_reach;

// Manual calculation for 'Boost Me' (story type)
// Tier for 25K followers: CPT = 4.50, Rate = 5%
\$base = 4.50 * (\$influencer->followers / 1000);
\$withGamification = \$base * (1 + \$influencer->gamification_percentage);
\$estimatedReach = \$influencer->followers * 0.05;
\$reachMultiplier = \$influencer->ig_story_avg_reach / \$estimatedReach;
\$finalPrice = \$withGamification * \$reachMultiplier;

echo 'Manual calculation: $' . round(\$finalPrice, 2);

// System calculation
\$calculator = new App\Services\PricingCalculator();
\$result = \$calculator->calculatePrice(\$influencer, 'Boost Me');
echo 'System calculation: $' . \$result['price'];
echo 'Match: ' . (abs(\$finalPrice - \$result['price']) < 0.01 ? 'YES' : 'NO');
"
```

#### Test Case 4: Scheduled Command

**Steps:**
1. Check command is registered
2. Run command manually
3. Verify it processes flagged influencers

**Commands:**
```bash
# Check scheduled commands
php artisan schedule:list

# Run command manually
php artisan influencers:clear-insight-flags

# Check if it processed any influencers
php artisan tinker --execute="
echo 'Influencers with missing story insights: ' . App\Models\Influencer::where('flag_story_insight_missing', true)->count();
echo 'Influencers with missing reel insights: ' . App\Models\Influencer::where('flag_reel_insight_missing', true)->count();
"
```

### Phase 4: Edge Case Testing

#### Test Case 5: Zero/Null Data Handling

```bash
php artisan tinker --execute="
// Test influencer with zero followers
\$influencer = App\Models\Influencer::create([
    'followers' => 0,
    'gamification_percentage' => 0
]);

\$calculator = new App\Services\PricingCalculator();
try {
    \$result = \$calculator->calculatePrice(\$influencer, 'Boost Me');
    echo 'Zero followers price: $' . \$result['price'];
} catch (Exception \$e) {
    echo 'Error with zero followers: ' . \$e->getMessage();
}
"
```

#### Test Case 6: Boundary Testing

```bash
php artisan tinker --execute="
// Test tier boundaries
\$boundaries = [999, 1000, 1001, 9999, 10000, 10001, 49999, 50000, 50001];

foreach (\$boundaries as \$followers) {
    \$influencer = new App\Models\Influencer(['followers' => \$followers, 'gamification_percentage' => 0]);
    \$calculator = new App\Services\PricingCalculator();
    \$result = \$calculator->calculatePrice(\$influencer, 'Boost Me');
    echo \$followers . ' followers -> Tier CPT: $' . \$result['breakdown']['tier']['cpt'];
}
"
```

### Phase 5: Performance Testing

#### Test Case 7: Bulk Processing

```bash
# Create multiple influencers for bulk testing
php artisan tinker --execute="
for (\$i = 0; \$i < 10; \$i++) {
    App\Models\Influencer::create([
        'followers' => rand(5000, 100000),
        'gamification_percentage' => rand(0, 20) / 100
    ]);
}
echo 'Created 10 test influencers';
"

# Test bulk repricing
php artisan tinker --execute="
\$startTime = microtime(true);
App\Jobs\RepriceAllJob::dispatchSync();
\$endTime = microtime(true);
echo 'Bulk repricing took: ' . round(\$endTime - \$startTime, 2) . ' seconds';
echo 'Total prices created: ' . App\Models\InfluencerPrice::count();
"
```

### Testing Checklist

#### Pre-Testing Setup
- [ ] All migrations applied successfully
- [ ] All automated tests pass
- [ ] Queue worker is running (`php artisan queue:work`)
- [ ] Test data created (influencers, ig_posts)

#### Core Functionality
- [ ] Pricing calculation works for all campaign types
- [ ] Reach calculation job processes correctly
- [ ] Outlier trimming removes top/bottom 20%
- [ ] Insight flags set correctly for insufficient data
- [ ] Admin reprice-all button works
- [ ] Marketplace visibility filtering works
- [ ] Admin override shows hidden influencers

#### Edge Cases
- [ ] Zero followers handled gracefully
- [ ] Missing reach data skips adjustment
- [ ] Tier boundaries work correctly
- [ ] Very high/low reach values handled
- [ ] Bulk processing completes without errors

#### Performance
- [ ] Bulk repricing completes in reasonable time
- [ ] No memory issues with large datasets
- [ ] Queue jobs process without timeouts

#### UI/UX
- [ ] Admin buttons are visible and functional
- [ ] Success/error messages display correctly
- [ ] Pricing breakdown data is accurate
- [ ] No JavaScript errors in browser console

### What to Report as Bugs

1. **Any test failures** in automated tests
2. **Incorrect pricing calculations** (compare manual vs system)
3. **Missing or broken admin UI elements**
4. **Jobs that fail or timeout**
5. **Influencers showing when they should be hidden**
6. **Database errors or migration issues**
7. **Performance issues** (>30 seconds for bulk operations)

## Admin Testing Panel

### Overview
The Admin Testing Panel provides a user-friendly interface for testing the influencer pricing system without needing to run command-line tools. It's accessible at `/admin/influencer-testing`.

### Features

#### 1. Influencer List View
- **URL:** `/admin/influencer-testing`
- **Purpose:** View all influencers with their current pricing data
- **Features:**
  - Displays followers, gamification %, reach data, and flags
  - Shows current status of insight flags
  - Quick access to individual testing pages

#### 2. Individual Influencer Testing
- **URL:** `/admin/influencer-testing/{influencer}`
- **Purpose:** Comprehensive testing interface for specific influencer

#### 3. Test Data Generation
**Quick Test Scenarios:**
- **High Performer:** Creates posts with 150% of estimated reach
- **Low Performer:** Creates posts with 50% of estimated reach
- **Mixed Performance:** Alternating high/low performance posts
- **Insufficient Data:** Creates insufficient posts to trigger flags

**Custom Post Generation:**
- Choose post type (story/reel/feed)
- Set number of posts (1-100)
- Define reach range (min/max)
- Specify time distribution (days back)

#### 4. Real-Time Price Calculation
- **Interactive Pricing:** Click "Calculate Price" for any campaign type
- **Detailed Breakdown:** Shows complete calculation steps
- **Visual Results:** Color-coded results with success/error states

#### 5. Data Management
- **Update Influencer Data:** Modify followers and gamification %
- **Recalculate Reach:** Trigger reach calculation manually
- **Clear Test Data:** Remove all generated posts and reset flags

### Usage Instructions

#### For Manual Testing:
1. **Navigate to Testing Panel:**
   - Go to Admin → Manage Influencers
   - Click "Pricing Tests" button
   - Select an influencer to test

2. **Set Up Test Data:**
   - Update influencer followers/gamification if needed
   - Choose a quick scenario OR generate custom posts
   - Click "Recalculate Reach" to process the data

3. **Test Pricing:**
   - Click "Calculate Price" for any campaign type
   - Review the detailed breakdown
   - Verify calculations match expected results

4. **Validate Different Scenarios:**
   - Test high performers (should get higher prices)
   - Test low performers (should get lower prices)
   - Test insufficient data (should skip reach adjustment)

#### For QA Testing:
1. **Boundary Testing:**
   - Test with 0 followers
   - Test with very high follower counts
   - Test tier boundaries (999, 1000, 1001, etc.)

2. **Edge Cases:**
   - Generate posts with 0 reach
   - Test with missing gamification data
   - Test with extreme reach values

3. **Error Handling:**
   - Try invalid campaign types
   - Test with corrupted data
   - Verify error messages are helpful

### Technical Details

#### Controller: `InfluencerTestingController`
- **Location:** `app/Http/Controllers/Admin/InfluencerTestingController.php`
- **Methods:**
  - `index()` - List all influencers
  - `show()` - Individual testing page
  - `generateFakePosts()` - Create test posts
  - `calculatePrice()` - AJAX price calculation
  - `createTestScenario()` - Generate predefined scenarios

#### Routes:
```php
Route::prefix('admin/influencer-testing')->group(function () {
    Route::get('/', 'index')->name('admin.influencer-testing.index');
    Route::get('/{influencer}', 'show')->name('admin.influencer-testing.show');
    Route::post('/{influencer}/generate-posts', 'generateFakePosts');
    Route::post('/{influencer}/calculate-price', 'calculatePrice');
    // ... more routes
});
```

#### Views:
- **Index:** `resources/views/admin/influencer-testing/index.blade.php`
- **Detail:** `resources/views/admin/influencer-testing/show.blade.php`

### Integration with Existing System
- **Seamless Integration:** Uses existing models and services
- **No Data Conflicts:** Test data is clearly marked and can be cleared
- **Real Calculations:** Uses the same `PricingCalculator` service as production
- **Safe Testing:** All operations are reversible

## Maintenance

### Regular Tasks
- Monitor job queue performance
- Review pricing accuracy with business team
- Update tier rates as market conditions change
- Clean up old `ig_posts` data (older than 60 days)

### Data Integrity
- Verify reach calculations match expected patterns
- Monitor for unusual pricing outliers
- Ensure all influencers have recent pricing data
- Validate campaign type mappings remain accurate