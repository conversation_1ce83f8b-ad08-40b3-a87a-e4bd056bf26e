## Summary of Automatic Campaign Cancellation Points
Created on 09.09.2025

I found **5 main console commands** that automatically set the `status` field in `InfluencerRequestDetail` to `Cancelled` or related cancellation statuses. These are all scheduled to run automatically via cron jobs:

### 1. **CancelledTimeoutRequest.php** (Line 77)
- **Command**: `cancelled:request`
- **Schedule**: Runs **hourly**
- **What it does**: Cancels campaigns when influencers don't accept requests within 3 days
- **Code**: 
````php path=app/Console/Commands/CancelledTimeoutRequest.php mode=EXCERPT
InfluencerRequestDetail::where('compaign_id', $row->compaign_id)
    ->where('influencer_detail_id', $row->i_id)
    ->update(['status' => 'Cancelled']);
````

### 2. **CancelledTimeoutInfluencerSubmit.php** (Line 155)
- **Command**: `cancelledinfluencer:submit`
- **Schedule**: Runs **hourly**
- **What it does**: Cancels campaigns when influencers don't submit content during the submit phase
- **Uses**: The centralized `cancelCampaign()` method with reason `'submit_timeout'`

### 3. **TimeoutCustomerPayment.php** (Line 79)
- **Command**: `timeoutcustomer:payment`
- **Schedule**: Runs **hourly**
- **What it does**: Cancels campaigns when customers don't pay within 6 days
- **Uses**: The centralized `cancelCampaign()` method with reason `'cancelled_by_customer'`

### 4. **PaymentRefundExpire.php** (Line 143)
- **Command**: `paymentrefund:expire`
- **Schedule**: Runs **daily**
- **What it does**: Cancels campaigns that have expired based on time limits
- **Uses**: The centralized `cancelCampaign()` method with reason `'time_expired'`

### 5. **Admin Controller Actions**
- **AdminController.php**: Multiple methods that can cancel campaigns:
  - `disputeConfirm()` - cancels when disputes are confirmed
  - `saveCampaign()` - bulk cancellation by admin
  - Uses the centralized `cancelCampaign()` method

## Cron Schedule (from Kernel.php)

````php path=app/Console/Kernel.php mode=EXCERPT
$schedule->command('cancelled:request')->hourly();
$schedule->command('cancelledinfluencer:submit')->hourly();
$schedule->command('cancelledcustomer:request')->hourly();
$schedule->command('timeoutcustomer:payment')->hourly();
$schedule->command('paymentrefund:expire')->daily();
````

## Key Findings

1. **Most frequent**: 4 commands run **hourly** that can cancel campaigns
2. **Direct status update**: Only `CancelledTimeoutRequest.php` directly sets `status = 'Cancelled'`
3. **Centralized method**: The other commands use the `cancelCampaign()` method which sets `payment_status` to `'cancelled'` or `'refunded'` and `refund_reason` to various cancellation reasons
4. **The bug is likely in one of these hourly commands** since they run most frequently

To debug which specific command is causing your issue, I'd recommend:
1. Check the logs for entries from these commands
2. Add more detailed logging to identify which command is triggering the cancellations
3. Look at the `refund_reason` field in your cancelled campaigns to identify the source

