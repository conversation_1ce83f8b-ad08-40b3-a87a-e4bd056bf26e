# InfluencerRequestDetail Price Fields Documentation

This document explains the confusing price-related attributes in the `InfluencerRequestDetail` model and where they are used throughout the codebase.

## Overview

The `InfluencerRequestDetail` model contains **7 different price-related fields** that serve different purposes in the campaign payment workflow:

1. `current_price` - ❌ **COMPLETELY DEPRECATED** (Removed 2025-01-29)
2. `discount_price` - Legacy pricing field (being phased out)
3. `cash_out_amount` - Amount available for influencer cashout
4. `platform_amount` - Platform commission amount
5. `influencer_amount` - Net amount for influencer after commission
6. `total_amount` - Total campaign amount (legacy field)
7. `influencer_price` - New pricing system amount

## Field Details

### 1. current_price ❌ **COMPLETELY DEPRECATED**
**Purpose**: **REMOVED FIELD** - This field has been completely deprecated and removed from the system as of 2025-01-29.

**Database**:
- **Original**: Added in original table creation (2022-11-22)
- **Status**: ❌ **REMOVED from fillable array** - No longer accepts new data
- **Replacement**: `cancellation_penalty` field for penalty tracking

**Migration History**:
- `2022_11_22_175837_create_influencer_request_details_table.php` - Original creation
- `2025_08_29_114241_add_cancellation_penalty_to_influencer_request_details.php` - Added replacement field

**Replacement Logic**:
```php
// OLD (removed):
$this->current_price = max(0, $this->current_price - $priceAdjustmentAmount);

// NEW (implemented):
$this->cancellation_penalty += $priceAdjustmentAmount;
```

**Status**: ✅ **COMPLETELY REMOVED** - All references updated to use `influencer_price` or removed entirely

### 2. discount_price
**Purpose**: **LEGACY FIELD** - Used in older payment calculations and VAT computations. Still actively used in some parts of the system but being phased out in favor of `influencer_price`.

**Database**: Added in original table creation (2022-11-22)
- Type: `string` (nullable)
- Migration: `2022_11_22_175837_create_influencer_request_details_table.php`

**Usage**:
- **Currently used in**:
  - `StripeMarketplaceController::calculateTotalAmountsToCharge()` - Line 92: `$totalAmount += $influencerRequestDetail->discount_price;`
  - `StripeMarketplaceController::processStripeChargesForInfluencers()` - Line 100: VAT calculations
  - `OpenCampaignsController` - Fallback when `influencer_price` is not available
  - `ChargeInfluencers` job - Commission calculations (fallback)
- **Fallback pattern**: `$influencerPrice = $CampaignInfluencer->influencer_price ?? $CampaignInfluencer->discount_price;`

**Code Examples**:
```php
// Active usage in StripeMarketplaceController for total calculations
$totalAmount += $influencerRequestDetail->discount_price;

// VAT calculation (still actively used)
$vatAmount = $user->is_small_business_owner ? 0 : ($influencerRequestDetail->discount_price * 0.19);

// Fallback usage in OpenCampaignsController
$influencerPrice = $CampaignInfluencer->influencer_price ?? $CampaignInfluencer->discount_price;
```

### 3. cash_out_amount
**Purpose**: Tracks the amount available for influencer cashout after campaign completion.

**Database**: Added in payment fields migration (2024-12-10)
- Type: `double` (default: 0)
- Migration: `2024_12_10_085936_add_payment_fields_to_influencer_request_details_table.php`

**Usage**:
- **Set in**: Payment processing workflows (Stripe marketplace controller)
- **Used in**: 
  - `UserController::cashoutInfluencer()` - Calculating available cashout amounts
  - `PayoutController::cashout()` - Processing influencer payouts
- **Calculated**: After campaign completion and commission deduction

**Code Examples**:
```php
// Calculating available cashout amount
$amount = $campaigns->sum('cash_out_amount');
$amount = floor($amount * 100) / 100; // Round down to 2 decimal places

// Used in cashout queries
$inprogressAmount = InfluencerRequestDetail::whereHas('influencerdetails', function ($query) {
    $query->where('user_id', Auth::id());
})->sum('cash_out_amount');
```

### 4. platform_amount
**Purpose**: Stores the platform's commission amount (typically 20% + VAT).

**Database**: Added in payment fields migration (2024-12-10)
- Type: `double` (default: 0)
- Migration: `2024_12_10_085936_add_payment_fields_to_influencer_request_details_table.php`

**Usage**:
- **Set in**: Payment processing during Stripe transactions
- **Used in**: 
  - `ChargeInfluencers` job - Commission collection from influencers
  - Commission calculations with VAT
- **Calculation**: `originalAmount * 0.20 + (commission * 0.19 VAT)`

**Code Examples**:
```php
// Using stored platform_amount in ChargeInfluencers job
if ($influencerRequestDetail->platform_amount > 0) {
    $commissionAmount = (int) round($influencerRequestDetail->platform_amount * 100);
} else {
    // Fallback calculation
    $originalAmountCents = (int) round($influencerRequestDetail->discount_price * 100);
    $commissionAmountCents = (int) round($originalAmountCents * 0.20);
    $commissionVatAmountCents = (int) round($commissionAmountCents * 0.19);
    $commissionAmount = $commissionAmountCents + $commissionVatAmountCents;
}
```

### 5. influencer_amount
**Purpose**: **DUPLICATE OF TOTAL_AMOUNT** - This field appears to hold the same value as `total_amount`. The reason for its creation is unclear and may be redundant.

**Database**: Added in payment fields migration (2024-12-10)
- Type: `double` (default: 0)
- Migration: `2024_12_10_085936_add_payment_fields_to_influencer_request_details_table.php`

**Usage**:
- **Set in**: `StripeMarketplaceController::processStripeChargesForInfluencers()` - Line 758: `'influencer_amount' => $totalAmountFormatted`
- **Value**: Same as `total_amount` - stores the total amount including VAT that customer paid for this influencer
- **Used in**:
  - `UserController::cashoutInfluencer()` - Determining influencer earnings
  - Payment status tracking
- **Note**: **May be redundant** - appears to duplicate `total_amount` functionality

**Code Examples**:
```php
// Set in StripeMarketplaceController (same value as total_amount)
'influencer_amount' => $totalAmountFormatted, // [example] 5.95
'total_amount' => $totalAmountFormatted,      // [example] 5.95 (same value!)

// Used in cashout calculations
$inprogressAmount = InfluencerRequestDetail::whereHas('influencerdetails', function ($query) {
    $query->where('user_id', Auth::id());
})->sum('influencer_amount');
```

### 6. total_amount
**Purpose**: Legacy field that stores the total campaign amount. Mostly unused in current codebase.

**Database**: Added early in development (2022-11-29)
- Type: `string` (nullable)
- Migration: `2022_11_29_154756_add_influencer_request_details_table.php`

**Usage**:
- **Set in**: `UserController::requestNowUpdateConfirm()` - Campaign updates
- **Used in**: Limited usage, mostly legacy code
- **Status**: Being phased out in favor of calculated totals

**Code Examples**:
```php
// Setting total_amount (legacy usage)
$InfluencerRequest->update(['total_amount', $total_amount]);
```

### 7. influencer_price
**Purpose**: New pricing system amount that replaces `discount_price`. Calculated using the modern pricing engine.

**Database**: Added for new pricing system (2022-12-27)
- Type: `string` (nullable)
- Migration: `2022_12_27_162243_add_cols_influencer_request_detail_table.php`

**Usage**:
- **Set in**: 
  - `UserController::requestNow()` - New campaign creation with pricing calculation
  - Modern pricing workflows using `PricingCalculator` service
- **Used in**: 
  - `OpenCampaignsController` - Primary pricing field (with `discount_price` fallback)
  - Modern payment processing
- **Calculation**: Uses `AdminComission` model and reach-based pricing

**Code Examples**:
```php
// Setting influencer_price with commission calculation
$AdminComission = AdminComission::first();
$influencer_price = ($newLivetreamPrice * $AdminComission->influencer) / 100;
if ($influencer_price < 2) {
    $influencer_price = 2;
}
$formData['influencer_price'] = $newLivetreamPrice - $influencer_price;

// Primary usage with fallback
$influencerPrice = $CampaignInfluencer->influencer_price ?? $CampaignInfluencer->discount_price;
```

## Payment Workflow

The typical payment workflow uses these fields in sequence:

1. **Campaign Creation**: `current_price` and `influencer_price` are set
2. **Payment Processing**: `platform_amount` and `influencer_amount` are calculated
3. **Campaign Completion**: `cash_out_amount` becomes available
4. **Cashout**: Influencer receives `cash_out_amount`

## Commission System

The platform uses a configurable commission system via the `AdminComission` model:
- **Customer Commission**: Configurable percentage (default: 10%)
- **Influencer Commission**: Configurable percentage (default: 10%)
- **Platform Fee**: 5% minimum €2
- **VAT**: 19% on commissions and platform fees

## Migration History

| Field | Added | Migration File |
|-------|-------|----------------|
| `current_price` | 2022-11-22 | ❌ **DEPRECATED 2025-01-29** |
| `cancellation_penalty` | 2025-01-29 | `add_cancellation_penalty_to_influencer_request_details.php` |
| `discount_price` | 2022-11-22 | `create_influencer_request_details_table.php` |
| `total_amount` | 2022-11-29 | `add_influencer_request_details_table.php` |
| `influencer_price` | 2022-12-27 | `add_cols_influencer_request_detail_table.php` |
| `cash_out_amount` | 2024-12-10 | `add_payment_fields_to_influencer_request_details_table.php` |
| `platform_amount` | 2024-12-10 | `add_payment_fields_to_influencer_request_details_table.php` |
| `influencer_amount` | 2024-12-10 | `add_payment_fields_to_influencer_request_details_table.php` |

## Detailed Usage by Class/Method

### Controllers

#### `App\Http\Controllers\Frontend\UserController`
- **`requestNow()`**: Sets `current_price` and `influencer_price` during campaign creation
- **`updateCampaignInfluencers()`**: Updates campaign influencer selections using new InfluencerPrice model
- **`requestNowUpdateConfirm()`**: Updates `total_amount` (legacy)
- **`cashoutInfluencer()`**: Reads `influencer_amount` for cashout calculations
- **`getAdminPricing()`**: Uses pricing calculator for `influencer_price` calculation

#### `App\Http\Controllers\Frontend\OpenCampaignsController`
- **Campaign total calculations**: Uses `influencer_price` with `discount_price` fallback
- **VAT calculations**: Processes pricing for payment workflows

#### `App\Http\Controllers\Frontend\StripeMarketplaceController`
- **Payment processing**: Calculates and sets `platform_amount`, `influencer_amount`, `cash_out_amount`
- **VAT handling**: Uses `discount_price` for VAT calculations
- **Commission calculations**: 20% platform commission + 19% VAT

#### `App\Http\Controllers\Frontend\PayoutController`
- **`cashout()`**: Sums `cash_out_amount` for influencer payouts
- **Balance checking**: Validates available funds against `cash_out_amount`

### Jobs

#### `App\Jobs\ChargeInfluencers`
- **Commission collection**: Uses `platform_amount` or calculates from `discount_price`
- **Stripe transfers**: Processes platform commission collection
- **Payment status updates**: Updates payment-related fields after processing

#### `App\Jobs\RepriceInfluencerJob`
- **Price calculation**: Uses `PricingCalculator` to determine `influencer_price`
- **Bulk repricing**: Updates pricing across multiple campaigns

### Models

#### `App\Models\InfluencerRequestDetail`
- **`calculatePriceAdjustment()`**: Uses `current_price` for cancellation adjustments
- **`cancelCampaign()`**: Reduces `current_price` when campaigns are cancelled
- **Accessors**: Provides computed properties based on price fields

#### `App\Models\AdminComission`
- **Commission rates**: Stores configurable commission percentages
- **Used in**: `influencer_price` calculations throughout the system

### Services

#### `App\Services\PricingCalculator`
- **Modern pricing**: Calculates prices that populate `influencer_price`
- **Reach-based pricing**: Uses follower count and engagement metrics
- **Tier-based pricing**: Different rates based on influencer size

#### `App\Services\ReachBasedPriceCalculatorService`
- **Advanced pricing**: Calculates reach-adjusted prices
- **Performance metrics**: Uses social media performance data

### Views/Templates

#### `resources/views/front-user/modals/payment/payment-popup.blade.php`
- **Payment display**: Shows pricing breakdown using multiple price fields
- **Platform fee calculation**: 5% of total amount, minimum €2
- **VAT display**: 19% VAT on platform fees

#### `resources/views/admin/comission/manage.blade.php`
- **Commission management**: Admin interface for setting commission rates
- **Affects**: `influencer_price` calculations system-wide

## Data Flow Diagram

```
Campaign Creation:
current_price (LEGACY - old data)
discount_price (LEGACY - still used for VAT calculations)
influencer_price (MODERN - primary pricing field)

Payment Processing (StripeMarketplaceController):
influencer_price (5.00)
→ + VAT if not small business (0.95)
→ = total_amount (5.95)
→ = influencer_amount (5.95) [DUPLICATE VALUE]
→ - platform_commission (1.19)
→ = cash_out_amount (4.76) [actual payout to influencer]

Platform Commission Calculation:
20% commission on original amount (1.00) + 19% VAT on commission (0.19) = platform_amount (1.19)
```

## Common Patterns

### Price Fallback Pattern
```php
// Used throughout the codebase
$price = $record->influencer_price ?? $record->discount_price;
```

### Commission Calculation Pattern
```php
// Standard 20% commission + 19% VAT
$commissionAmount = $originalAmount * 0.20;
$vatAmount = $commissionAmount * 0.19;
$totalCommission = $commissionAmount + $vatAmount;
$influencerAmount = $originalAmount - $totalCommission;
```

### Platform Fee Pattern
```php
// 5% platform fee, minimum €2
$platformFee = max($totalAmount * 0.05, 2);
$platformVat = $platformFee * 0.19;
```

## Key Insights from Code Analysis

Based on the actual `StripeMarketplaceController.php` code, here are the real relationships:

1. **`current_price`** = ❌ **COMPLETELY DEPRECATED** (Removed 2025-01-29)
8. **`cancellation_penalty`** = Tracks penalty amounts for cancelled campaigns
2. **`discount_price`** = Still used for VAT calculations and total amount calculations
3. **`influencer_price`** = Modern pricing field (e.g., 5.00)
4. **`total_amount`** = Total amount customer pays for this influencer (e.g., 5.95)
5. **`influencer_amount`** = **DUPLICATE** of `total_amount` (both set to 5.95)
6. **`platform_amount`** = Platform commission including VAT (e.g., 1.19)
7. **`cash_out_amount`** = Actual amount influencer receives after commission (e.g., 4.76)

## Recommendations

1. **Investigate `influencer_amount` duplication**: Determine why it duplicates `total_amount` and consider removing redundancy
2. **Phase out legacy fields**: `current_price` appears to be completely legacy and could be deprecated
3. **Standardize on `influencer_price`**: Continue migration from `discount_price` to `influencer_price`
4. **Add field validation**: Ensure mathematical relationships between fields are maintained
5. **Documentation**: Add inline comments explaining why certain fields exist and their relationships
6. **Refactor calculations**: Consolidate the various pricing calculations into a single service
7. **Database cleanup**: Consider migrating old `current_price` and `discount_price` data to `influencer_price` where possible
