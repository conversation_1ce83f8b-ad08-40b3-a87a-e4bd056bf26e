# Database Performance Optimizations

*Created: 2025-01-13*  
*Status: Future Implementation*

This document tracks database performance optimizations that should be implemented as the application scales and dataset grows.

## 1. ChargeInfluencers Job Query Optimization

### Current Status
- **File**: `app/Jobs/ChargeInfluencers.php`
- **Method**: `handle()`
- **Current Dataset**: Small (performance acceptable)
- **Priority**: Low (implement when dataset grows)

### Issue
The ChargeInfluencers job uses a complex query with multiple WHERE conditions that could benefit from a composite index:

```php
$influencerRequestDetails = InfluencerRequestDetail::with('invoices')
    ->where('payment_status', 'pending')
    ->whereNotNull('invoice_id')
    ->whereNull('refund_reason')
    ->whereNotNull('completed_at')
    ->where('is_cashed_out', 0)
    ->whereNull('cashed_out_at')
    ->where('review', 1)
    ->where('status', '!=', 'Cancelled')
    ->whereNull('refund_txn_id')
    ->whereNull('refund_payment_date')
    ->where('is_complained', 0)
    ->where('is_paused', 0)
    // ... additional conditions
```

### Recommended Solution

#### Option 1: Composite Index (Recommended)
Create a Laravel migration to add a composite index:

```bash
php artisan make:migration add_charge_influencers_index_to_influencer_request_details
```

Migration content:
```php
public function up()
{
    Schema::table('influencer_request_details', function (Blueprint $table) {
        $table->index([
            'payment_status', 
            'is_cashed_out', 
            'review', 
            'completed_at', 
            'is_complained', 
            'is_paused'
        ], 'idx_charge_influencers');
    });
}

public function down()
{
    Schema::table('influencer_request_details', function (Blueprint $table) {
        $table->dropIndex('idx_charge_influencers');
    });
}
```

#### Option 2: Individual Indexes
If composite index becomes too large, create individual indexes on most selective columns:

```php
$table->index('payment_status');
$table->index('is_cashed_out');
$table->index('completed_at');
$table->index('review');
```

### Performance Impact
- **Expected Improvement**: 50-90% query time reduction on large datasets
- **Storage Cost**: ~2-5MB additional index storage per 100k records
- **Maintenance Cost**: Minimal (automatic index updates)

### Implementation Trigger
Implement when:
- `influencer_request_details` table exceeds 50,000 records
- ChargeInfluencers job execution time exceeds 30 seconds
- Database CPU usage consistently above 70% during job execution

### Monitoring
After implementation, monitor:
- Query execution time via `EXPLAIN` statements
- Database performance metrics
- Job completion times

---

## 2. Future Optimization Areas

### General Query Patterns
- **Campaign History Queries**: Multiple controllers use similar joins on `influencer_request_details`
- **User Statistics**: Frequent aggregation queries could benefit from materialized views
- **Social Post Updates**: Batch processing optimizations needed

### Index Candidates
- `influencer_request_details.created_at` (used in timeout commands)
- `influencer_request_details.user_id` (frequent filtering)
- `social_posts.user_id` (social media data queries)

### Caching Opportunities
- User statistics calculations
- Campaign summary data
- Social media metrics aggregations

---

## Implementation Guidelines

### 1. Always Use Migrations
```bash
# Create migration
php artisan make:migration add_performance_index_name

# Apply to all environments
php artisan migrate
```

### 2. Test Before Production
- Test on staging with production-like data volume
- Measure query performance before/after
- Monitor for any negative impacts

### 3. Documentation
- Update this document when optimizations are implemented
- Include performance metrics and results
- Note any trade-offs or considerations

---

## Notes
- All optimizations should be implemented via Laravel migrations for consistency across environments
- Performance improvements should be measured and documented
- Consider query complexity vs. index maintenance overhead
- Regular review of slow query logs recommended as application scales

*Last Updated: 2025-01-13*
