<?php

/**
 * <PERSON><PERSON><PERSON> to properly fix all monitoring trait issues
 * Run with: php scripts/fix_all_monitoring_issues.php
 */

$jobsDirectory = __DIR__ . '/../app/Jobs';
$files = glob($jobsDirectory . '/*.php');

$fixed = [];
$errors = [];

foreach ($files as $file) {
    $filename = basename($file);
    
    try {
        $content = file_get_contents($file);
        $originalContent = $content;
        
        // Remove any malformed use statements
        $content = preg_replace('/use Illuminate\\\\Queue\\\\SerializesModels,\s*IsMonitored;/', 'use Illuminate\\Queue\\SerializesModels;', $content);
        
        // Ensure proper import exists
        if (strpos($content, 'use romanzipp\QueueMonitor\Traits\IsMonitored;') === false) {
            // Add the import after SerializesModels
            $content = preg_replace(
                '/(use Illuminate\\\\Queue\\\\SerializesModels;)/',
                "$1\nuse romanzipp\\QueueMonitor\\Traits\\IsMonitored;",
                $content,
                1
            );
        }
        
        // Fix the class use statement
        if (preg_match('/use\s+([^;]*SerializesModels[^;]*);/', $content, $matches)) {
            $useStatement = trim($matches[1]);
            
            // Remove any existing IsMonitored from the use statement
            $useStatement = preg_replace('/,\s*IsMonitored/', '', $useStatement);
            $useStatement = preg_replace('/IsMonitored,\s*/', '', $useStatement);
            $useStatement = preg_replace('/IsMonitored/', '', $useStatement);
            
            // Clean up any double commas or trailing commas
            $useStatement = preg_replace('/,\s*,/', ',', $useStatement);
            $useStatement = preg_replace('/,\s*$/', '', $useStatement);
            
            // Add IsMonitored at the end
            $newUseStatement = $useStatement . ', IsMonitored';
            
            // Replace in content
            $content = preg_replace(
                '/use\s+[^;]*SerializesModels[^;]*;/',
                "use $newUseStatement;",
                $content,
                1
            );
        }
        
        // Only write if content changed
        if ($content !== $originalContent) {
            file_put_contents($file, $content);
            $fixed[] = $filename;
        }
        
    } catch (Exception $e) {
        $errors[] = "$filename: " . $e->getMessage();
    }
}

echo "=== Monitoring Fix Results ===\n\n";

if (!empty($fixed)) {
    echo "✅ FIXED (" . count($fixed) . " files):\n";
    foreach ($fixed as $file) {
        echo "   - $file\n";
    }
    echo "\n";
}

if (!empty($errors)) {
    echo "❌ ERRORS (" . count($errors) . " files):\n";
    foreach ($errors as $error) {
        echo "   - $error\n";
    }
    echo "\n";
}

echo "=== Summary ===\n";
echo "Files processed: " . count($files) . "\n";
echo "Files fixed: " . count($fixed) . "\n";
echo "Errors: " . count($errors) . "\n";

if (count($fixed) > 0) {
    echo "\n🎉 All monitoring issues fixed!\n";
    echo "💡 Remember to restart your queue workers: php artisan queue:restart\n";
}
