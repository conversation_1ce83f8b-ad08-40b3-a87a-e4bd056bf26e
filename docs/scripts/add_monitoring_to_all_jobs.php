<?php

/**
 * <PERSON><PERSON><PERSON> to add IsMonitored trait to all job files
 * Run with: php scripts/add_monitoring_to_all_jobs.php
 */

$jobsDirectory = __DIR__ . '/../app/Jobs';
$files = glob($jobsDirectory . '/*.php');

$monitoringTrait = 'romanzipp\QueueMonitor\Traits\IsMonitored';
$traitName = 'IsMonitored';

$processed = [];
$skipped = [];
$errors = [];

foreach ($files as $file) {
    $filename = basename($file);
    
    try {
        $content = file_get_contents($file);
        
        // Skip if already has the trait
        if (strpos($content, $monitoringTrait) !== false || strpos($content, "use $traitName;") !== false) {
            $skipped[] = $filename . ' (already has IsMonitored trait)';
            continue;
        }
        
        // Skip if not a proper job class
        if (strpos($content, 'implements ShouldQueue') === false) {
            $skipped[] = $filename . ' (not a ShouldQueue job)';
            continue;
        }
        
        $modified = false;
        
        // Add the import after other use statements
        if (preg_match('/use Illuminate\\\\Queue\\\\SerializesModels;/', $content)) {
            $content = preg_replace(
                '/(use Illuminate\\\\Queue\\\\SerializesModels;)/',
                "$1\nuse $monitoringTrait;",
                $content,
                1
            );
            $modified = true;
        }
        
        // Add the trait to the use statement in the class
        if (preg_match('/use\s+([^;]+SerializesModels[^;]*);/', $content, $matches)) {
            $useStatement = $matches[1];
            
            // Check if IsMonitored is already in the use statement
            if (strpos($useStatement, $traitName) === false) {
                $newUseStatement = $useStatement . ', ' . $traitName;
                $content = str_replace(
                    "use $useStatement;",
                    "use $newUseStatement;",
                    $content
                );
                $modified = true;
            }
        }
        
        if ($modified) {
            file_put_contents($file, $content);
            $processed[] = $filename;
        } else {
            $skipped[] = $filename . ' (could not modify)';
        }
        
    } catch (Exception $e) {
        $errors[] = $filename . ': ' . $e->getMessage();
    }
}

// Output results
echo "=== Job Monitoring Setup Results ===\n\n";

if (!empty($processed)) {
    echo "✅ PROCESSED (" . count($processed) . " files):\n";
    foreach ($processed as $file) {
        echo "   - $file\n";
    }
    echo "\n";
}

if (!empty($skipped)) {
    echo "⏭️  SKIPPED (" . count($skipped) . " files):\n";
    foreach ($skipped as $file) {
        echo "   - $file\n";
    }
    echo "\n";
}

if (!empty($errors)) {
    echo "❌ ERRORS (" . count($errors) . " files):\n";
    foreach ($errors as $error) {
        echo "   - $error\n";
    }
    echo "\n";
}

echo "=== Summary ===\n";
echo "Total files found: " . count($files) . "\n";
echo "Successfully processed: " . count($processed) . "\n";
echo "Skipped: " . count($skipped) . "\n";
echo "Errors: " . count($errors) . "\n";

if (count($processed) > 0) {
    echo "\n🎉 Monitoring has been enabled for " . count($processed) . " job(s)!\n";
    echo "💡 Remember to restart your queue workers: php artisan queue:restart\n";
}
