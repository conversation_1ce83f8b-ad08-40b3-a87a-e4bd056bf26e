<?php

/**
 * Final script to properly fix all job trait usage
 * Run with: php scripts/fix_job_traits_final.php
 */

$jobsDirectory = __DIR__ . '/../app/Jobs';
$files = glob($jobsDirectory . '/*.php');

$fixed = [];
$errors = [];

foreach ($files as $file) {
    $filename = basename($file);
    
    try {
        $content = file_get_contents($file);
        $originalContent = $content;
        
        // Fix incorrect trait usage in class
        // Replace any line that has "use Illuminate\Queue\SerializesModels;" inside the class
        $content = preg_replace(
            '/^(\s+)use\s+Illuminate\\\\Queue\\\\SerializesModels;$/m',
            '$1use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, IsMonitored;',
            $content
        );
        
        // Also fix any other malformed trait usage patterns
        $content = preg_replace_callback(
            '/^(\s+)use\s+([^;]*SerializesModels[^;]*);$/m',
            function($matches) {
                $indent = $matches[1];
                $useStatement = $matches[2];

                // If it doesn't already have all the required traits, fix it
                if (!preg_match('/Dispatchable.*InteractsWithQueue.*Queueable.*SerializesModels.*IsMonitored/', $useStatement)) {
                    return $indent . 'use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, IsMonitored;';
                }

                return $matches[0]; // Return unchanged if already correct
            },
            $content
        );
        
        // Only write if content changed
        if ($content !== $originalContent) {
            file_put_contents($file, $content);
            $fixed[] = $filename;
        }
        
    } catch (Exception $e) {
        $errors[] = "$filename: " . $e->getMessage();
    }
}

echo "=== Job Traits Fix Results ===\n\n";

if (!empty($fixed)) {
    echo "✅ FIXED (" . count($fixed) . " files):\n";
    foreach ($fixed as $file) {
        echo "   - $file\n";
    }
    echo "\n";
}

if (!empty($errors)) {
    echo "❌ ERRORS (" . count($errors) . " files):\n";
    foreach ($errors as $error) {
        echo "   - $error\n";
    }
    echo "\n";
}

echo "=== Summary ===\n";
echo "Files processed: " . count($files) . "\n";
echo "Files fixed: " . count($fixed) . "\n";
echo "Errors: " . count($errors) . "\n";

if (count($fixed) > 0) {
    echo "\n🎉 Job trait usage fixed for " . count($fixed) . " file(s)!\n";
    echo "💡 All jobs should now have proper trait usage:\n";
    echo "   use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, IsMonitored;\n";
}
