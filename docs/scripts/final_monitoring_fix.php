<?php

/**
 * Final comprehensive fix for all monitoring issues
 * Run with: php scripts/final_monitoring_fix.php
 */

$jobsDirectory = __DIR__ . '/../app/Jobs';
$files = glob($jobsDirectory . '/*.php');

$fixed = [];
$errors = [];

foreach ($files as $file) {
    $filename = basename($file);
    
    try {
        $content = file_get_contents($file);
        $originalContent = $content;
        
        // Step 1: Remove any malformed use statements with IsMonitored in them
        $content = preg_replace('/use\s+[^;]*SerializesModels\s*,\s*IsMonitored[^;]*;/', 'use Illuminate\\Queue\\SerializesModels;', $content);
        
        // Step 2: Ensure proper import exists (only add if not already there)
        if (strpos($content, 'use romanzipp\QueueMonitor\Traits\IsMonitored;') === false) {
            // Find the position after SerializesModels import
            if (preg_match('/(use Illuminate\\\\Queue\\\\SerializesModels;)/', $content)) {
                $content = preg_replace(
                    '/(use Illuminate\\\\Queue\\\\SerializesModels;)/',
                    "$1\nuse romanzipp\\QueueMonitor\\Traits\\IsMonitored;",
                    $content,
                    1
                );
            }
        }
        
        // Step 3: Fix the class use statement
        if (preg_match('/use\s+([^;]*SerializesModels[^;]*);/', $content, $matches)) {
            $useStatement = trim($matches[1]);
            
            // Remove any existing IsMonitored references
            $useStatement = preg_replace('/,\s*IsMonitored/', '', $useStatement);
            $useStatement = preg_replace('/IsMonitored\s*,/', '', $useStatement);
            $useStatement = preg_replace('/IsMonitored/', '', $useStatement);
            
            // Clean up multiple commas and spaces
            $useStatement = preg_replace('/,\s*,+/', ',', $useStatement);
            $useStatement = preg_replace('/,\s*$/', '', $useStatement);
            $useStatement = trim($useStatement);
            
            // Add IsMonitored at the end
            if (!empty($useStatement)) {
                $newUseStatement = $useStatement . ', IsMonitored';
                
                // Replace in content
                $content = preg_replace(
                    '/use\s+[^;]*SerializesModels[^;]*;/',
                    "use $newUseStatement;",
                    $content,
                    1
                );
            }
        }
        
        // Only write if content changed
        if ($content !== $originalContent) {
            file_put_contents($file, $content);
            $fixed[] = $filename;
        }
        
    } catch (Exception $e) {
        $errors[] = "$filename: " . $e->getMessage();
    }
}

echo "=== Final Monitoring Fix Results ===\n\n";

if (!empty($fixed)) {
    echo "✅ FIXED (" . count($fixed) . " files):\n";
    foreach ($fixed as $file) {
        echo "   - $file\n";
    }
    echo "\n";
}

if (!empty($errors)) {
    echo "❌ ERRORS (" . count($errors) . " files):\n";
    foreach ($errors as $error) {
        echo "   - $error\n";
    }
    echo "\n";
}

echo "=== Summary ===\n";
echo "Files processed: " . count($files) . "\n";
echo "Files fixed: " . count($fixed) . "\n";
echo "Errors: " . count($errors) . "\n";

if (count($fixed) > 0) {
    echo "\n🎉 All monitoring issues should now be fixed!\n";
    echo "💡 Test with: php artisan tinker --execute=\"App\\Jobs\\RepriceAllJob::dispatch();\"\n";
}
