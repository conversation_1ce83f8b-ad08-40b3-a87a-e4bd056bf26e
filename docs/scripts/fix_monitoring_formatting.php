<?php

/**
 * <PERSON><PERSON>t to fix formatting issues in job files after adding IsMonitored trait
 * Run with: php scripts/fix_monitoring_formatting.php
 */

$jobsDirectory = __DIR__ . '/../app/Jobs';
$files = glob($jobsDirectory . '/*.php');

$fixed = [];
$skipped = [];

foreach ($files as $file) {
    $filename = basename($file);
    
    try {
        $content = file_get_contents($file);
        $originalContent = $content;
        
        // Fix: "SerializesModels, IsMonitored;" should be "SerializesModels;"
        $content = preg_replace(
            '/use Illuminate\\\\Queue\\\\SerializesModels,\s*IsMonitored;/',
            'use Illuminate\\Queue\\SerializesModels;',
            $content
        );
        
        // Ensure IsMonitored trait is in the class use statement
        if (strpos($content, 'romanzipp\QueueMonitor\Traits\IsMonitored') !== false) {
            // Find the class use statement and add IsMonitored if not present
            if (preg_match('/use\s+([^;]*SerializesModels[^;]*);/', $content, $matches)) {
                $useStatement = $matches[1];
                
                // If IsMonitored is not in the use statement, add it
                if (strpos($useStatement, 'IsMonitored') === false) {
                    $newUseStatement = $useStatement . ', IsMonitored';
                    $content = str_replace(
                        "use $useStatement;",
                        "use $newUseStatement;",
                        $content
                    );
                }
            }
        }
        
        // Only write if content changed
        if ($content !== $originalContent) {
            file_put_contents($file, $content);
            $fixed[] = $filename;
        } else {
            $skipped[] = $filename;
        }
        
    } catch (Exception $e) {
        echo "Error processing $filename: " . $e->getMessage() . "\n";
    }
}

echo "=== Formatting Fix Results ===\n\n";

if (!empty($fixed)) {
    echo "✅ FIXED (" . count($fixed) . " files):\n";
    foreach ($fixed as $file) {
        echo "   - $file\n";
    }
    echo "\n";
}

if (!empty($skipped)) {
    echo "⏭️  NO CHANGES NEEDED (" . count($skipped) . " files):\n";
    foreach ($skipped as $file) {
        echo "   - $file\n";
    }
    echo "\n";
}

echo "=== Summary ===\n";
echo "Files processed: " . count($files) . "\n";
echo "Files fixed: " . count($fixed) . "\n";
echo "Files unchanged: " . count($skipped) . "\n";

if (count($fixed) > 0) {
    echo "\n🎉 Formatting fixed for " . count($fixed) . " file(s)!\n";
}
