# InfluencerRequestDetail Amount Fields Explained

## Overview

The `InfluencerRequestDetail` model contains multiple amount-related fields that work together to track the complete financial flow of influencer campaigns. This document explains the purpose of each field and how they relate to each other.

## The Four Amount Fields

### 1. `influencer_price` - The Base Price
**Purpose**: The base price that the influencer charges for their service (before any fees, VAT, or platform commissions)

**Database**: 
- Type: `decimal/double`
- Added: Part of original pricing system (2022)

**Usage**:
```php
// Example: Influencer charges €50 for an Instagram post
$detail->influencer_price = 50.00;

// Used in campaign total calculations
$totalAmount = $influencerRequestDetails->sum('influencer_price');
```

### 2. `influencer_amount` - Total Customer Payment
**Purpose**: The total amount the customer pays for this specific influencer (including VAT if applicable)

**Database**: 
- Type: `double` (default: 0)
- Added: Payment fields migration (2024-12-10)

**Usage**:
```php
// Example: €50 base + €9.50 VAT (19%) = €59.50 total
$detail->influencer_amount = 59.50;

// Used in customer billing
$customerTotal = $influencerRequestDetails->sum('influencer_amount');
```

### 3. `platform_amount` - Platform Commission
**Purpose**: The commission amount the platform takes (typically 20% of base price + VAT)

**Database**: 
- Type: `double` (default: 0)
- Added: Payment fields migration (2024-12-10)

**Usage**:
```php
// Example: 20% of €50 = €10, plus €1.90 VAT = €11.90
$detail->platform_amount = 11.90;

// Used in platform revenue tracking
$platformRevenue = $influencerRequestDetails->sum('platform_amount');
```

### 4. `cash_out_amount` - Net Influencer Payout
**Purpose**: The net amount the influencer receives after platform commission is deducted

**Database**: 
- Type: `double` (default: 0)
- Added: Payment fields migration (2024-12-10)

**Usage**:
```php
// Example: €59.50 total - €11.90 commission = €47.60 for influencer
$detail->cash_out_amount = 47.60;

// Used in influencer payouts
$payoutAmount = $campaigns->sum('cash_out_amount');
```

## Financial Flow Example

Here's how a €50 influencer post flows through the system:

| Step | Field | Value | Calculation | Purpose |
|------|-------|-------|-------------|---------|
| 1 | `influencer_price` | €50.00 | Base price | Influencer's rate |
| 2 | `influencer_amount` | €59.50 | €50 + €9.50 VAT | Customer pays |
| 3 | `platform_amount` | €11.90 | (€50 × 20%) + VAT | Platform keeps |
| 4 | `cash_out_amount` | €47.60 | €59.50 - €11.90 | Influencer gets |

**Verification**: €47.60 + €11.90 = €59.50 ✅

## Code Implementation

### Payment Processing (StripeMarketplaceController)
```php
// 1. Start with base prices
$totalAmount = $influencerRequestDetails->sum('influencer_price');

// 2. Calculate commission (20% + VAT)
$commission = ($basePrice * 0.20) + ($basePrice * 0.20 * 0.19);

// 3. Calculate amounts
$influencerAmount = $basePrice + ($basePrice * 0.19); // Base + VAT
$cashOutAmount = $influencerAmount - $commission;     // Net for influencer

// 4. Store all amounts
InfluencerRequestDetail::where('id', $detail->id)->update([
    'platform_amount' => $commission,
    'influencer_amount' => $influencerAmount, 
    'cash_out_amount' => $cashOutAmount,
    'payment_status' => 'completed'
]);
```

### Campaign Calculations (OpenCampaignsController)
```php
// Use influencer_price for campaign totals
$totalAmount = 0;
foreach ($influencerRequestDetails as $detail) {
    $influencerPrice = $detail->influencer_price ?? $detail->discount_price;
    $totalAmount += $influencerPrice;
    
    // Add VAT if not small business
    if (!$user->is_small_business_owner) {
        $totalAmount += $influencerPrice * 0.19;
    }
}
```

### Payout Processing (PayoutController)
```php
// Calculate available payout amount
$availableAmount = InfluencerRequestDetail::whereHas('influencerdetails', function ($query) {
    $query->where('user_id', Auth::id());
})->where('payment_status', 'completed')
  ->where('is_cashed_out', 0)
  ->sum('cash_out_amount');
```

## Historical Context & Evolution

### Phase 1: Original Pricing (2022)
- Only `influencer_price` existed
- Simple pricing without detailed financial tracking
- Used `discount_price` as fallback

### Phase 2: Payment Fields Addition (2024-12-10)
- Added `cash_out_amount`, `platform_amount`, `influencer_amount`
- Enabled detailed financial tracking
- Separated customer billing from influencer payouts

### Phase 3: Current State (2025)
- All four fields work together
- `influencer_price` remains the foundation
- Other fields provide detailed financial breakdown

## Why Keep All Four Fields?

### 1. **Data Integrity**
Each field serves a specific business purpose and audit trail:
- `influencer_price`: Original agreed rate
- `influencer_amount`: Customer billing record
- `platform_amount`: Revenue tracking
- `cash_out_amount`: Payout obligations

### 2. **Business Logic Clarity**
```php
// Clear separation of concerns
$baseRate = $detail->influencer_price;        // Pricing logic
$customerBill = $detail->influencer_amount;   // Billing logic  
$platformRevenue = $detail->platform_amount; // Revenue logic
$influencerPayout = $detail->cash_out_amount; // Payout logic
```

### 3. **Reporting & Analytics**
Different stakeholders need different views:
- **Influencers**: Care about `cash_out_amount`
- **Customers**: See `influencer_amount` on invoices
- **Platform**: Tracks `platform_amount` for revenue
- **Pricing Team**: Analyzes `influencer_price` trends

### 4. **Backward Compatibility**
Maintains compatibility with existing pricing system:
```php
// Fallback pattern used throughout codebase
$price = $detail->influencer_price ?? $detail->discount_price;
```

## Common Patterns

### Validation
```php
// Ensure amounts add up correctly
$calculatedTotal = $detail->cash_out_amount + $detail->platform_amount;
if (abs($calculatedTotal - $detail->influencer_amount) > 0.01) {
    throw new \Exception('Amount calculation mismatch');
}
```

### Default Values
All payment fields default to 0 until payment processing:
```php
// New campaigns start with:
$detail->influencer_price = 50.00;    // Set during campaign creation
$detail->cash_out_amount = 0;         // Set during payment processing
$detail->platform_amount = 0;        // Set during payment processing  
$detail->influencer_amount = 0;      // Set during payment processing
```

### Status Tracking
```php
// Check if amounts have been calculated
$isProcessed = $detail->cash_out_amount > 0 && 
               $detail->platform_amount > 0 && 
               $detail->influencer_amount > 0;
```

## Related Documentation

- `docs/influencer-request-detail-price-fields-explained.md` - Detailed price field analysis
- `database/migrations/2024_12_10_085936_add_payment_fields_to_influencer_request_details_table.php` - Payment fields migration
- `app/Http/Controllers/Frontend/StripeMarketplaceController.php` - Payment processing implementation

---

**Last Updated**: 2025-01-29  
**Author**: System Documentation  
**Related Models**: `InfluencerRequestDetail`, `Invoice`, `Campaign`
