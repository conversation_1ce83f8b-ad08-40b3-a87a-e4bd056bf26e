# Queue Monitor Package - Deployment Guide

## Overview

This guide covers the complete installation and configuration of the Laravel Queue Monitor package for tracking and monitoring queue jobs in production environments.

## Package Information

- **Package**: `romanzipp/laravel-queue-monitor`
- **Purpose**: Monitor and track Laravel queue job execution
- **Features**: Job tracking, performance metrics, error monitoring, admin dashboard

## Installation Steps

### 1. Install the Package

```bash
composer require romanzipp/laravel-queue-monitor
```

### 2. Publish Configuration

```bash
php artisan vendor:publish --provider="romanzipp\QueueMonitor\Providers\QueueMonitorProvider" --tag="config"
```

### 3. Publish and Run Migrations

```bash
# Publish migrations
php artisan vendor:publish --provider="romanzipp\QueueMonitor\Providers\QueueMonitorProvider" --tag="migrations"

# Run migrations
php artisan migrate
```

**Important**: Ensure the migration creates the correct table structure. The table should have these columns:
- `id`, `job_uuid`, `job_id`, `name`, `queue`, `status`
- `queued_at`, `started_at`, `started_at_exact`, `finished_at`, `finished_at_exact`
- `attempt`, `retried`, `progress`
- `exception`, `exception_message`, `exception_class`, `data`

### 4. Configure the Package

Edit `config/queue-monitor.php`:

```php
<?php

return [
    // Enable monitoring
    'enabled' => env('QUEUE_MONITOR_ENABLED', true),

    // Database connection (null = default)
    'connection' => env('QUEUE_MONITOR_DB_CONNECTION', null),

    // Table name
    'table' => env('QUEUE_MONITOR_TABLE', 'queue_monitor_jobs'),

    // Monitor specific queues (empty array = all queues)
    'queues' => [
        'default',
    ],

    // Monitor queued jobs
    'monitor_queued_jobs' => true,

    // Disable built-in UI (we use custom dashboard)
    'ui' => [
        'enabled' => false,
    ],

    // Cleanup settings
    'cleanup' => [
        'enabled' => true,
        'mode' => 'days',
        'keep' => 7,  // Keep records for 7 days
    ],
];
```

### 5. Add Environment Variables

Add to your `.env` file:

```env
# Queue Monitor Configuration
QUEUE_MONITOR_ENABLED=true
QUEUE_MONITOR_DB_CONNECTION=null
QUEUE_MONITOR_TABLE=queue_monitor_jobs
```

### 6. Update Jobs to Enable Monitoring

Add the monitoring trait to jobs you want to track:

```php
<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use romanzipp\QueueMonitor\Traits\IsMonitored; // Add this

class RepriceAllJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, IsMonitored; // Add IsMonitored

    // ... rest of your job code
}
```

### 7. Install Custom Admin Dashboard

The custom admin dashboard files should be in place:

**Controller**: `app/Http/Controllers/Admin/QueueMonitorController.php`
**Views**: 
- `resources/views/admin/queue-monitor/dashboard.blade.php`
- `resources/views/admin/queue-monitor/index.blade.php`
- `resources/views/admin/queue-monitor/show.blade.php`

**Routes**: Add to `routes/web.php`:

```php
// Queue Monitor routes (inside admin middleware group)
Route::middleware('admin.only')->group(function () {
    Route::get('/queue-monitor', [\App\Http\Controllers\Admin\QueueMonitorController::class, 'dashboard'])->name('admin.queue-monitor.dashboard');
    Route::get('/queue-monitor/jobs', [\App\Http\Controllers\Admin\QueueMonitorController::class, 'index'])->name('admin.queue-monitor.index');
    Route::get('/queue-monitor/jobs/{monitor}', [\App\Http\Controllers\Admin\QueueMonitorController::class, 'show'])->name('admin.queue-monitor.show');
    Route::post('/queue-monitor/start-repricing', [\App\Http\Controllers\Admin\QueueMonitorController::class, 'startRepricing'])->name('admin.queue-monitor.start-repricing');
    Route::post('/queue-monitor/jobs/{monitor}/retry', [\App\Http\Controllers\Admin\QueueMonitorController::class, 'retry'])->name('admin.queue-monitor.retry');
    Route::delete('/queue-monitor/jobs/{monitor}', [\App\Http\Controllers\Admin\QueueMonitorController::class, 'delete'])->name('admin.queue-monitor.delete');
    Route::post('/queue-monitor/clear-completed', [\App\Http\Controllers\Admin\QueueMonitorController::class, 'clearCompleted'])->name('admin.queue-monitor.clear-completed');
});
```

## Production Configuration

### 1. Queue Workers

Ensure queue workers are running with proper configuration:

```bash
# Start queue workers
php artisan queue:work --tries=3 --timeout=300 --memory=512

# Or use Supervisor for production
```

**Supervisor Configuration** (`/etc/supervisor/conf.d/laravel-worker.conf`):

```ini
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/your/app/artisan queue:work --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=3
redirect_stderr=true
stdout_logfile=/path/to/your/app/storage/logs/worker.log
stopwaitsecs=3600
```

### 2. Database Optimization

Add indexes for better performance:

```sql
-- Add indexes to queue_monitor_jobs table
ALTER TABLE queue_monitor_jobs ADD INDEX idx_status (status);
ALTER TABLE queue_monitor_jobs ADD INDEX idx_started_at (started_at);
ALTER TABLE queue_monitor_jobs ADD INDEX idx_queue (queue);
ALTER TABLE queue_monitor_jobs ADD INDEX idx_name (name);
```

### 3. Cleanup Configuration

Set up automatic cleanup in your scheduler (`app/Console/Kernel.php`):

```php
protected function schedule(Schedule $schedule)
{
    // Clean up old queue monitor records daily
    $schedule->command('queue-monitor:cleanup')->daily();
}
```

### 4. Monitoring and Alerts

Set up monitoring for:

- **Failed Jobs**: Alert when failure rate exceeds threshold
- **Queue Length**: Alert when queue gets too long
- **Worker Health**: Monitor if workers are running
- **Performance**: Track average execution times

## Troubleshooting

### Common Issues

**1. Jobs Not Being Monitored**

```bash
# Check if package is installed
composer show romanzipp/laravel-queue-monitor

# Verify table exists
php artisan tinker
>>> Schema::hasTable('queue_monitor_jobs')

# Check if trait is added to job
# Ensure IsMonitored trait is used in job class
```

**2. Database Connection Issues**

```bash
# Test database connection
php artisan tinker
>>> DB::connection()->getPdo()

# Check migration status
php artisan migrate:status | grep queue
```

**3. Permission Issues**

```bash
# Ensure proper permissions
chown -R www-data:www-data storage/
chmod -R 755 storage/
```

**4. Queue Workers Not Processing**

```bash
# Check if workers are running
ps aux | grep "queue:work"

# Restart workers
php artisan queue:restart

# Check queue status
php artisan queue:monitor
```

### Performance Optimization

**1. Database Cleanup**

```php
// Add to scheduled tasks
$schedule->call(function () {
    \romanzipp\QueueMonitor\Models\Monitor::where('started_at', '<', now()->subDays(7))->delete();
})->daily();
```

**2. Memory Management**

```bash
# Use memory limits for workers
php artisan queue:work --memory=512

# Monitor memory usage
php artisan queue:work --memory=512 --timeout=300
```

**3. Queue Optimization**

```php
// config/queue.php
'connections' => [
    'database' => [
        'driver' => 'database',
        'table' => 'jobs',
        'queue' => 'default',
        'retry_after' => 90,
        'after_commit' => false,
    ],
],
```

## Security Considerations

### 1. Admin Access Control

Ensure only authorized users can access the queue monitor:

```php
// In AdminOnly middleware or similar
public function handle($request, Closure $next)
{
    if (!auth()->check() || !auth()->user()->isAdmin()) {
        abort(403, 'Access denied');
    }
    
    return $next($request);
}
```

### 2. Data Sanitization

The queue monitor stores job data. Ensure sensitive information is not logged:

```php
// In your jobs, avoid logging sensitive data
public function handle()
{
    // Don't log passwords, API keys, etc.
    Log::info('Processing job', [
        'user_id' => $this->userId,
        // 'password' => $this->password, // DON'T DO THIS
    ]);
}
```

### 3. Rate Limiting

Consider rate limiting for the admin dashboard:

```php
Route::middleware(['auth', 'admin.only', 'throttle:60,1'])->group(function () {
    // Queue monitor routes
});
```

## Monitoring and Maintenance

### Daily Tasks

1. **Check Failed Jobs**: Review and retry failed jobs
2. **Monitor Performance**: Check average execution times
3. **Clean Old Records**: Ensure cleanup is working
4. **Worker Health**: Verify workers are running

### Weekly Tasks

1. **Performance Review**: Analyze job trends
2. **Capacity Planning**: Check if more workers needed
3. **Error Analysis**: Review common failure patterns

### Monthly Tasks

1. **Database Optimization**: Analyze and optimize queries
2. **Capacity Review**: Assess infrastructure needs
3. **Security Audit**: Review access logs and permissions

## Access URLs

Once deployed, access the queue monitor at:

- **Dashboard**: `/admin/queue-monitor`
- **Jobs List**: `/admin/queue-monitor/jobs`
- **Job Details**: `/admin/queue-monitor/jobs/{id}`

## Support and Maintenance

For ongoing support:

1. **Documentation**: Refer to package documentation
2. **Logs**: Check Laravel logs for errors
3. **Monitoring**: Set up alerts for critical issues
4. **Backups**: Ensure queue monitor data is backed up

The queue monitor package is now ready for production use with comprehensive monitoring and management capabilities!
