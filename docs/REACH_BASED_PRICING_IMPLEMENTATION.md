# Reach-Based Pricing Implementation

## Overview

This document describes the implementation of reach-based pricing for influencer campaigns in ClickItFame. The new system integrates reach metrics into the existing CPT (Cost per Thousand Follower) pricing model to provide more accurate pricing based on actual performance data.

## Implementation Summary

### 1. Database Changes

**Migration**: `2025_08_08_000001_add_estimated_reach_to_admin_pricings_table.php`
- Added `estimated_reach` column to `admin_pricings` table
- Type: `decimal(5,2)` to store percentage values (e.g., 20.00 for 20%)
- Nullable to maintain backward compatibility

**Model Updates**:
- Updated `AdminPricing` model to include `estimated_reach` in fillable fields
- Added decimal casting for proper type handling

### 2. Service Classes

**AverageReachCalculatorService** (`app/Services/AverageReachCalculatorService.php`)
- Calculates average reach for influencers by post type (story, reel, post)
- Maps campaign types to post types:
  - Boost Me → Story
  - Survey → Story  
  - Reaction Video → Reel
- Only considers posts from last 24 hours
- Handles zero reach scenarios gracefully

**ReachBasedPriceCalculatorService** (`app/Services/ReachBasedPriceCalculatorService.php`)
- Implements the reach multiplier calculation: `Average Reach / (Followers × Estimated Reach %)`
- Applies reach adjustment to base price after gamification
- Provides comprehensive debug information
- Handles edge cases (zero reach, missing configuration, no social connection)

### 3. Pricing Calculation Flow

The new pricing flow follows these steps:

1. **Base CPT Calculation**: `CPT × (Followers / 1000)`
2. **Gamification Multiplier**: `Base Price × (Gamification % / 100)`
3. **NEW: Reach Adjustment**: `Price After Gamification × Reach Multiplier`
4. **VAT Addition** (if not small business): `Price × 1.19`
5. **CIF Provision Deduction**: `Price × 0.8`

### 4. Reach Multiplier Calculation

```
Reach Multiplier = Average Reach / (Followers × Estimated Reach %)

Example:
- Average Reach: 3,000
- Followers: 10,000
- Estimated Reach: 20%
- Expected Reach: 10,000 × 0.20 = 2,000
- Reach Multiplier: 3,000 / 2,000 = 1.5
- Final Price: Base Price × 1.5
```

### 5. Controller Integration

**UserController** (`app/Http/Controllers/Frontend/UserController.php`)
- Updated `getAdminPricing()` method to integrate reach-based pricing
- Added service instantiation and reach calculation
- Enhanced response with reach multiplier and usage flag
- Includes debug information in development mode

### 6. Admin Interface Updates

**Add Pricing Form** (`resources/views/admin/pricing/add.blade.php`)
- Added "Estimated Reach (%)" field
- Updated JavaScript for dynamic row addition

**Edit Pricing Form** (`resources/views/admin/pricing/edit.blade.php`)
- Added estimated reach field with existing value population
- Updated dynamic row generation

**AdminController** (`app/Http/Controllers/Backend/AdminController.php`)
- Updated `savePricing()` and `updatePricing()` methods
- Handles estimated_reach field with null fallback

### 7. Edge Case Handling

The system gracefully handles several edge cases:

1. **Zero Average Reach**: Falls back to standard CPT pricing
2. **No Estimated Reach Configured**: Skips reach-based pricing
3. **No Social Media Connection**: Returns standard pricing
4. **No Recent Posts**: Average reach becomes 0, falls back to standard pricing
5. **Unknown Campaign Type**: Returns 0 average reach

### 8. Testing

Comprehensive test suite includes:

**Unit Tests**:
- `AverageReachCalculatorServiceTest`: Tests reach calculation logic
- `ReachBasedPriceCalculatorServiceTest`: Tests price adjustment logic

**Integration Tests**:
- `ReachBasedPricingIntegrationTest`: End-to-end pricing flow tests

**Test Factories**:
- `SocialPostFactory`: Creates test social media posts
- `SocialConnectFactory`: Creates test social media connections  
- `AdminPricingFactory`: Creates test pricing configurations

### 9. API Response Format

The pricing API now returns additional fields:

```json
{
  "code": "200",
  "price": "57.12",
  "price_next": "74.26",
  "reach_multiplier": 1.5,
  "used_reach_pricing": true,
  "debug_info": {
    "average_reach": 3000,
    "followers": 10000,
    "estimated_reach_percentage": 20.00,
    "expected_reach": 2000,
    "base_price_after_gamification": 40.0,
    "adjusted_price": 60.0
  }
}
```

### 10. Backward Compatibility

The implementation maintains full backward compatibility:
- Existing pricing configurations work without estimated_reach
- When reach-based pricing cannot be applied, falls back to standard CPT pricing
- All existing API endpoints continue to work
- No breaking changes to database schema

### 11. Configuration

To enable reach-based pricing for a campaign type:

1. Go to Admin → Manage Pricing
2. Edit or create pricing configuration
3. Set "Estimated Reach (%)" field (e.g., 20.00 for 20%)
4. Save configuration

The system will automatically apply reach-based pricing when:
- Estimated reach is configured
- Influencer has recent posts (last 24 hours)
- Posts have reach data > 0
- Social media connection exists

### 12. Monitoring and Debugging

In development mode, the API response includes detailed debug information showing:
- Average reach calculation
- Expected reach calculation  
- Reach multiplier applied
- Reason for skipping reach-based pricing (if applicable)

This information helps administrators understand pricing decisions and troubleshoot issues.

## Future Enhancements

Potential improvements for future versions:
1. Configurable time window (currently fixed at 24 hours)
2. Weighted average reach based on post recency
3. Platform-specific reach calculations
4. Historical reach trend analysis
5. Automated estimated reach updates based on platform data
