# Queue Monitor Setup Checklist

## Pre-Deployment Checklist

### ✅ Package Installation
- [ ] Install package: `composer require romanzipp/laravel-queue-monitor`
- [ ] Publish config: `php artisan vendor:publish --provider="romanzipp\QueueMonitor\Providers\QueueMonitorProvider" --tag="config"`
- [ ] Publish migrations: `php artisan vendor:publish --provider="romanzipp\QueueMonitor\Providers\QueueMonitorProvider" --tag="migrations"`
- [ ] Run migrations: `php artisan migrate`

### ✅ Configuration
- [ ] Update `config/queue-monitor.php` with correct settings
- [ ] Add environment variables to `.env`
- [ ] Verify table structure has all required columns
- [ ] Configure cleanup settings (7-day retention recommended)

### ✅ Job Monitoring Setup
- [ ] Add `IsMonitored` trait to `RepriceAllJob`
- [ ] Add `use romanzipp\QueueMonitor\Traits\IsMonitored;` import
- [ ] Test job monitoring with sample job dispatch

### ✅ Admin Dashboard
- [ ] Verify `QueueMonitorController` is in place
- [ ] Confirm all view files exist in `resources/views/admin/queue-monitor/`
- [ ] Add routes to `routes/web.php` with proper middleware
- [ ] Test dashboard access at `/admin/queue-monitor`

### ✅ Queue Workers
- [ ] Ensure queue workers are running: `php artisan queue:work`
- [ ] Configure Supervisor for production (recommended)
- [ ] Set appropriate memory limits and timeouts
- [ ] Test job processing and monitoring

## Post-Deployment Verification

### ✅ Functionality Tests
- [ ] Access dashboard: `/admin/queue-monitor`
- [ ] Start repricing job from dashboard
- [ ] Verify job appears in monitoring table
- [ ] Check job details page works
- [ ] Test retry functionality for failed jobs
- [ ] Verify cleanup functionality

### ✅ Performance Checks
- [ ] Monitor database performance with queue monitor table
- [ ] Check memory usage of queue workers
- [ ] Verify job execution times are reasonable
- [ ] Test with multiple concurrent jobs

### ✅ Security Verification
- [ ] Confirm only admin users can access dashboard
- [ ] Verify no sensitive data is logged in job details
- [ ] Check CSRF protection on all forms
- [ ] Test access control middleware

## Production Monitoring

### ✅ Daily Monitoring
- [ ] Check failed job count and investigate failures
- [ ] Monitor average job execution times
- [ ] Verify queue workers are running
- [ ] Check database storage usage

### ✅ Weekly Reviews
- [ ] Analyze job performance trends
- [ ] Review error patterns and common failures
- [ ] Check if additional workers are needed
- [ ] Verify cleanup is working properly

### ✅ Monthly Maintenance
- [ ] Database optimization and indexing
- [ ] Review and update cleanup retention period
- [ ] Capacity planning for queue infrastructure
- [ ] Security audit of access logs

## Troubleshooting Quick Reference

### Common Issues and Solutions

**Jobs Not Being Monitored:**
```bash
# Check if trait is added to job
grep -r "IsMonitored" app/Jobs/

# Verify table exists
php artisan tinker
>>> Schema::hasTable('queue_monitor_jobs')

# Test with simple job dispatch
php artisan tinker
>>> App\Jobs\RepriceAllJob::dispatch()
```

**Dashboard Access Issues:**
```bash
# Check routes
php artisan route:list | grep queue-monitor

# Verify middleware
# Ensure admin.only middleware is working

# Clear caches
php artisan route:cache
php artisan config:cache
```

**Performance Issues:**
```bash
# Add database indexes
ALTER TABLE queue_monitor_jobs ADD INDEX idx_status (status);
ALTER TABLE queue_monitor_jobs ADD INDEX idx_started_at (started_at);

# Monitor table size
SELECT COUNT(*) FROM queue_monitor_jobs;

# Clean old records
DELETE FROM queue_monitor_jobs WHERE started_at < DATE_SUB(NOW(), INTERVAL 7 DAY);
```

## Environment-Specific Notes

### Development
- Monitor data retention: 3-7 days
- Single queue worker sufficient
- Debug mode can be enabled for detailed logging

### Staging
- Mirror production configuration
- Test with realistic job volumes
- Verify all monitoring features work

### Production
- Multiple queue workers with Supervisor
- Database indexes for performance
- Automated cleanup scheduled
- Monitoring and alerting configured
- Regular backups including monitor data

## Success Criteria

✅ **Installation Complete When:**
- Package installed and configured
- Database table created with correct structure
- Jobs are being monitored automatically
- Admin dashboard accessible and functional

✅ **Production Ready When:**
- Queue workers running with Supervisor
- Database optimized with proper indexes
- Cleanup automation configured
- Monitoring and alerting in place
- Security measures verified

✅ **Fully Operational When:**
- Jobs processing and being monitored
- Dashboard showing real-time data
- Performance metrics within acceptable ranges
- Error handling and retry mechanisms working
- Regular maintenance procedures established

## Quick Commands Reference

```bash
# Check monitoring status
php artisan tinker
>>> romanzipp\QueueMonitor\Models\Monitor::count()

# Dispatch test job
php artisan reprice:all

# Check queue workers
ps aux | grep "queue:work"

# Monitor queue in real-time
php artisan queue:monitor

# Clear old monitor records
php artisan queue-monitor:cleanup

# Restart queue workers
php artisan queue:restart
```

## Support Resources

- **Package Documentation**: [GitHub Repository](https://github.com/romanzipp/Laravel-Queue-Monitor)
- **Laravel Queue Documentation**: [Laravel Docs](https://laravel.com/docs/queues)
- **Custom Dashboard**: See `docs/DEPLOYMENT_GUIDE_QUEUE_MONITOR.md`
- **Troubleshooting**: Check Laravel logs in `storage/logs/`

---

**Note**: This checklist should be completed in order. Each section builds on the previous one, so ensure all items in a section are completed before moving to the next.
