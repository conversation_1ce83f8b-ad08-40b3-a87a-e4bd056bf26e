# Queue Monitor Admin Dashboard Setup

## Overview

This setup provides a comprehensive admin dashboard for monitoring queue jobs, specifically designed to work with your existing `romanzipp/Laravel-Queue-Monitor` package without requiring Redis.

## Features

✅ **Real-time Job Monitoring** - View all jobs with status, timing, and performance metrics  
✅ **Admin-Only Access** - Secure access control for admin users  
✅ **RepriceAllJob Integration** - Start repricing jobs directly from the dashboard  
✅ **Detailed Job Views** - Complete job information including error traces  
✅ **Job Management** - Retry failed jobs, delete old records, clear completed jobs  
✅ **Performance Analytics** - Charts, statistics, and performance metrics  
✅ **Auto-refresh** - Real-time updates for running jobs  

## Setup Instructions

### 1. Register Admin Middleware

Add the admin middleware to your `app/Http/Kernel.php`:

```php
// app/Http/Kernel.php
protected $middlewareAliases = [
    // ... existing middleware
    'admin' => \App\Http\Middleware\AdminMiddleware::class,
];
```

### 2. Configure Admin Detection

Edit `app/Http/Middleware/AdminMiddleware.php` to match your admin detection method:

```php
// Option 1: is_admin column (recommended)
if (isset($user->is_admin) && $user->is_admin) {
    return $next($request);
}

// Option 2: Role-based (if using Spatie Permission or similar)
if ($user->hasRole('admin')) {
    return $next($request);
}

// Option 3: Specific emails
$adminEmails = ['<EMAIL>'];
if (in_array($user->email, $adminEmails)) {
    return $next($request);
}
```

### 3. Include Routes

Add the queue monitor routes to your main routes file:

```php
// routes/web.php
require __DIR__.'/admin_queue_monitor.php';
```

Or manually add them:

```php
// routes/web.php
use App\Http\Controllers\Admin\QueueMonitorController;

Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/queue-monitor', [QueueMonitorController::class, 'dashboard'])->name('queue-monitor.dashboard');
    Route::get('/queue-monitor/jobs', [QueueMonitorController::class, 'index'])->name('queue-monitor.index');
    Route::get('/queue-monitor/jobs/{monitor}', [QueueMonitorController::class, 'show'])->name('queue-monitor.show');
    Route::post('/queue-monitor/start-repricing', [QueueMonitorController::class, 'startRepricing'])->name('queue-monitor.start-repricing');
    Route::post('/queue-monitor/jobs/{monitor}/retry', [QueueMonitorController::class, 'retry'])->name('queue-monitor.retry');
    Route::delete('/queue-monitor/jobs/{monitor}', [QueueMonitorController::class, 'delete'])->name('queue-monitor.delete');
    Route::post('/queue-monitor/clear-completed', [QueueMonitorController::class, 'clearCompleted'])->name('queue-monitor.clear-completed');
});
```

### 4. Update Layout Reference

The views extend `admin.layout`. Update this to match your admin layout:

```blade
{{-- In the view files, change: --}}
@extends('admin.layout')

{{-- To your actual admin layout: --}}
@extends('layouts.admin')
{{-- or --}}
@extends('adminlte::page')
{{-- or whatever your admin layout is --}}
```

### 5. Start Queue Workers

Make sure you have queue workers running to process jobs:

```bash
# Development
php artisan queue:work

# Production (use Supervisor)
php artisan queue:work --daemon --tries=3 --timeout=300
```

### 6. Configure Queue Monitor Package

Ensure your `config/queue-monitor.php` is properly configured:

```php
// config/queue-monitor.php
return [
    'enabled' => true,
    'ui' => [
        'show_successful' => true,
        'show_failed' => true,
    ],
    'cleanup' => [
        'mode' => 'days',
        'keep' => 7, // Keep records for 7 days
    ],
];
```

## Usage

### Access the Dashboard

Navigate to: `http://your-app.com/admin/queue-monitor`

### Start Repricing Job

**From Dashboard:**
- Click "Start Repricing" button
- Job will be queued and monitored

**From Command Line:**
```bash
# Queue the job
php artisan reprice:all

# Run synchronously (not recommended for production)
php artisan reprice:all --sync

# Force start even if other jobs are running
php artisan reprice:all --force
```

### Monitor Jobs

1. **Dashboard View** - Overview with statistics and charts
2. **Jobs List** - Detailed list with filters and pagination
3. **Job Details** - Complete information about individual jobs

### Job Management

- **Retry Failed Jobs** - Click retry button on failed jobs
- **Delete Job Records** - Remove individual job records
- **Clear Old Jobs** - Bulk delete completed jobs older than 7 days

## Dashboard Features

### Statistics Cards
- Total jobs processed
- Overall success rate
- Currently running jobs
- Failed jobs count

### Performance Chart
- 7-day job activity chart
- Success/failure trends
- Visual performance monitoring

### Recent Jobs Table
- Latest job executions
- Real-time status updates
- Quick access to job details

### Job Details Page
- Complete job information
- Error traces for failed jobs
- Performance metrics
- Special information for RepriceAllJob

## Security Features

- **Admin-only access** via middleware
- **CSRF protection** on all forms
- **Confirmation dialogs** for destructive actions
- **Input validation** on all filters

## Performance Considerations

- **Auto-refresh** every 30 seconds on dashboard
- **Auto-refresh** every 10 seconds on jobs list (if running jobs exist)
- **Auto-refresh** every 5 seconds on job details (if job is running)
- **Pagination** for large job lists (25 per page)
- **Efficient queries** with proper indexing

## Troubleshooting

### No Jobs Appearing
1. Check if queue workers are running: `php artisan queue:work`
2. Verify Queue Monitor package is installed and configured
3. Ensure jobs are being dispatched: `RepriceAllJob::dispatch()`

### Access Denied
1. Check admin middleware configuration
2. Verify user has admin privileges
3. Check route middleware is applied correctly

### Jobs Not Processing
1. Ensure queue workers are running
2. Check queue configuration in `config/queue.php`
3. Verify database queue table exists: `php artisan queue:table && php artisan migrate`

### Performance Issues
1. Clear old job records regularly
2. Add database indexes on queue monitor table
3. Consider using Redis for better performance (optional)

## Next Steps

1. **Customize the admin layout** to match your existing admin panel
2. **Add more job types** to the monitoring system
3. **Set up alerts** for failed jobs (email, Slack, etc.)
4. **Add more detailed analytics** and reporting
5. **Configure automatic cleanup** of old job records

The queue monitor dashboard is now ready to use and provides comprehensive monitoring for your RepriceAllJob and any other queued jobs!
