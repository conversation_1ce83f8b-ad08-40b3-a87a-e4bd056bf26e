{{-- To see the contents of the varaible $influencerCampaignDetail, check OpenCampaignsController::buildOpenCampaignsPageForBrand() --}}
<div class="campaign-header">
    <div class="row"
        style="margin-top: -18px; margin-left: 0px; margin-right: 0px; cursor: pointer;"
        data-bs-toggle="modal"
        data-bs-target="#brandCampaignPhasesModal">
        @if ($influencerCampaignDetail->created_at->addDays(3) > now())
            <span class="btn-request-phase" style="width: 100%; padding: 5px;display: flex; justify-content: space-between;">
                <span style="text-align: left;">Request Phase</span>
                <span style="text-align: right;">ID # {{ $influencerCampaignDetail->campaign_id }}</span>
            </span><br>
        @else
            <span style="width: 100%; padding: 5px;display: flex; background-color:#E8C11C; color:white; justify-content: space-between;">
                <span style="text-align: left;">Payment Phase</span>
                <span style="text-align: right;">ID # {{ $influencerCampaignDetail->campaign_id }}</span>
            </span><br>
        @endif
    </div>
    <div class="header-section" style="padding: 0px;">
        <div style="display: flex; width: 100%;">
            <div style="width: 69%;">
                <h4 class="truncate-text" style="width: 100%; font-weight: 700;font-size: 15px; padding-top: 10px;">
                    {{ $influencerCampaignDetail->campaign_title }}
                </h4>
                <div class="row gx-0" style="display: flex;">
                    <div class="row">
                        <span class="badge col-sm px-0 my-0" style="text-align: left;"
                            target="popup" data-bs-toggle="modal"
                            data-bs-target="#showInfluencers{{ $influencerCampaignDetail->id }}">
                            <img src="{{ asset('/assets/front-end/images/new/three_users.svg') }}">
                            {{ $influencerCampaignDetail->total_influencer_count }}
                            {{ $influencerCampaignDetail->total_influencer_count > 1 ? 'Influencers' : 'Influencer' }}
                        </span>
                        <span class="badge col-sm px-0 my-0" style="text-align: left;">
                            <span
                                class="ctrb green-user p-1 mx-1 rounded text-white">{{ $influencerCampaignDetail->accept_request > 0 ? $influencerCampaignDetail->accept_request : 0 }}</span>
                            <span
                                class="ctrb red-user p-1 mx-1 rounded text-white">{{ $influencerCampaignDetail->total_count - $influencerCampaignDetail->accept_request }}</span>
                            <span class="mx-1" target="popup" data-bs-toggle="modal" data-bs-target="#showInfluencers{{ $influencerCampaignDetail->id }}">
                                <img src="{{ asset('/assets/front-end/images/icon-gare.svg') }}" alt="" style="width: 1.5em;">
                            </span>
                        </span>
                    </div>
                    <div class="row">
                        <span class="badge col-sm px-0 my-0" style="text-align: left;">
                            <img src="{{ asset('/assets/front-end/images/new/survey_camp.svg') }}">
                            {{ $influencerCampaignDetail->advertising }}
                        </span>
                        <span class="badge col-sm px-0 my-0" style="text-align: left;">
                            <img width="20" height="20" src="{{ asset('/assets/front-end/images/icons/campaigns-' . $influencerCampaignDetail->media . '.svg') }}">
                            {{ $influencerCampaignDetail->post_type }}
                        </span>
                    </div>
                    <div class="row">
                        <span class="text-success col-sm px-2 mx-0" style="width:50%;">€
                            @php
                                // Calculate total using same logic as payment modal
                                $subtotal = 0;
                                $totalVAT = 0;
                                $campaignInfluencers = \App\Models\InfluencerRequestDetail::where('compaign_id', $influencerCampaignDetail->campaign_id)->get();

                                foreach ($campaignInfluencers as $campaignInfluencer) {
                                    $influencerPrice = $campaignInfluencer->influencer_price ?? 0;
                                    $subtotal += $influencerPrice;

                                    // Calculate VAT separately (same as payment modal)
                                    if ($campaignInfluencer->influencerdetails && $campaignInfluencer->influencerdetails->user && !$campaignInfluencer->influencerdetails->user->is_small_business_owner) {
                                        $totalVAT += $influencerPrice * 0.19;
                                    }
                                }

                                // Calculate platform fee on subtotal only (same as payment modal)
                                $platformFee = ($subtotal * 5) / 100;
                                if ($platformFee < 2) {
                                    $platformFee = 2;
                                }
                                $totalVAT += $platformFee * 0.19; // Add platform fee VAT

                                $campaignTotal = $subtotal + $platformFee + $totalVAT;
                            @endphp
                            {{ number_format($campaignTotal, 2) }}
                        </span>
                        <span class="submit-text-color col-sm p-0 mx-0"
                            style="width:50%; line-height:10px">
                            @php
                                $time =
                                    isset(
                                        $influencerCampaignDetail->influencer_request_accepts
                                            ->request_time_accept,
                                    ) &&
                                    $influencerCampaignDetail->influencer_request_accepts
                                        ->request_time_accept == 1
                                        ? $influencerCampaignDetail->influencer_request_accepts
                                                ->request_time + $influencerCampaignDetail->time
                                        : $influencerCampaignDetail->time;
                                $created_date = date(
                                    'Y-m-d H:i:s',
                                    strtotime($influencerCampaignDetail->created_at),
                                );
                                $updated_date = date(
                                    'Y-m-d H:i:s',
                                    strtotime($influencerCampaignDetail->updated_at),
                                );
                                $campaignDate = date(
                                    'Y-m-d H:i:s',
                                    strtotime(
                                        $created_date .
                                            ' + ' .
                                            $campaignRequestTime->request_time .
                                            ' days',
                                    ),
                                );
                                $date = date('Y-m-d H:i:s');
                                $seconds = strtotime($campaignDate) - strtotime($date);

                                $days = floor($seconds / 86400);
                                if ($days < 3 && $days >= 0) {
                                    $hours = floor(($seconds - $days * 86400) / 3600);
                                    $minutes = floor(
                                        ($seconds - $days * 86400 - $hours * 3600) / 60,
                                    );
                                    $seconds = floor(
                                        $seconds -
                                            $days * 86400 -
                                            $hours * 3600 -
                                            $minutes * 60,
                                    );
                                } else {
                                    'TIme Passed';
                                }
                            @endphp
                            <span class="timing" style="width: auto; font-size: 0.67em; line-height: normal; margin-bottom: 0 !important;" id="timerMobile{{ $influencerCampaignDetail->campaign_id }}"></span>
                            @if ($influencerCampaignDetail->social_posts == '')
                                @if ($influencerCampaignDetail->accept_request == $influencerCampaignDetail->total_influencer_count)
                                    <span class="smalltext" id="timer-info-mobile-{{ $influencerCampaignDetail->campaign_id }}" style="left: 0; bottom:0; text-align:left; font-size: 0.44em; margin-top: 10px;">Time to start your campaign</span>
                                @else
                                    <span class="smalltext" id="timer-info-mobile_{{ $influencerCampaignDetail->campaign_id }}" style="left: -20px; bottom:0; text-align:left; font-size: 0.50em; margin-top: 10px;">Wait for response from influencers</span>
                                @endif
                            @endif
                        </span>
                    </div>
                </div>
            </div>
            <div style="padding: 0px 8px;width: 30%;margin: 2% 0;">
                <button class="btn  btn-show-details"
                    style="width: 100% !important; height: auto; padding: .375rem .75rem; font-size: 0.5em;"
                    data-id="{{ $influencerCampaignDetail->id }}" target="popup" data-bs-toggle="modal"
                    data-bs-target="#campaign-details-{{ $influencerCampaignDetail->id }}">Show Details</button>
                <button class="btn  btn-cancel-new"
                    style="margin-top: 5px;width: 100%; font-size: 0.5em; height: auto; padding: .375rem .75rem;"
                    value="Cancel Campaign" target="popup" data-bs-toggle="modal"
                    data-bs-target="#cancelRequest{{ $influencerCampaignDetail->id }}">Cancel
                    Campaign</button>
                @if (
                    $influencerCampaignDetail->total_influencer_count == $influencerCampaignDetail->accept_request + ($influencerCampaignDetail->total_count - $influencerCampaignDetail->accept_request) &&
                        $influencerCampaignDetail->accept_request >= 1)
                    <button id="startCampaign"
                        style="margin-top: 5px;width: 100%; font-size: 0.5em; height: auto; padding: .375rem .75rem;"
                        onclick="startCampaign('{{ $influencerCampaignDetail->campaign_id }}');"
                        class="btn  btn-show-result1 startTImer{{ $influencerCampaignDetail->campaign_id }}">Start
                        Campaign</button>
                @else
                    @if ($date <= $campaignDate)
                        <button id="startCampaign"
                            style="margin-top: 5px;width: 100%; font-size: 0.5em; height: auto; padding: .375rem .75rem;"
                            onclick="startCampaign('{{ $influencerCampaignDetail->campaign_id }}');"
                            class="btn  btn-show-result1 startTImer{{ $influencerCampaignDetail->campaign_id }}"
                            disabled>Start Campaign</button>
                    @else
                        <button id="startCampaign"
                            style="margin-top: 5px;width: 100%; font-size: 0.5em; height: auto; padding: .375rem .75rem;"
                            onclick="startCampaign('{{ $influencerCampaignDetail->campaign_id }}');"
                            class="btn  btn-show-result1 startTImer{{ $influencerCampaignDetail->campaign_id }}">Start
                            Campaign</button>
                    @endif
                @endif
            </div>
        </div>
    </div>
</div>
