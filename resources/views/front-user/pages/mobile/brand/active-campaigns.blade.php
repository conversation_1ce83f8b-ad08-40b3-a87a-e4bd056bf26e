@php
    // Currently all timestamp calculation are happening based on the first influlencerRequestDetail
    // in the campaign. But there can be multiple influencers in one campaign. So it should calculate
    // the times based on when the brand paid and started the campaign.
    // TODO fix this soon!! **************************
    $influencerRequestDetail = $influencerCampaignDetail->influencer_request_details[0];

    $currentPhaseDurationInDays = $influencerRequestDetail->phase_duration;
    if ($influencerRequestDetail->influencer_request_accepts->is_time_extension_approved) {
        $currentPhaseDurationInDays += $influencerRequestDetail->influencer_request_accepts->requested_extension_duration;
    }

    // Get the current date and time
    $now = new DateTime('now', new DateTimeZone('UTC'));

    // TODO
    $campaignCreatedAt = new DateTime(
        $influencerRequestDetail->influencer_request_accepts->created_at,
        new DateTimeZone('UTC'),
    );

    // A new DateTime to track until when the timer will be shown
    // For brand side timer, it has nothing to do with if the influencer has submitted or not.
    // It is all about when the submit phase generally should end and when the review phase
    // generally should end.
    $submitPhaseEndsAt = clone $campaignCreatedAt;
    $submitPhaseEndsAt->modify("+10 days");

    $reviewPhaseEndsAt = clone $submitPhaseEndsAt;
    $reviewPhaseEndsAt->modify("+3 days");

    // Determine current phase based on timestamps
    $reviewTag = true;
    $timerRunsUntil = clone $now;
    $shouldTimerRun = false;
    if ($now < $submitPhaseEndsAt) {
        $influencerCampaignDetail->current_phase = 'submit';
        $reviewTag = false;
        $shouldTimerRun = true;
        $timerRunsUntil = clone $submitPhaseEndsAt;
    } elseif ($now >= $submitPhaseEndsAt && $now < $reviewPhaseEndsAt) {
        $influencerCampaignDetail->current_phase = 'review';
        $timerRunsUntil = clone $reviewPhaseEndsAt;
        $shouldTimerRun = true;
    } else {
        // Past review phase end date
        $influencerCampaignDetail->current_phase = 'finish';
    }

    // In milliseconds
    $distanceUntilTimerStopsMS = ($timerRunsUntil->getTimestamp() * 1000) - ($now->getTimestamp() * 1000);
@endphp

<div class="campaign-header">
    <div class="row" style="margin-left:0px;margin-right:0px;">
        <span class="btn-submit-phase mobile-view"
            id="phaseTagMobile{{ $influencerCampaignDetail->campaign_id }}"
            style="width: 100%; padding: 5px;display: flex;justify-content: space-between;cursor: pointer;"
            data-bs-toggle="modal"
            data-bs-target="#brandCampaignPhasesModal">
            <span style="text-align: left;" id="tagName">Submit Phase</span>
            <span style="text-align: right;">ID # {{ $influencerCampaignDetail->campaign_id }}</span>
        </span>
        <br>
    </div>
    <div class="header-section" style="padding: 0px;">
        <div style="display: flex; width: 100%;">
            <div style="width: 70%;">
                <h4 class="truncate-text" style="width: 80%; font-weight: 700;font-size: 15px; padding-top: 10px; padding-left: 4px;">
                    {{ $influencerCampaignDetail->compaign_title }}
                </h4>
                <div class="row">
                    <div>
                        <span class="badge px-1 mx-0" target="popup"
                            data-bs-toggle="modal"
                            data-bs-target="#showInfluencers{{ $influencerCampaignDetail->id }}"><img
                                width="15" height="15"
                                src="{{ asset('/assets/front-end/images/new/three_users.svg') }}">
                            {{ $influencerCampaignDetail->influencer_count }}
                            {{ $influencerCampaignDetail->influencer_count > 1 ? 'Influencers' : 'Influencer' }}</span>
                        <span class="badge px-1 mx-0">
                            <img width="15" height="15" src="{{ asset('/assets/front-end/images/new/survey_camp.svg') }}"> {{ $influencerCampaignDetail->post_type }}
                        </span>
                        <span class="badge px-1 mx-0">
                            <img width="15" height="15" src="{{ asset('/assets/front-end/images/icons/campaigns-' . $influencerCampaignDetail->media . '.svg') }}">
                            {{ $influencerCampaignDetail->advertising }}
                        </span>
                    </div>
                    <div>
                        <div style="display: flex; margin-top: 12px;">
                            <span class="text-success" style="font-size: 16px; padding-left: 5px;">
                                € {{ number_format(isset($influencerCampaignDetail->campaign_details->total_amount) ? $influencerCampaignDetail->campaign_details->total_amount : 0, 2) }}
                            </span>
                            {{-- Timing Starts --}}
                            <span class="submit-text-color" style="padding: 5px;">
                                <input type="hidden"
                                    class="campaign-title-{{ $influencerCampaignDetail->campaign_id }}"
                                    value="{{ $influencerCampaignDetail->compaign_title }}">
                                <input type="hidden"
                                    class="data-submit-phase-duration-day-{{ $influencerCampaignDetail->campaign_id }}"
                                    value="{{ $currentPhaseDurationInDays }}">
                                <input type="hidden"
                                    class="data-influencer-request-accept-date-{{ $influencerCampaignDetail->campaign_id }}"
                                    value="{{ $influencerRequestDetail->influencer_request_accepts->created_at }}">
                                @if ($shouldTimerRun)
                                    {{-- If still running, then show the timer --}}
                                    <span class="timing timerActive{{ $influencerCampaignDetail->campaign_id }}" style="display:block; background-color:transparent; font-size:16px"></span>
                                @else
                                    {{-- If campaign is already is finished --}}
                                    <span class="timing timerActive{{ $influencerCampaignDetail->campaign_id }}" style="display:block; background-color:transparent; font-size:16px">Finished</span>
                                @endif
                            </span>
                            {{-- Timing Ends --}}
                        </div>
                    </div>
                </div>
            </div>
            <div style="width: 30%; margin-top: 2%; margin-right: 1%;">
                <button class="btn  btn-show-details"
                    style="width: 100% !important; height:auto; font-size:8px; padding: .375rem .75rem;"
                    data-id="{{ $influencerCampaignDetail->id }}" target="popup"
                    data-bs-toggle="modal"
                    data-bs-target="#show-campaign-details-{{ $influencerCampaignDetail->id }}">
                    Show Details
                </button>
                @foreach ($influencerCampaignDetail->platform_invoices as $platform_invoice)
                    @if (empty($platform_invoice->receipt))
                    @php continue; @endphp
                    @endif
                    <a class="btn btn-show-fee-invoice" href="{{ $platform_invoice->receipt }}" target="_blank"
                        style="margin-top: 7px; width: 100% !important; height:auto; font-size:8px; padding: .375rem .75rem; border: solid 1px #AD80FF; color:#FFFFFF; background-color: #AD80FF;">
                        Fee Invoice
                    </a>
                    @php break; @endphp
                @endforeach
                <form method="post" action="{{ url('/finish-campaign') }}"
                    style="margin-bottom: 0px;"
                    data-parsley-validate>
                    @csrf
                    <input type="hidden" name="compaign_id" value="{{ $influencerCampaignDetail->campaign_id }}">
                    <button type="submit" style="width: 100% !important; font-size:8px;"
                        class="finish-camp btn btn-finish-campaign @if (!$influencerCampaignDetail->can_be_finished) btn-finish-disabled @endif"
                        @if (!$influencerCampaignDetail->can_be_finished) disabled @endif
                        value="Finish campaign"
                        style="margin-top: 5px;width: 150%;"> Finish Campaign
                    </button>
                </form>
                <div class="row" style="display: flex">
                    <div
                        style="display:flex; justify-content:center; margin-top: 5px;">
                        <span class="circlecamp-button dropdown-button collapsed"
                            style=" margin-bottom: 7px;" data-bs-toggle="collapse"
                            data-bs-target="#mobile-collapse{{ $influencerCampaignDetail->campaign_id }}"
                            aria-expanded="false"
                            aria-controls="mobile-collapse{{ $influencerCampaignDetail->campaign_id }}"
                            rowspan="2">
                            <i class="collapse-icon fas fa-chevron-up"
                                data-toggle="collapse"
                                data-target="#mobile-campaignDetails"
                                style="color: #AD80FF; border: 1px solid #AD80FF; border-radius: 50%;">
                            </i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<x-brand.mobile.active-campaigns-influencer-info-tab :influencerCampaignDetail="$influencerCampaignDetail" />

<script type="text/javascript">
    (function() {
        jQuery(document).ready(function($) {
            // Local variables scoped only to this function
            let influencerRequestDetail = @json($influencerRequestDetail);
            let activeCampaignsCount = @json($activeCampaignsCount);
            let reviewTag = @json($reviewTag);
            let shouldTimerRun = @json($shouldTimerRun);
            let distanceUntilTimerStops = @json($distanceUntilTimerStopsMS);
            let submitPhaseEndsAt = @json($submitPhaseEndsAt);
            let reviewPhaseEndsAt = @json($reviewPhaseEndsAt);
            let latestDistanceUntilTimerStops = distanceUntilTimerStops;
            let timerObj;
            
            // Debug logging
            console.log('MOBILE >>>>>>>>>>>>');
            console.log('Campaign ID:', influencerRequestDetail.campaign_id);
            console.log('Campaign Title:', influencerRequestDetail.campaign_title);
            console.log('influencerRequestDetail:', influencerRequestDetail);
            console.log('activeCampaignsCount:', activeCampaignsCount);
            console.log('reviewTag:', reviewTag);
            console.log('shouldTimerRun:', shouldTimerRun);
            console.log('distanceUntilTimerStops:', distanceUntilTimerStops);
            console.log('Current Phase:', @json($influencerCampaignDetail->current_phase));
            console.log('submitPhaseEndsAt:', submitPhaseEndsAt);
            console.log('reviewPhaseEndsAt:', reviewPhaseEndsAt);
            console.log('latestDistanceUntilTimerStops:', latestDistanceUntilTimerStops);
            console.log('***********************************');

            function updateTimer() {
                // After each turn, deduct 1000 ms (1 sec) from the latestDistanceUntilTimerStops,
                // so that we can update the UI with fancy timer view.
                latestDistanceUntilTimerStops -= (1000 * 60);

                var days = Math.floor(latestDistanceUntilTimerStops / (1000 * 60 * 60 * 24));
                var hours = Math.floor((latestDistanceUntilTimerStops % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                var minutes = Math.floor((latestDistanceUntilTimerStops % (1000 * 60 * 60)) / (1000 * 60));
                var seconds = Math.floor((latestDistanceUntilTimerStops % (1000 * 60)) / 1000);

                if (latestDistanceUntilTimerStops > 0) {
                    if (days < 10 && days != 0) {
                        days = '0' + days;
                    }

                    if (hours < 10) {
                        hours = '0' + hours;
                    }

                    if (minutes < 10) {
                        minutes = '0' + minutes;
                    }

                    if (seconds < 10) {
                        seconds = '0' + seconds;
                    }

                    $(".timerActive" + influencerRequestDetail.campaign_id).html(days + " d " + hours + " h " + minutes + " min");
                } else {
                    days = '0';
                    hours = '0';
                    minutes = '0';
                    seconds = '0';

                    if (latestDistanceUntilTimerStops <= 0) {
                        if (timerObj) {
                            clearInterval(timerObj);
                        }

                        $(".timerActive" + influencerRequestDetail.campaign_id).html("Finished");

                        if (reviewTag) {
                            $('#phaseTagMobile' + influencerRequestDetail.campaign_id).html('Review Phase');
                            $('#phaseTagMobile' + influencerRequestDetail.campaign_id).removeClass('btn-submit-phase');
                            $('#phaseTagMobile' + influencerRequestDetail.campaign_id).addClass('btn-review-phase');
                        }

                        if (!influencerRequestDetail['refund_reason'] && !influencerRequestDetail['is_complained'] && !influencerRequestDetail['is_paused']) {
                            $.ajax({
                                url: "{{ URL::to('/review-phase-closed') }}",
                                method: 'POST',
                                data: {
                                    '_token': "{{ csrf_token() }}",
                                    'rowId': influencerRequestDetail['id']
                                }
                            }).done(function(data) {
                                if ($('#btn-brand-review-submitted-content-' + influencerRequestDetail['id']).length) {
                                    $('#btn-brand-review-submitted-content-' + influencerRequestDetail['id']).attr('disabled', true);
                                }
                            }).fail(function() {
                                // TODO what to do when it fails?
                            });
                        }
                    }
                }
            }

            // Initialize the timer and UI
            updateTimer();

            if (reviewTag) {
                $('#phaseTagMobile' + influencerRequestDetail.campaign_id).html('Review Phase');
                $('#phaseTagMobile' + influencerRequestDetail.campaign_id).removeClass('btn-submit-phase');
                $('#phaseTagMobile' + influencerRequestDetail.campaign_id).addClass('btn-review-phase');
            }

            if (activeCampaignsCount > 0 && shouldTimerRun) {
                timerObj = setInterval(updateTimer, 1000 * 60); // Runs every minute
            }
        });
    })(); // Immediately invoke the function
</script>
