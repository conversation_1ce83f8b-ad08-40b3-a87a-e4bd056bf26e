<div class="campaign-header">
    @if ($influencerRequestData->refund_reason != '')
        @if ($influencerRequestData->refund_reason == 'Complaint Confirmed')
            <div class="row" style="margin-top: -18px;margin-left:0px;margin-right:0px;">
                <span class="btn-finished-phase"
                    style="width: 100%; padding: 5px;display: flex;justify-content: space-between;"><span
                        style="text-align: left;">Accepted</span><span
                        style="text-align: right;">ID #
                        {{ $influencerRequestData->compaign_id }}</span></span><br>
            </div>
        @elseif(array_key_exists($influencerRequestData->refund_reason, $closeIconsReasons))
            <div class="row" style="margin-top: -18px;margin-left:0px;margin-right:0px;">
                <span class="btn-rejected-phase"
                    style="width: 100%; padding: 5px;display: flex;justify-content: space-between;"><span
                        style="text-align: left;">{{ $closeIconsReasons[$influencerRequestData->refund_reason] }}</span><span
                        style="text-align: right;">ID #
                        {{ $influencerRequestData->compaign_id }}</span></span><br>
            </div>
        @endif
    @else
        @if (isset($influencerRequestData->influencer_request_accepts->request) && $influencerRequestData->influencer_request_accepts->request == '1')
            <div class="row" style="margin-top: -18px;margin-left:0px;margin-right:0px;">
                <span class="btn-finished-phase"
                    style="width: 100%; padding: 5px;display: flex;justify-content: space-between;"><span
                        style="text-align: left;">Accepted</span><span
                        style="text-align: right;">ID #
                        {{ $influencerRequestData->compaign_id }}</span></span><br>
            </div>
        @else
            <div class="row" style="margin-top: -18px;margin-left:0px;margin-right:0px;">
                <span class="btn-rejected-phase"
                    style="width: 100%; padding: 5px;display: flex;justify-content: space-between;"><span
                        style="text-align: left;">Rejected</span><span
                        style="text-align: right;">ID #
                        {{ $influencerRequestData->compaign_id }}</span></span><br>
            </div>
        @endif
    @endif
    <div class="header-section" style=" margin-bottom: 30px">
        <div class="row" style="padding-left:20px; width:100%;">
            <div class="col-9">
                <h4 class="row truncate-text" style="font-weight: 700;font-size: 15px; padding-top: 10px; margin-left: -14px;">
                    {{ $influencerRequestData->compaign_title }}
                </h4>
                <div class="row" style="width:100%">
                    <span class="col-auto"
                        style="font-size: 6px !important; padding: 0 5px 0 0;"><img
                            src="{{ asset('/assets/front-end/images/new/three_users.svg') }}"
                            style="height: 15px;width:15px">
                        {{ $influencerRequestData->user->first_name }} {{ $influencerRequestData->user->last_name }}</span>
                    <span class="col-auto"
                        style="font-size: 6px !important; padding: 0 5px 0 0;"><img
                            src="{{ asset('/assets/front-end/images/new/survey_camp.svg') }}"
                            alt="" style="height: 15px;width:15px">
                        {{ $influencerRequestData->post_type }}</span>
                    <span class="col-auto"
                        style="font-size: 6px !important; padding: 0 5px 0 0;"><img
                            src="{{ asset('/assets/front-end/images/icons/campaigns-' . $influencerRequestData->media . '.svg') }}"
                            alt="" style="height: 15px;width:15px">
                        {{ $influencerRequestData->advertising }}</span>
                </div>
                <div class="row">
                    <span class="text-success" style="font-size: 16px;font-weight: 700;">
                        € {{ number_format($influencerRequestData->total_amount ?? 0, 2) }} </span>
                </div>
            </div>
            <div class="col-3"
                style="display:flex; align-items:center; justify-content:center; position: right; padding: 0 0px; margin: 0 0px;">
                <button class="btn  btn-show-details "
                    style="font-size:7px; padding: .375rem .75rem; height:auto;"
                    target="popup" data-bs-toggle="modal"
                    data-bs-target="#requestForm{{ $influencerRequestData->id }}">Show Details</button>
            </div>
        </div>
    </div>
</div>