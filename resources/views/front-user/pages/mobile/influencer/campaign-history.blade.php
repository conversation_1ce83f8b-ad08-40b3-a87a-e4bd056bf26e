<div class="campaign-header">
    <div class="row" style="margin-top: -18px;margin-left:0px;margin-right:0px;">
        @if ($influencerRequestDetail->review == '1' || $influencerRequestDetail->finish == '1')
            <span class="btn-finished-phase"
                style="width: 100%; padding: 5px;display: flex;justify-content: space-between;">
                <span style="text-align: left;">Completed</span>
                <span style="text-align: right;">ID # {{ $influencerRequestDetail->compaign_id }}</span>
            </span>
        @else
            <span class="btn-rejected"
                style="width: 100%; padding: 5px;display: flex;justify-content: space-between;">
                <span style="text-align: left;">Cancelled</span>
                <span style="text-align: right;">ID # {{ $influencerRequestDetail->compaign_id }}</span>
            </span>
        @endif
    </div>
    <div class="header-section">
        <div class="row" style="padding-left:20px; width:100%">
            <div class="col-9">
                <h4 class="row truncate-text" style="font-weight: 700;font-size: 15px; padding-top: 10px; margin-left: -14px;">
                    {{ $influencerRequestDetail->compaign_title }}
                </h4>
                <div class="row" style="width: 100%;">
                    <span class="col-auto" style="font-size: 6px !important; padding: 0 5px 0 0;">
                        <img src="{{ asset('/assets/front-end/images/new/brand_camp.svg') }}"
                            width="12" height="12">
                        {{ $influencerRequestDetail->user->first_name }} {{ $influencerRequestDetail->user->last_name }}
                    </span>&nbsp;
                    <span class="col-auto" style="font-size: 6px !important; padding: 0 5px 0 0;">
                        <img
                            src="{{ asset('/assets/front-end/images/new/survey_camp.svg') }}"
                            width="12" height="12">
                            {{ $influencerRequestDetail->post_type }}
                    </span>&nbsp;
                    <span class="col-auto" style="font-size: 6px !important; padding: 0 5px 0 0;">
                        <img src="{{ asset('/assets/front-end/images/icons/campaigns-' . $influencerRequestDetail->media . '.svg') }}"
                            width="12" height="12">
                            {{ $influencerRequestDetail->advertising }}
                    </span>
                </div>
                <div class="row" style="">
                    <span
                        class="@if ($influencerRequestDetail->review == '1' || $influencerRequestDetail->finish == '1') text-success @elseif($influencerRequestDetail->status == 'Cancelled' || $influencerRequestDetail->refund_reason == 'Cancelled')  text-gray @endif"
                        style="font-size: 16px;font-weight: 700; width:auto;">
                        @php
                            // Use modern pricing system (current_price completely deprecated 2025-01-29)
                            $influencerPrice = $influencerRequestDetail->cash_out_amount ?? 0;
                        @endphp
                        € {{ number_format($influencerPrice, 2) }}
                    </span>
                    <span style="font-size: 9px; width:auto; display:flex; align-items:center">
                        <img width="10"
                            src="{{ asset('/assets/front-end/images/new/likes_camp.svg') }}">&nbsp;
                        {{ isset($influencerRequestDetail->like) ? $influencerRequestDetail->like : 0 }} &nbsp;
                        <img width="10"
                            src="{{ asset('/assets/front-end/images/new/shares_camp.svg') }}">&nbsp;
                        {{ isset($influencerRequestDetail->share) ? $influencerRequestDetail->share : 0 }} &nbsp;
                        <img width="10"
                            src="{{ asset('/assets/front-end/images/new/view_camp.svg') }}">&nbsp;
                        {{ isset($influencerRequestDetail->view) ? $influencerRequestDetail->view : 0 }} &nbsp;
                        <img width="10"
                            src="{{ asset('/assets/front-end/images/new/comments_camp.svg') }}">&nbsp;
                        {{ isset($influencerRequestDetail->comment) ? $influencerRequestDetail->comment : 0 }} &nbsp;
                    </span>
                </div>
            </div>
            <div class="col-3" style="display:flex; align-items:center; justify-content:center; flex-direction:column; padding: 0 0px; margin: 0 0px;">
                <button class="btn btn-show-details" target="popup"
                    data-bs-toggle="modal"
                    style="width: 100% !important; padding: 0; margin: 2px 0; font-size: 10px"
                    data-bs-target="#requestForm{{ $influencerRequestDetail->id }}">Show
                    Details</button>
                @if ($influencerRequestDetail->review == 1 && isset($influencerRequestDetail->influencer_request_accepts->rating_reviews))
                    <button
                        class="btn btn-show-result1 btn-influencer-show-results-mobile startTImer{{ $influencerRequestDetail->compaign_id }}"
                        data-bs-toggle="modal"
                        data-bs-target="#influencer-show-results-{{ $influencerRequestDetail->id }}"
                        style="width: 100% !important; padding: 0; margin: 2px 0; font-size: 10px">Show Results
                    </button>
                    @if (!empty($influencerRequestDetail->invoices->receipt))
                        <a class="btn btn-show-my-invoice" href="{{ $influencerRequestDetail->invoices->receipt }}" target="_blank"
                            style="width: 100% !important; padding: 0; margin: 2px 0; font-size: 10px; border: solid 1px #AD80FF; color: white; background-color:#AD80FF;">My Invoice
                        </a>
                    @endif
                    <div class="star-rating" style="font-size: 13px" data-bs-toggle="modal"
                        data-bs-target="#reviewRatingPopup{{ $influencerRequestDetail->influencer_request_accepts->rating_reviews->id }}">
                        <?php
                        $count = 5 - $influencerRequestDetail->influencer_request_accepts->rating_reviews->rating;
                        ?>
                        @for ($i = 0; $i < $influencerRequestDetail->influencer_request_accepts->rating_reviews->rating; $i++)
                            <span class="star filled">★</span>
                        @endfor
                        @for ($j = 0; $j < $count; $j++)
                            <span>★</span>
                        @endfor
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>