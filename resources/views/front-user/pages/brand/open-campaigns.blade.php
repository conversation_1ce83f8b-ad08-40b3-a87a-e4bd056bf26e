{{-- To see the contents of the varaible $influencerCampaignDetail, check OpenCampaignsController::buildOpenCampaignsPageForBrand() --}}

@extends('front-user.layouts.master_user_dashboard')

@section('content')
    <style>
        span.smalltext {
            position: relative !important;
            text-align: left;
            left: 11px;
            bottom: 0px;
        }

        .submit-text-color {
            padding-top: 13px;
        }

        .campaign-list {
            padding-left: 0px;
            padding-right: 0px;
        }

        .campaign_vat_price {
            margin-left: 100px;
        }

        .order-summary {
            margin-left: 0px !important;
            margin-right: 0px !important;
        }

        .total-container {
            margin-left: 0px !important;
            margin-right: 0px !important;
        }

        .summary-column {
            width: 57%;
        }

        .summary-item {
            margin-bottom: 0px;
        }

        .summary-left .summary-item {
            padding-top: 21px;
        }
    </style>
    <section id="campaignForm">
        <h1 class="section-heading"><span>Open Campaigns</span></h1>

        <div class="row">
            <div class="offset-md-8 col-md-4 col-sm-6" style="padding-right: 20px; padding-bottom: 10px">
                <a href="{{ url('campaign-history') }}" class="btn btn-outline-secondary top-right-button btn-history"
                    style="float: right;"><img src="{{ asset('/assets/front-end/images/new/history.svg') }}"> Campaign History</a>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12 new_card_row">
                @php
                    $count = 0;
                    $amount = 0;
                @endphp
                @if (isset($influencerCampaignDetails))
                    @foreach ($influencerCampaignDetails as $influencerCampaignDetail)
                        @if ($influencerCampaignDetail->accept_request > 0 || $influencerCampaignDetail->review != '2' && $influencerCampaignDetail->finish != '0')
                            @php $count++; @endphp
                            {{-- Desktop View --}}
                            <div class="campaign-card desktop-view">
                                @include('front-user.pages.desktop.brand.open-campaigns', ['influencerCampaignDetail' => $influencerCampaignDetail])
                            </div>
                            {{-- Mobile View --}}
                            <div class="campaign-card mobile-view" style="display: none;">
                                @include('front-user.pages.mobile.brand.open-campaigns', ['influencerCampaignDetail' => $influencerCampaignDetail])
                            </div>

                            {{-- Brand Open Campaign Modals --}}
                            <x-modals.brand.open-campaigns.influencer-tasks :influencerCampaignDetail="$influencerCampaignDetail" />
                            <x-modals.brand.open-campaigns.manage-influencers :influencerCampaignDetail="$influencerCampaignDetail" :campaignRequestTime="$campaignRequestTime" />
                            <x-modals.brand.open-campaigns.configure-request :influencerCampaignDetail="$influencerCampaignDetail" />
                            <x-modals.brand.open-campaigns.campaign-details :influencerCampaignDetail="$influencerCampaignDetail" :campaignRequestTime="$campaignRequestTime" />
                            <x-modals.brand.open-campaigns.cancel-request :influencerCampaignDetail="$influencerCampaignDetail" />
                        @endif
                    @endforeach
                @endif
                @if ($count == 0)
                    <div class="no-data-div">
                        <img src="{{ asset('/assets/front-end/images/icons/icon-no-data-image.png') }}"
                            alt="">
                        <div class="no-data-contant">
                            You have no open campaigns at the moment.
                        </div>
                        <div class="">
                            <a href="{{ url('create-campaign') }}" class="button-ccg">Create campaign</a>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <div id="paymentPopup"></div>

        <div class="loaderss" id="pageLoader">
            <img src="{{ asset('/assets/front-end/images/loading-loading-forever.gif') }}" alt="">
        </div>

        @if (isset($influencerCampaignDetails))
            @foreach ($influencerCampaignDetails as $influencerCampaignDetail)
                @if ($influencerCampaignDetail->review != '2')
                    @php
                        $influencerRequestDetails = App\Models\InfluencerRequestDetail::where('compaign_id', $influencerCampaignDetail->campaign_id)
                            ->where('status', null)
                            ->get();
                    @endphp

                    @foreach ($influencerRequestDetails as $influencerRequestDetail)
                        {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                        @if (empty($influencerRequestDetail->influencerdetails))
                            @php continue; @endphp
                        @endif
                        @php
                            $influencerSocialLinkDetail = App\Models\SocialConnect::where('user_id', $influencerRequestDetail->influencerdetails->user->id)
                                ->where('media', $influencerCampaignDetail->media)
                                ->first();
                        @endphp
                        @include('front-user.pages.market-step-detail-influencer')
                    @endforeach
                @endif
            @endforeach
        @endif
    </section>
@endsection

@section('script_links')
    <script src="https://js.stripe.com/v3/"></script>
@endsection

@section('script_codes')
    <script type="text/javascript">
        $(document).ready(function() {
            var hash = window.location.hash;
            if (hash != '') {
                $('#myTab button[data-bs-target="#active-campaigns"]').tab('show');
            }
        });

        function createTimer(itemD, index) {
            itemD['campaign_id'] = itemD['campaign_id'];
            var campaignDate = new Date(itemD['created_at']);
            campaignDate.setDate(campaignDate.getDate() + parseInt(@json($campaignRequestTime->request_time)));

            var now1 = new Date();

            // Update the count down every 1 second
            let timerObjs = {};
            timerObjs[itemD['campaign_id']] = setInterval(function() {
                // Get today's date and time
                var now = new Date().getTime();

                // Find the distance between now and the count down date
                if (now1 > campaignDate) {
                    // Add 3 more days to the campaign date for payment phase
                    campaignDate.setDate(campaignDate.getDate() + 3);
                }
                var distance = campaignDate - now;
                // Time calculations for days, hours, minutes and seconds
                var days = Math.floor(distance / (1000 * 60 * 60 * 24));
                var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                var seconds = Math.floor((distance % (1000 * 60)) / 1000);

                if (days < 10 && days != 0) {
                    days = '0' + days;
                }

                if (hours < 10) {
                    hours = '0' + hours;
                }

                if (minutes < 10) {
                    minutes = '0' + minutes;
                }

                if (seconds < 10) {
                    seconds = '0' + seconds;
                }

                $("#timer" + itemD['campaign_id']).text(days + " d " + hours + " h " + minutes + " min");
                $("#timer1" + itemD['campaign_id']).text(days + " d " + hours + " h " + minutes + " min");
                $("#timer2" + itemD['campaign_id']).text(days + " d " + hours + " h " + minutes + " min");
                $("#timerMobile" + itemD['campaign_id']).text(days + " d " + hours + " h " + minutes + " min");

                // If the count down is over, write some text
                if (distance < 0) {
                    clearInterval(timerObjs[itemD['campaign_id']]);
                    $("#timer" + itemD['campaign_id']).text("EXPIRED");
                    $("#timer1" + itemD['campaign_id']).text("EXPIRED");
                    $("#timer2" + itemD['campaign_id']).text("EXPIRED");
                    $("#timerMobile" + itemD['campaign_id']).text("EXPIRED");
                    $(".startTImer" + itemD['campaign_id']).attr('disabled', 'disabled');

                    $("#timer-info-" + itemD['campaign_id']).html("");
                    $("#timer-info-mobile-" + itemD['campaign_id']).html("");
                }
            }, 1000);
        }
        
        let influencerCampaignDetails = @json($influencerCampaignDetails);
        if (influencerCampaignDetails.length > 0) {
            influencerCampaignDetails.forEach(createTimer);
        }

        function requestSubmitbutton(id) {
            $("#pageLoader").show()
            $.ajax({
                url: "{{ URL::to('/review-influencer-submission') }}/" + id,
                method: 'GET',
            }).done(function(data) {
                $('.campHistory').html(data);
                $('#reviewRating' + id).modal('show');
                $("#pageLoader").hide()
            }).fail(function() {
                $("#pageLoader").hide()
            });
        }


        function finishCampaign(id) {
            $("#pageLoader").show()
            $.ajax({
                url: "{{ URL::to('/finish-campaign') }}/" + id,
                method: 'GET',
            }).done(function(data) {
                $('.campHistory').html(data);
                $('#reviewRating' + id).modal('show');
                $("#pageLoader").hide()
            }).fail(function() {
                $("#pageLoader").hide()
            });
        }

        $(document).on("click", ".openconfigure", function() {
            var popupid = $(this).attr("data-popup-id");

            $("#configure" + popupid).on('shown.bs.modal', function() {
                $(document).on('change', '.Checkall', function() {
                    var i_id = $(this).data('i_id');
                    var id = $(this).attr('id').replace('selectUser', '');
                    $("#selectInfluencer" + i_id + '_' + id).val(id);
                    // current_price calculations removed - field completely deprecated 2025-01-29
                    // Price calculations now handled by modern InfluencerPrice system

                    if (this.checked) {
                        $(this).closest(".checkboxAllCheck").hide();
                        $(this).closest(".checkboxAllCheck").prev().show();
                    }
                });

                $(document).on('click', '.remove', function() {
                    var id = $(this).attr('id').replace('selectUser', '');

                    // current_price calculations removed - field completely deprecated 2025-01-29
                    // Price calculations now handled by modern InfluencerPrice system

                    $("#selectInfluencer" + i_id + '_' + id).val(' ');
                    $(this).closest("input[name='selectInfluencer']").val(' ');
                    $(this).closest("span").hide();
                    $(this).closest("span").next(".checkboxAllCheck").show();
                    $(this).closest("span").next(".checkboxAllCheck").find("input").prop("checked", false);
                });
            });
        });

        function reviewTask(slug, id) {
            var review_count = 0;

            var task1 = $('input[name="task1"]:checked').val();
            var task2 = $('input[name="task2"]:checked').val();
            var task3 = $('input[name="task3"]:checked').val();

            if (task1 != undefined) {
                var review_count = parseInt(review_count) + 1;
            }

            if (task2 != undefined) {
                var review_count = parseInt(review_count) + 1;
            }

            if (task3 != undefined) {
                var review_count = parseInt(review_count) + 1;
            }

            var task = parseInt(task1) + parseInt(task2) + parseInt(task3);
            if (task == 3) {
                $('#review' + id).prop("disabled", false);
                $('#complaint' + id).hide();
                $('#complaint' + id).prop("disabled", true);
            } else {
                if (review_count == 3) {
                    $('#complaint' + id).show();
                    $('#complaint' + id).prop("disabled", false);
                    $('#review' + id).prop("disabled", true);
                }
            }
        }

        function openPayment(name, amt) {
            $('#payName').val(name);
            $('#payAmount').val(amt);
            $('#amtPay').html(amt);
        };

        $(document).ready(function() {
            $('.Checkall').change(function() {
                $('.Checkall').val(this.checked);
            });
        });

        function requestCancelbutton(campaign_id) {
            $("#pageLoader").show();

            $.ajax({
                url: "{{ URL::to('/campaign-cancel') }}/" + campaign_id,
                method: 'GET',
            }).done(function(data) {
                $("#pageLoader").hide();
                toastr.success('Campaign cancelled successfully');
                setTimeout(function() {
                    window.location.reload();
                }, 1000);
            }).fail(function() {
                $("#pageLoader").hide();
            });
        }

        function startCampaign(id) {
            $('#paymentPopup').html('');
            $.ajax({
                url: "{{ URL::to('/payment-detail') }}/" + id,
                method: 'GET',
            }).done(function(data) {
                $('#paymentPopup').html(data);
                $('#startCampaign' + id).modal('show');

                $(document).on("click", ".continue-link", function() {
                    $("#campaignOverviewTab").removeClass("active");
                    $("#general-information").removeClass("show active");
                    $("#makePaymrntTab").addClass("active");
                    $("[aria-labelledby='makePaymrntTab']").addClass("show active");
                    $("#campaignOverviewTab").removeClass("active");
                })

                $(document).on("click", ".campaignOverviewTab", function() {
                    $("#campaignOverviewTab").addClass("active");
                    $("#general-information").addClass("show active");
                    $("#makePaymrntTab").removeClass("active");
                    $("[aria-labelledby='makePaymrntTab']").removeClass("show active");
                    $("#campaignOverviewTab").addClass("active");
                })

            }).fail(function() {
                $("#pageLoader").hide();
            });
        }

        $(document).on("click", "[value='Update Influencers']", function() {
            $("#pageLoader").show();
        });
        $(document).on("click", ".add_new_influencer", function() {
            var total_influencers = parseInt($('#acceptedRequest').val()) + parseInt($('#not_responded').val()) + parseInt($('#rejected_inf').val());
            if ($(this).prop('checked') && total_influencers >= 50) {
                $(this).prop('checked', false);
                toastr.info("Maximum number of influencer limit reached. ");
            }
        });
    </script>
@endsection

<x-modals.brand.campaign-phases />
