<div class="modal fade influncerdetailpopup" id="influncerdetailpopup{{ $influencerDetail->id }}"
    data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="payoutconfirmpopupLabel" aria-hidden="true">
    @php
        $fullInfluencerDetail = \App\Models\InfluencerDetail::where(
            'user_id',
            $influencerDetail->user_id,
        )->first();
        $hashTags = \App\Models\Hashtag::where('user_id', $influencerDetail->user_id)->get();
        $userData = \App\Models\User::where('id', $influencerDetail->user_id)->first();
        if ($userData->trophy == '') {
            $userData->trophy = 'Bronze';
        }

        $influencerCategory = \App\Models\Category::where(
            'id',
            $fullInfluencerDetail->category_id,
        )->first();
        $country = \App\Models\Country::where('id', $userData->country)->first();
    @endphp
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background: #AD80FF;">
                <button class="close-popup-button" type="button"
                    data-bs-dismiss="modal"><img
                        src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}"
                        alt=""></button>
                <div class="modal-header-outer mb-3">
                    <div class="modal-header-content">
                        <div class="header-influencer-image">
                            <img src="{{ asset('storage/' . $media->picture) }}" alt="">
                        </div>
                        <div class="header-influencer-flage">
                            <img src="{{ asset('/assets/front-end/images/icons/icon-flage.svg') }}" alt="">
                        </div>
                        <div class="header-influencer-trophy">
                            <img src="{{ asset('/assets/front-end/images/icons/trofy-' . $trophy . '.svg') }}" alt="">
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-body">

                <div class="modal-header-username d-flex justify-content-center mt-4"
                    style="color: black; font-size: 22px;">
                    {{ $influencerDetail->username }}
                </div>
                <div class="d-flex justify-content-center mt-0"
                    style="color: black; font-size: 15px;font-family: Mulish; opacity: 0.7;">
                    {{ $fullInfluencerDetail->influencer_type }} |
                    {{ $influencerCategory->name }}
                </div>
                <div class="row justify-content-center mt-3"
                    style="color: black; font-size: 15px;font-family: Mulish;">
                    <span class="col-md-3 text-center">Age of the Follower:
                        {{ $fullInfluencerDetail->ages }}</span>
                    <span class="col-md-3 text-center">Country:
                        {{ $country->name }}</span>
                    <span class="col-md-3 text-center">Content Language:
                        {{ $fullInfluencerDetail->content_language }}</span>
                </div>
                <div class="row justify-content-center mt-4">
                    @foreach ($hashTags as $hashTag)
                        <div class="col-md-2 text-center mx-3 my-2"
                            style="color: #AD80FF; border: 1px solid #AD80FF; padding: 0.375em 0.85em; border-radius: 25px;">
                            #{{ $hashTag->tags }}
                        </div>
                    @endforeach
                </div>
                <div class="popup-social-section d-flex justify-content-center" style="padding: 25px 0;">
                    @php $medias = \App\Models\SocialConnect::where('user_id',$influencerDetail->user_id)->get(); @endphp
                    @foreach ($medias as $socials)
                        <div class="popup-social-image @if ($socials->media == $influencerDetail->media) green-circle @endif ">
                            <a href="{{ $socials->url }}" target="_blank">
                                <img src="{{ asset('/') }}/assets/front-end/images/icons/new_social_media_{{ $socials->media }}.png" alt="">
                                <span>{{ $socials->followers }} Follower</span>
                            </a>
                        </div>
                    @endforeach
                </div>
                <div class="latest-campaigns">
                    <div class="latest-campaigns-title">Latest Campaigns</div>
                    <div class="latest-campaigns-box-outer">
                        @php
                            $influencerData = \App\Models\InfluencerRequestDetail::join(
                                'influencer_details',
                                'influencer_request_details.influencer_detail_id',
                                '=',
                                'influencer_details.id',
                            )
                                ->leftjoin(
                                    'influencer_request_accepts',
                                    'influencer_request_accepts.influencer_request_detail_id',
                                    '=',
                                    'influencer_request_details.id',
                                )
                                ->leftjoin(
                                    'rating_reviews',
                                    'influencer_request_accepts.id',
                                    '=',
                                    'rating_reviews.influencer_request_accept_id',
                                )
                                ->leftjoin(
                                    'users',
                                    'users.id',
                                    '=',
                                    'influencer_details.user_id',
                                )
                                ->select(
                                    'influencer_request_details.*',
                                    'users.first_name',
                                    'users.last_name',
                                    'rating_reviews.rating',
                                    'rating_reviews.review',
                                )
                                ->where(
                                    'influencer_request_details.influencer_detail_id',
                                    $influencerDetail->i_id,
                                )
                                ->orderBy('influencer_request_details.id', 'desc')
                                ->get()
                                ->take(3);
                        @endphp

                        @if (isset($influencerData))
                            @foreach ($influencerData as $data)
                                <div class="latest-campaigns-box">
                                    <div class="latest-campaigns-name">
                                        {{ $data->first_name }} {{ $data->last_name }}
                                    </div>
                                    <div class="latest-campaigns-date">
                                        {{ date('d-m-Y', strtotime($data->created_at)) }}
                                    </div>
                                    <div class="latest-campaigns-stars">
                                        @if ($data->rating != '')
                                            @if ($data->rating == 0)
                                                <img src="{{ asset('/assets/front-end/images/star-blank.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-blank.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-blank.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-blank.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-blank.png') }}" alt="">
                                            @endif
                                            @if ($data->rating == 1)
                                                <img src="{{ asset('/assets/front-end/images/star-fill.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-blank.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-blank.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-blank.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-blank.png') }}" alt="">
                                            @endif
                                            @if ($data->rating == 2)
                                                <img src="{{ asset('/assets/front-end/images/star-fill.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-fill.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-blank.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-blank.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-blank.png') }}" alt="">
                                            @endif
                                            @if ($data->rating == 3)
                                                <img src="{{ asset('/assets/front-end/images/star-fill.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-fill.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-fill.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-blank.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-blank.png') }}" alt="">
                                            @endif
                                            @if ($data->rating == 4)
                                                <img src="{{ asset('/assets/front-end/images/star-fill.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-fill.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-fill.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-fill.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-blank.png') }}" alt="">
                                            @endif
                                            @if ($data->rating == 5)
                                                <img src="{{ asset('/assets/front-end/images/star-fill.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-fill.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-fill.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-fill.png') }}" alt="">
                                                <img src="{{ asset('/assets/front-end/images/star-fill.png') }}" alt="">
                                            @endif
                                        @endif
                                    </div>
                                    <div class="latest-campaigns-text">
                                        @if ($data->review != '')
                                            “{{ $data->review }}”
                                        @else
                                            No Review
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="nodata-text">
                                The influencer has not yet run a campaign. Take the opportunity
                                and be the first to launch a successful campaign with this influencer.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>