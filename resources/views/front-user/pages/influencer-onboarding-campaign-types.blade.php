<div class="mobile-step-detail">
    <div class="steps-cont">
        STEP 05
    </div>
    <div class="steps-which">
        Campaign types
    </div>
</div>

<form action="{{ url('/save-influencer-form') }}" method="post" data-parsley-validate data-parsley-excluded=[disabled] id="draft3">
    @csrf
    <input type="hidden" name="advertising_method" value="true">
    <h3 align="center" style="color: #AD80FF !important;">Brands can request you only for the activated campaign types</h3>
    <div class="informationDiv advertisingMethod1">
<div class="connectPrising advertisingSelect advertisingMethod1" id="advertisingMethod1">  
    @if ($user->activate != '2')
        <input type="hidden" name="collection" value="">  
    @else
        <input type="hidden" name="collection" value="3">  
    @endif
    <div id="showError" style="display: none;" >Please select one type of ad type</div>
    <div class="d-flex mediabox">
       
    </div>
@php  $type_count =0; @endphp
    <div class="advertising-methods-outer d-flex justify-content-center">
         
        <div class="new-camping d-inline-flex align-items-center overflow-auto mt-5">
 

        @php
            $userData =  \App\Models\User::where('id',Auth::user()->id)->first(); 
            if($userData->trophy == ''){ $userData->trophy = 'Bronze'; }
            $pricingData = \App\Models\AdminGamification::where('select_type','Pricing & Rank')->where('type',$userData->trophy)->first(); 
            $pricingNext = \App\Models\AdminGamification::where('id',@$pricingData->id+1)->first();
            $advertising_method_check = \App\Models\AdvertisingMethodNewPrice::where('user_id',Auth::id())->get();

            $sm_campaigns_boost= 0;
            foreach($sm_campaigns as $campaign){
                if($campaign->type == 'Boost_me'){
                    $sm_campaigns_boost++;
                } 
            }

            $sm_campaigns_video = 0;
            foreach($sm_campaigns as $campaign){
                if($campaign->type == 'Reaction_video'){
                    $sm_campaigns_video++;
                } 
            }


            $sm_campaigns_survey= 0;
            foreach($sm_campaigns as $campaign){
                if($campaign->type == 'Survey'){
                    $sm_campaigns_survey++;
                } 
            } 
        @endphp  
            <div class="camping-box   @foreach($advertising_method_check as $method)
                        @if(isset($method->type)  )
                        {{isset($method->type)?(($method->type == 'Boost me')?'open':''):'open'}} 
                        @endif 
                    @endforeach
                      {{!isset($advertising_method_check[0])?'open':''}} 
                    ">
                    <!-- @if($sm_campaigns_boost == 0) disabled @endif -->
                <div class="cbInput">
                    <!-- <input type="radio" class="cbInput-radio" name="cb-input"> -->
                    <input type="checkbox" value="Boost me" name="type_boost" class="cbInput-radio" id="type-post-content-boost"
                    @foreach($advertising_method_check as $method)
                        @if(isset($method->type) )
                        {{isset($method->type)?(($method->type == 'Boost me')?'checked':''):'checked'}} 
                        @endif 
                    @endforeach 
                     {{!isset($advertising_method_check[0])?'checked':''}} 
                     >

                    <label class="cbInputLabel">

                    </label>
                </div>
                <div class="cb-image">
                    <img src="{{ asset('/assets/front-end/images/icons/icon-postType-boost-black.svg') }}" alt="">
                </div>
                <div class="cb-title">
                    Boost Me
                </div>
                <div class="cb-sublabel">
                    Boost the content from your brand
                </div>
                <div class="cb-socialmedia">                    
                    <img src="{{ asset('/assets/front-end/images/icons/icon-cb-instagram.svg') }}"  class="{{ (isset($social_connect_instagram))?'active':'' }} mediaCheck  {{ (isset($social_connect['media']) && $social_connect['media'] == 'instagram')?'checked':'' }}" alt=""  data-media="instagram"   >
                    <img src="{{ asset('/assets/front-end/images/icons/icon-cb-facebook.svg') }}" alt="" class="{{ (isset($social_connect_facebook))?'active':'' }} mediaCheck {{ (isset($social_connect['media']) && $social_connect['media'] == 'facebook')?'checked':'' }}"  data-media="facebook" >
                    <img src="{{ asset('/assets/front-end/images/icons/icon-cb-youtube.svg') }}"  class="{{ (isset($social_connect_youtube))?'active':'' }} mediaCheck {{ (isset($social_connect['media']) && $social_connect['media'] == 'youtube')?'checked':'' }}" alt="" data-media="youtube" >
                    <img src="{{ asset('/assets/front-end/images/icons/icon-cb-tiktok.svg') }}" alt="" class="{{ (isset($social_connect_tiktok))?'active':'' }} mediaCheck {{ (isset($social_connect['media']) && $social_connect['media'] == 'tiktok')?'checked':'' }}"  data-media="tiktok" >
                </div>
                <div class="cb-prcing">
                    <div class="cb-smlabl">Your rank & what you earn:</div>
                    <div class="cb-smbl">
                        <div class="cb-smbl-img">
                            <img src="{{ asset('/assets/front-end/images/icons/trofy-' . $userData->trophy . '.svg') }}" alt="">
                        </div>
                        <div class="cb-smbl-prc">
                            <input type="text" id="width-dynamic" class="type_price_boost price-current" readonly hidden> 
                            <span class="type_price_boost_text price-current_text"></span> €
                        </div>
                    </div>
                </div>
                <div class="cb-prcing">
                    <div class="cb-smlabl">Your next rank:</div>
                    <div class="cb-smbl">
                        <div class="cb-smbl-img">
                            <img src="{{ asset('/assets/front-end/images/icons/trofy-' . @$pricingNext->type . '.svg') }}" alt="">
                        </div>
                        <div class="cb-smbl-prc">
                            <input type="text" class="type_price_boost_next price-next" readonly hidden> 
                            <span class="type_price_boost_next_text price-next_text"></span> €
                        </div>
                        <div class="prcnt">
                            <img src="{{ asset('/assets/front-end/images/icons/icon-cb-graph.svg') }}" alt="">
                            <span class="price-current_boost_per price-current_per"></span>%
                        </div>
                        
                    </div>
                </div>
                    
            </div>
            <div class="camping-box   @foreach($advertising_method_check as $method)
                        @if(isset($method->type)  )
                        {{isset($method->type)?(($method->type == 'Reaction video')?'open':''):'open'}} 
                        @endif 
                    @endforeach 
                      {{!isset($advertising_method_check[0])?'open':''}} 
                    ">

                    <!-- @if($sm_campaigns_video == 0) disabled @endif -->
                <div class="cbInput">
                    <input type="checkbox" value="Reaction video" name="type_reaction" class="cbInput-radio" id="type-post-content-reaction"
                    @foreach($advertising_method_check as $method)
                        @if(isset($method->type) )
                        {{isset($method->type)?(($method->type == 'Reaction video')?'checked':''):'checked'}} 
                        @endif 
                    @endforeach
                      {{!isset($advertising_method_check[0])?'checked':''}} 
                      >
                    <label class="cbInputLabel">

                    </label>
                </div>
                <div class="cb-image">
                    <img src="{{ asset('/assets/front-end/images/icons/icon-postType-reaction-black.svg') }}" alt="">
                </div>
                <div class="cb-title">
                   Reaction video
                </div>
                <div class="cb-sublabel">
                   Make a reaction video for your brand
                </div>
                <div class="cb-socialmedia">                       
                    <img src="{{ asset('/assets/front-end/images/icons/icon-cb-instagram.svg') }}"  class="{{ (isset($social_connect_instagram))?'active':'' }} mediaCheck  {{ (isset($social_connect_high['media']) && $social_connect_high['media'] == 'instagram')?'checked':'' }}" alt=""  data-media="instagram"   >
                    <img src="{{ asset('/assets/front-end/images/icons/icon-cb-facebook.svg') }}" alt="" class="{{ (isset($social_connect_facebook))?'active':'' }} mediaCheck {{ (isset($social_connect_high['media']) && $social_connect_high['media'] == 'facebook')?'checked':'' }}"  data-media="facebook" >
                    <img src="{{ asset('/assets/front-end/images/icons/icon-cb-youtube.svg') }}"  class="{{ (isset($social_connect_youtube))?'active':'' }} mediaCheck {{ (isset($social_connect_high['media']) && $social_connect_high['media'] == 'youtube')?'checked':'' }}" alt="" data-media="youtube" >
                    <img src="{{ asset('/assets/front-end/images/icons/icon-cb-tiktok.svg') }}" alt="" class="{{ (isset($social_connect_tiktok))?'active':'' }} mediaCheck {{ (isset($social_connect_high['media']) && $social_connect_high['media'] == 'tiktok')?'checked':'' }}"  data-media="tiktok" >
                </div>
                <div class="cb-prcing">
                    <div class="cb-smlabl">Your rank & what you earn:</div>
                    <div class="cb-smbl">
                        <div class="cb-smbl-img">
                            <img src="{{ asset('/assets/front-end/images/icons/trofy-' . $userData->trophy . '.svg') }}" alt="">
                        </div>
                        <div class="cb-smbl-prc">
                            <input type="text" id="width-dynamic" class="type_price_reaction price-current" readonly hidden > 
                            <span class="type_price_reaction_text price-current_text"></span> €
                        </div>
                    </div>
                </div>
                <div class="cb-prcing">
                    <div class="cb-smlabl">Your next rank:</div>
                    <div class="cb-smbl">
                        <div class="cb-smbl-img">
                            <img src="{{ asset('/assets/front-end/images/icons/trofy-' . @$pricingNext->type . '.svg') }}" alt="">
                        </div>
                        <div class="cb-smbl-prc">
                            
                            <input type="text" class="type_price_reaction_next price-next" readonly hidden > 
                            <span class="type_price_reaction_next_text price-next_text"></span> €
                        </div>
                        <div class="prcnt">
                            <img src="{{ asset('/assets/front-end/images/icons/icon-cb-graph.svg') }}" alt="">
                            <span class="price-current_reaction_per price-current_per the-value-is-here"></span>%
                        </div>
                        
                    </div>
                </div>
                    
            </div>
            <div class="camping-box   @foreach($advertising_method_check as $method)
                        @if(isset($method->type)  )
                        {{isset($method->type)?(($method->type == 'Survey')?'open':''):'open'}} 
                        @endif 
                    @endforeach
                      {{!isset($advertising_method_check[0])?'open':''}} 
                   
                    ">
                     <!-- @if($sm_campaigns_survey == 0) disabled @endif -->
                <div class="cbInput">
                    <input type="checkbox" value="Survey" class="cbInput-radio" name="type_survey" id="type-post-content-survey"  
                    @foreach($advertising_method_check as $method)
                        @if(isset($method->type)  )
                        {{isset($method->type)?(($method->type == 'Survey')?'checked':''):'checked'}} 
                        @endif 
                    @endforeach
                      {{!isset($advertising_method_check[0])?'checked':''}} >
                    {{-- <input type="radio" class="cbInput-radio" name="cb-input"> --}}
                    <label class="cbInputLabel">

                    </label>
                </div>
                <div class="cb-image">
                    <img src="{{ asset('/assets/front-end/images/icons/icon-postType-survey-black.svg') }}" alt="">
                </div>
                <div class="cb-title">
                   Survey
                </div>
                <div class="cb-sublabel">
                    Make a survey for your brand
                </div>
                <div class="cb-socialmedia">              
                    <img src="{{ asset('/assets/front-end/images/icons/icon-cb-instagram.svg') }}"  class="{{ (isset($social_connect_instagram))?'active':'' }} mediaCheck  {{ (isset($social_connect_high['media']) && $social_connect_high['media'] == 'instagram')?'checked':'' }}" alt=""  data-media="instagram"   >
                    <img src="{{ asset('/assets/front-end/images/icons/icon-cb-facebook.svg') }}" alt="" class="{{ (isset($social_connect_facebook))?'active':'' }} mediaCheck {{ (isset($social_connect_high['media']) && $social_connect_high['media'] == 'facebook')?'checked':'' }}"  data-media="facebook" >
                    <img src="{{ asset('/assets/front-end/images/icons/icon-cb-youtube.svg') }}"  class="{{ (isset($social_connect_youtube))?'active':'' }} mediaCheck {{ (isset($social_connect_high['media']) && $social_connect_high['media'] == 'youtube')?'checked':'' }}" alt="" data-media="youtube" >
                    <img src="{{ asset('/assets/front-end/images/icons/icon-cb-tiktok.svg') }}" alt="" class="{{ (isset($social_connect_tiktok))?'active':'' }} mediaCheck {{ (isset($social_connect_high['media']) && $social_connect_high['media'] == 'tiktok')?'checked':'' }}"  data-media="tiktok" >
                </div>
                <div class="cb-prcing">
                    <div class="cb-smlabl">Your rank & what you earn:</div>
                    <div class="cb-smbl">
                        <div class="cb-smbl-img">
                            <img src="{{ asset('/assets/front-end/images/icons/trofy-' . $userData->trophy . '.svg') }}" alt="">
                        </div>
                        <div class="cb-smbl-prc">
                            <input type="text" id="width-dynamic" class="type_price_survey price-current" readonly hidden > 
                            <span class="type_price_survey_text price-current_text"></span> €
                        </div>
                    </div>
                </div>
                <div class="cb-prcing">
                    <div class="cb-smlabl">Your next rank:</div>
                    <div class="cb-smbl">
                        <div class="cb-smbl-img">
                            <img src="{{ asset('/assets/front-end/images/icons/trofy-' . @$pricingNext->type . '.svg') }}" alt="">
                        </div>
                        <div class="cb-smbl-prc">
                            <input type="text" class="type_price_survey_next price-next" readonly hidden > 
                            <span class="type_price_survey_next_text price-next_text"></span> €
                        </div>
                        <div class="prcnt">
                            <img src="{{ asset('/assets/front-end/images/icons/icon-cb-graph.svg') }}" alt="">
                            <span class="price-current_survey_per price-current_per"></span>%
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<input type="hidden" name="publish" value="Publish"> 
<div class="step-nevigationbutton">
    <div class="nav-left back me-2" id="Back_step3" data-from-step="Reaction video">
        <img src="{{ asset('assets/front-end/images/icons/step-left.svg') }}" class="" alt="">
    </div>
    <div class="nav-right next ms-auto">
        <img src="{{ asset('assets/front-end/images/icons/step-right.svg') }}" class="" alt="">
    </div>
</div>
</div>
</form>

<script>
    $(document).ready(function() {
        $('#textbox1').val(this.checked);
    });

    $(function() {
        $("#selectsharecontent").change(function() {
            var status = this.value;
            $("#sharecontentImage").attr("src","{{ asset('/') }}assets/front-end/images/" + status + ".png");
        });
    });

    $(function() {
        $("#selectvideoondemand").change(function(){
            var status = this.value;
            $("#videoondemand").attr("src","{{ asset('/') }}assets/front-end/images/" + status + ".png");
        });
    });

    $(function() {
        $("#selectLivestreme").change(function(){
            var status = this.value;
            $("#livestreme").attr("src","{{ asset('/') }}assets/front-end/images/" + status + ".png");
        });
    });


    $(".socialMediaSection input[type=checkbox]").attr("disabled", true)
    
    $(document).on("click","input.classtop",function(){
        if($(this).is(':checked')){
            $(this).closest(".select_media").addClass("checkedmedia");
            $(this).closest(".select_media").find("select").removeAttr('disabled');
            $(this).closest(".select_media").find('.shareconent').each(function(){
                //if statement here 
                // use $(this) to reference the current div in the loop
                //you can try something like...
                // if(condition){
                // }
                var mediaLink = $(this).val();
                $(this).closest(".select_media ").addClass(mediaLink);
            }).change(function(){
                var mediaLink = $(this).val();
                $(this).closest(".select_media ").addClass(mediaLink);

                if($(this).val() == "facebook"){
                    $(this).closest(".select_media ").removeClass("instagram twitter tiktok youtube snapchat twitch")
                }else if($(this).val() == "twitch"){
                    $(this).closest(".select_media ").removeClass("instagram facebook twitter tiktok youtube snapchat")
                }else if($(this).val() == "instagram"){
                    $(this).closest(".select_media ").removeClass("facebook twitter tiktok youtube snapchat twitch")
                }else if($(this).val() == "twitter"){
                    $(this).closest(".select_media ").removeClass("instagram facebook tiktok youtube snapchat twitch")
                }else if($(this).val() == "tiktok"){
                    $(this).closest(".select_media ").removeClass("instagram twitter facebook youtube snapchat twitch")
                }else if($(this).val() == "youtube"){
                    $(this).closest(".select_media ").removeClass("instagram twitter tiktok facebook snapchat twitch")
                }else if($(this).val() == "snapchat"){
                    $(this).closest(".select_media ").removeClass("instagram twitter tiktok youtube facebook twitch")
                }
            });
        }else{
            $(this).closest(".select_media").removeClass("checkedmedia");
            $(this).closest(".select_media").find("select").attr('disabled','disabled');
            $(this).closest(".select_media").find('.shareconent').change(function(){
                var mediaLink = $(this).val();
                $(this).closest(".select_media ").addClass(mediaLink);

                if($(this).val() == "facebook"){
                    $(this).closest(".select_media ").removeClass("instagram twitter tiktok youtube snapchat twitch")
                }else if($(this).val() == "twitch"){
                    $(this).closest(".select_media ").removeClass("instagram facebook twitter tiktok youtube snapchat")
                }else if($(this).val() == "instagram"){
                    $(this).closest(".select_media ").removeClass("facebook twitter tiktok youtube snapchat twitch")
                }else if($(this).val() == "twitter"){
                    $(this).closest(".select_media ").removeClass("instagram facebook tiktok youtube snapchat twitch")
                }else if($(this).val() == "tiktok"){
                    $(this).closest(".select_media ").removeClass("instagram twitter facebook youtube snapchat twitch")
                }else if($(this).val() == "youtube"){
                    $(this).closest(".select_media ").removeClass("instagram twitter tiktok facebook snapchat twitch")
                }else if($(this).val() == "snapchat"){
                    $(this).closest(".select_media ").removeClass("instagram twitter tiktok youtube facebook twitch")
                }
            });
        }
    })
    
function formatState (state) {
    if (!state.id) {
        return state.text;
    }
    var baseUrl = "{{ asset('/assets/front-end/images') }}";
    var $state = $(
        '<span><img src="' + baseUrl + '/col_icon_' + state.element.value.toLowerCase() + '.png" class="img-flag" /> ' + state.text + '</span>'
    );
    return $state;
};

$(".shareconent").select2({
    templateResult: formatState,
    templateSelection: formatState,
    containerCssClass: "selectoption",
    dropdownCssClass: "selectoptionTop"
}); 

$(document).ready(function(){
    if($(".cbInput-radio").is(':checked')){
        $(".cbInput-radio").closest(".camping-box").addClass("open");
    }
})


$(document).on("click", ".cbInput-radio", function(){
    
    if($(this).is(':checked')){
        $(this).closest(".camping-box").addClass("open");
    }else{
        $(this).closest(".camping-box").removeClass("open");
    }
})
 

$(document).on("click", ".cb-socialmedia .mediaCheck", function() {
    var media = $(this).data('media'); 
    var type = $(this).closest(".camping-box").find(".cbInput-radio").val(); 
    var this_o = $(this)
    this_o.closest(".camping-box").find(".mediaCheck").removeClass("checked")
    this_o.addClass("checked")
    console.log(media);
    $.ajax({
        url: "{{ url('get-admin-pricing') }}",
        data: { media: media, type: type }
    })
    .done(function(data) { 
        if(data.price != null) {
            this_o.closest(".camping-box").find(".price-current").val(data.price.toFixed(2));  
            this_o.closest(".camping-box").find(".price-current_text").text(data.price.toFixed(2));  
            this_o.closest(".camping-box").find(".price-next").val(data.price_next.toFixed(2));  
            this_o.closest(".camping-box").find(".price-next_text").text(data.price_next.toFixed(2));  

            var per = ((data.price_next / data.price) * 100) - 100;
            const roundedPer = data.price > 0 ? Math.round(per) : 0;
            this_o.closest(".camping-box").find(".price-current_per").text(roundedPer);
        }else{
            this_o.closest(".camping-box").find(".price-current").val('');  
            this_o.closest(".camping-box").find(".price-current_text").text('');  
            this_o.closest(".camping-box").find(".price-next").val('');  
            this_o.closest(".camping-box").find(".price-next_text").text('');  


            this_o.closest(".camping-box").find(".price-current_per").text('0');  
        } 
    });

})

</script>