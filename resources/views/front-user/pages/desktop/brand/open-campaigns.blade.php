{{-- To see the contents of the varaible $influencerCampaignDetail, check OpenCampaignsController::buildOpenCampaignsPageForBrand() --}}
<div class="row campaign-status-badge-container"
    data-bs-toggle="modal"
    data-bs-target="#brandCampaignPhasesModal"
    style="cursor: pointer;">
    @if ($influencerCampaignDetail->created_at->addDays(3) > now())
        <span class="btn-request-phase campaign-status-badge">
            Request Phase
        </span>
    @else
        <span class="campaign-status-badge" style="background-color:#E8C11C; color:white;">
            Payment Phase
        </span>
    @endif
</div>
<div class="campaign-header">
    <div class="header-section">
        <div class="campagin_info" style="width: 60%">
            <span style="color: #AD80FF; padding: 0 10px;">ID # {{ $influencerCampaignDetail->campaign_id }}</span>
            <h4 style="font-weight: 700;  padding: 0 10px;">{{ $influencerCampaignDetail->campaign_title }}</h4>
            <div>
                <span class="badge" target="popup" data-bs-toggle="modal"
                    data-bs-target="#showInfluencers{{ $influencerCampaignDetail->id }}">
                    @php
                        $lists2 = App\Models\InfluencerRequestDetail::where(
                            'compaign_id',
                            $influencerCampaignDetail->campaign_id,
                        )
                            ->where('status', null)
                            ->get();
                        $influencer_count = 0;
                        foreach ($lists2 as $list) {
                            if (
                            (isset($list->influencer_request_accepts->request) &&
                                $list->influencer_request_accepts->request == 1) ||
                            !isset($list->influencer_request_accepts->request)
                        ) {
                                $influencer_count += 1;
                            }
                        }
                    @endphp
                    <span style="cursor: pointer;">
                        <img src="{{ asset('/assets/front-end/images/new/three_users.svg') }}">
                        {{ $influencer_count }}
                        {{ $influencer_count > 1 ? 'Influencers' : 'Influencer' }}
                    </span>
                    <span
                        class="ctrb green-user p-2 rounded text-white">{{ $influencerCampaignDetail->accept_request > 0 ? $influencerCampaignDetail->accept_request : 0 }}</span>
                    <span
                        class="ctrb red-user p-2 rounded text-white">{{ $influencerCampaignDetail->total_count - $influencerCampaignDetail->accept_request }}</span>
                    <span style="cursor: pointer;" target="popup" data-bs-toggle="modal"
                        data-bs-target="#showInfluencers{{ $influencerCampaignDetail->id }}">
                        <img src="{{ asset('/') }}/assets/front-end/images/icon-gare.svg"
                            alt="">
                    </span>
                </span>
                <span class="badge">
                    <img src="{{ asset('/assets/front-end/images/new/survey_camp.svg') }}"> {{ $influencerCampaignDetail->post_type }}
                </span>
                <span class="badge">
                    <img width="20" height="20" src="{{ asset('/assets/front-end/images/icons/campaigns-' . $influencerCampaignDetail->media . '.svg') }}"> {{ $influencerCampaignDetail->advertising }}
                </span>
            </div>
        </div>
        <div class="vertical-line"></div>
        <div class="details" style="width: 25%; display:flex; flex-direction:column;">
            <span class="text-success" style="font-size: 1.5em;font-weight: 700; padding: 5px;">
                @php
                    $newLivetreamPrice = 0;
                    $addNewLivetreamPrice = 0;
                    $user = App\Models\User::find($influencerCampaignDetail->influencerdetails->user->id);

                    // Use the new InfluencerPrice system instead of legacy AdvertisingMethodPrice
                    if ($user->influencer) {
                        // Map legacy advertising types to proper campaign types
                        $campaignType = match ($influencerCampaignDetail->advertising) {
                            App\Constants\CampaignType::BOOST_ME, 'Boost me', 'boost me', 'Story', 'Story - Picture', 'Story - Video' => App\Constants\CampaignType::BOOST_ME,
                            App\Constants\CampaignType::REACTION_VIDEO, 'Reaction video', 'reaction video', 'Reel' => App\Constants\CampaignType::REACTION_VIDEO,
                            App\Constants\CampaignType::SURVEY, 'survey', 'Post - Picture' => App\Constants\CampaignType::SURVEY,
                            default => App\Constants\CampaignType::BOOST_ME // fallback
                        };

                        $influencerPrice = $user->influencer->prices()
                            ->where('campaign_type', $campaignType)
                            ->first();

                        if ($influencerPrice) {
                            $newLivetreamPrice = $influencerPrice->price;
                        }
                    }
                @endphp

                @php
                    // Calculate total using same logic as payment modal
                    $subtotal = 0;
                    $totalVAT = 0;
                    $campaignInfluencers = \App\Models\InfluencerRequestDetail::where('compaign_id', $influencerCampaignDetail->campaign_id)->get();

                    foreach ($campaignInfluencers as $campaignInfluencer) {
                        $influencerPrice = $campaignInfluencer->influencer_price ?? 0;
                        $subtotal += $influencerPrice;

                        // Calculate VAT separately (same as payment modal)
                        if ($campaignInfluencer->influencerdetails && $campaignInfluencer->influencerdetails->user && !$campaignInfluencer->influencerdetails->user->is_small_business_owner) {
                            $totalVAT += $influencerPrice * 0.19;
                        }
                    }

                    // Calculate platform fee on subtotal only (same as payment modal)
                    $platformFee = ($subtotal * 5) / 100;
                    if ($platformFee < 2) {
                        $platformFee = 2;
                    }
                    $totalVAT += $platformFee * 0.19; // Add platform fee VAT

                    $campaignTotal = $subtotal + $platformFee + $totalVAT;
                @endphp
                € {{ number_format($campaignTotal, 2) }}
            </span>
            <span class="submit-text-color" style="padding: 5px;">
                <span class="timing" style="display:block; background-color:transparent; font-size:25px; width: 100%;" id="timer{{ $influencerCampaignDetail->campaign_id }}"></span>
                @if ($influencerCampaignDetail->social_posts == '')
                    @if ($influencerCampaignDetail->accept_request == $influencerCampaignDetail->total_influencer_count)
                        <span class="smalltext" id="timer-info-{{ $influencerCampaignDetail->campaign_id }}" style="left: 0; bottom:0; text-align:center; margin-top: 10px;">
                            Time to start your campaign
                        </span>
                    @else
                        <span class="smalltext" id="timer-info-{{ $influencerCampaignDetail->campaign_id }}" style="left: 0; bottom:0; text-align:center; margin-top: 10px;">
                            Wait for response from influencers
                        </span>
                    @endif
                @endif
            </span>
        </div>
        <div class="vertical-line"></div>
        <div class="details" style="width: 25%;  display:flex; flex-direction:column;">
            <button class="btn  btn-show-details " style="width: 100%;"
                data-id="{{ $influencerCampaignDetail->id }}" target="popup" data-bs-toggle="modal"
                data-bs-target="#campaign-details-{{ $influencerCampaignDetail->id }}">Show Details</button>
            <button class="btn  btn-cancel-new" style="width: 100%;margin-top: 3%;"
                value="Cancel Campaign" target="popup" data-bs-toggle="modal"
                data-bs-target="#cancelRequest{{ $influencerCampaignDetail->id }}">Cancel
                Campaign</button>
            @php
                $created_date = date('Y-m-d', strtotime($influencerCampaignDetail->created_at));
                $campaignDate = date('Y-m-d', strtotime($created_date . ' + 3 days'));
                $date = date('Y-m-d');
            @endphp
            @if (
                $influencerCampaignDetail->total_influencer_count == $influencerCampaignDetail->accept_request + ($influencerCampaignDetail->total_count - $influencerCampaignDetail->accept_request) &&
                    $influencerCampaignDetail->accept_request >= 1)
                <button id="startCampaign"
                    onclick="startCampaign('{{ $influencerCampaignDetail->campaign_id }}');"
                    class="btn green-btn  btn-outline-secondary startTImer{{ $influencerCampaignDetail->campaign_id }}"
                    style="display: block; width: 100%; margin-top: 7px;">Start
                    Campaign</button>
            @else
                @if ($date <= $campaignDate)
                    <button id="startCampaign"
                        onclick="startCampaign('{{ $influencerCampaignDetail->campaign_id }}');"
                        class="btn green-btn btn-outline-secondary startTImer{{ $influencerCampaignDetail->campaign_id }}"
                        disabled style="display: block; width: 100%; margin-top: 7px;">Start
                        Campaign</button>
                @else
                    <button id="startCampaign"
                        onclick="startCampaign('{{ $influencerCampaignDetail->campaign_id }}');"
                        class="btn green-btn  btn-outline-secondary startTImer{{ $influencerCampaignDetail->campaign_id }}"
                        style="display: block; width: 100%; margin-top: 7px;">Start
                        Campaign</button>
                @endif
            @endif
        </div>
    </div>
</div>
