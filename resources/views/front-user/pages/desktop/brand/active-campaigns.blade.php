@php
    // Currently all timestamp calculation are happening based on the first influlencerRequestDetail 
    // in the campaign. But there can be multiple influencers in one campaign. So it should calculate
    // the times based on when the brand paid and started the campaign.
    // TODO fix this soon!! **************************
    $influencerRequestDetail = $influencerCampaignDetail->influencer_request_details[0];

    $currentPhaseDurationInDays = $influencerRequestDetail->phase_duration;
    if ($influencerRequestDetail->influencer_request_accepts->is_time_extension_approved) {
        $currentPhaseDurationInDays += $influencerRequestDetail->influencer_request_accepts->requested_extension_duration;
    }

    // Get the current date and time
    $now = new DateTime('now', new DateTimeZone('UTC'));
    
    // TODO 
    $campaignCreatedAt = new DateTime(
        $influencerRequestDetail->influencer_request_accepts->created_at,
        new DateTimeZone('UTC'),
    );

    // A new DateTime to track until when the timer will be shown
    // For brand side timer, it has nothing to do with if the influencer has submitted or not.
    // It is all about when the submit phase generally should end and when the review phase
    // generally should end.
    $submitPhaseEndsAt = clone $campaignCreatedAt;
    $submitPhaseEndsAt->modify("+10 days");

    $reviewPhaseEndsAt = clone $submitPhaseEndsAt;
    $reviewPhaseEndsAt->modify("+3 days");

    // Determine current phase based on timestamps
    $reviewTag = true;
    $timerRunsUntil = clone $now;
    $shouldTimerRun = false;
    if ($now < $submitPhaseEndsAt) {
        $influencerCampaignDetail->current_phase = 'submit';
        $reviewTag = false;
        $shouldTimerRun = true;
        $timerRunsUntil = clone $submitPhaseEndsAt;
    } elseif ($now >= $submitPhaseEndsAt && $now < $reviewPhaseEndsAt) {
        $influencerCampaignDetail->current_phase = 'review';
        $timerRunsUntil = clone $reviewPhaseEndsAt;
        $shouldTimerRun = true;
    } else {
        // Past review phase end date
        $influencerCampaignDetail->current_phase = 'finish';
    }

    // In milliseconds
    $distanceUntilTimerStopsMS = ($timerRunsUntil->getTimestamp() * 1000) - ($now->getTimestamp() * 1000);
@endphp

<div class="row campaign-status-badge-container" data-bs-toggle="modal" data-bs-target="#brandCampaignPhasesModal" style="cursor: pointer;">
    <span class="btn-submit-phase desktop-view campaign-status-badge" id="phaseTag{{ $influencerCampaignDetail->campaign_id }}">Submit Phase</span>
</div>
<div class="campaign-header">
    <div class="header-section">
        <div class="campagin_info" style="width: 60%">
            <span style="color: #AD80FF; padding: 0 10px;">ID # {{ $influencerCampaignDetail->campaign_id }}</span>
            <h4 style="font-weight: 700;  padding: 0 10px;">{{ $influencerCampaignDetail->compaign_title }}</h4>
            <div>
                <span class="badge" target="popup" data-bs-toggle="modal" data-bs-target="#showInfluencers{{ $influencerCampaignDetail->id }}">
                    <img src="{{ asset('/assets/front-end/images/new/three_users.svg') }}">
                    {{ $influencerCampaignDetail->influencer_count }}
                    {{ $influencerCampaignDetail->influencer_count > 1 ? 'Influencers' : 'Influencer' }}
                </span>
                <span class="badge">
                    <img src="{{ asset('/assets/front-end/images/new/survey_camp.svg') }}" />
                    {{ $influencerCampaignDetail->post_type }}
                </span>
                <span class="badge">
                    <img width="20" height="20" src="{{ asset('assets/front-end/images/icons/campaigns-' . $influencerCampaignDetail->media . '.svg') }}" />
                    {{ $influencerCampaignDetail->advertising }}
                </span>
            </div>
        </div>
        <div class="vertical-line"></div>
        <div class="details" style="width: 25%; display:flex; flex-direction:column;">
            <span class="text-success" style="font-size: 1.5em;font-weight: 700; padding: 5px;">
                € {{ number_format(isset($influencerCampaignDetail->campaign_details->total_amount) ? $influencerCampaignDetail->campaign_details->total_amount : 0, 2) }}
            </span>

            {{-- Timing Starts --}}
            <span class="submit-text-color" style="padding: 5px;">
                <input type="hidden"
                    class="campaign-title-{{ $influencerCampaignDetail->campaign_id }}"
                    value="{{ $influencerCampaignDetail->compaign_title }}">
                <input type="hidden"
                    class="data-submit-phase-duration-day-{{ $influencerCampaignDetail->campaign_id }}"
                    value="{{ $currentPhaseDurationInDays }}">
                <input type="hidden"
                    class="data-influencer-request-accept-date-{{ $influencerCampaignDetail->campaign_id }}"
                    value="{{ $influencerRequestDetail->influencer_request_accepts->created_at }}">

                @if ($shouldTimerRun)
                    {{-- If still running, then show the timer --}}
                    <span class="timing timerActive{{ $influencerCampaignDetail->campaign_id }}" style="display:block; background-color:transparent; font-size:25px"></span>
                @else
                    {{-- If campaign is already is finished --}}
                    <span class="timing timerActive{{ $influencerCampaignDetail->campaign_id }}" style="display:block; background-color:transparent; font-size:25px">Finished</span>
                @endif
            </span>
            {{-- Timing Ends --}}
        </div>
        <div class="vertical-line"></div>
        <div class="details" style="width: 25%;  display:flex; flex-direction:column;">
            <button class="btn  btn-show-details "
                style="width: 100%; margin: 0 0 10px 0;" data-id="{{ $influencerCampaignDetail->id }}"
                target="popup" data-bs-toggle="modal"
                data-bs-target="#show-campaign-details-{{ $influencerCampaignDetail->id }}">Show Details
            </button>

            @if (!empty($influencerCampaignDetail->platform_invlices[0]) && !empty($influencerCampaignDetail->platform_invlices[0]->receipt))
            <a class="btn btn-show-fee-invoice" href="{{ $platform_invoice->receipt }}" target="_blank"
                style="width: 100%; margin: 0 0 10px 0; border: solid 1px #AD80FF; color: white; background-color:#AD80FF; padding: 2px;">
                <img name="pdf-icon-btn" src="{{ asset('/assets/front-end/images/icons/pdf-icon.svg') }}" />
                Fee Invoice
            </a>
            @endif

            <form method="post" action="{{ url('/finish-campaign') }}" style="width: 100%;" data-parsley-validate>
                @csrf
                <input type="hidden" name="campaign_id" value="{{ $influencerCampaignDetail->campaign_id }}" />
                <button type="submit" style="width: 100%; margin: 0;"
                    class="finish-camp btn btn-finish-campaign @if (!$influencerCampaignDetail->can_be_finished) btn-finish-disabled @endif"
                    @if (!$influencerCampaignDetail->can_be_finished) disabled @endif
                    value="Finish campaign">Finish Campaign
                </button>
            </form>

        </div>
        <div class="vertical-line"></div>
        <div class="details" style="width: 10%;  display:flex; flex-direction:column;">
            <span class="circle camp-button dropdown-button collapsed"
                data-bs-toggle="collapse"
                data-bs-target="#desktop-collapse{{ $influencerCampaignDetail->campaign_id }}"
                aria-expanded="false"
                aria-controls="desktop-collapse{{ $influencerCampaignDetail->campaign_id }}" rowspan="2">
                <i class="collapse-icon fas fa-chevron-up" data-toggle="collapse" data-target="#campaignDetails"></i>
            </span>
        </div>
    </div>
</div>

<x-brand.desktop.active-campaigns-influencer-info-tab :influencerCampaignDetail="$influencerCampaignDetail" />

<script type="text/javascript">
    (function() {
        jQuery(document).ready(function($) {
            // Local variables scoped only to this function
            let influencerRequestDetail = @json($influencerRequestDetail);
            let activeCampaignsCount = @json($activeCampaignsCount);
            let reviewTag = @json($reviewTag);
            let shouldTimerRun = @json($shouldTimerRun);
            let distanceUntilTimerStops = @json($distanceUntilTimerStopsMS);
            let submitPhaseEndsAt = @json($submitPhaseEndsAt);
            let reviewPhaseEndsAt = @json($reviewPhaseEndsAt);
            let latestDistanceUntilTimerStops = distanceUntilTimerStops;
            let timerObj;
            
            // Debug logging
            console.log('DESKTOP >>>>>>>>>>>>');
            console.log('Campaign ID:', influencerRequestDetail.campaign_id);
            console.log('Campaign Title:', influencerRequestDetail.campaign_title);
            console.log('influencerRequestDetail:', influencerRequestDetail);
            console.log('activeCampaignsCount:', activeCampaignsCount);
            console.log('reviewTag:', reviewTag);
            console.log('shouldTimerRun:', shouldTimerRun);
            console.log('distanceUntilTimerStops:', distanceUntilTimerStops);
            console.log('Current Phase:', @json($influencerCampaignDetail->current_phase));
            console.log('submitPhaseEndsAt:', submitPhaseEndsAt);
            console.log('reviewPhaseEndsAt:', reviewPhaseEndsAt);
            console.log('latestDistanceUntilTimerStops:', latestDistanceUntilTimerStops);
            console.log('***********************************');

            function updateTimer() {
                // After each turn, deduct 1000 ms (1 sec) from the latestDistanceUntilTimerStops,
                // so that we can update the UI with fancy timer view.
                latestDistanceUntilTimerStops -= (1000 * 60);

                var days = Math.floor(latestDistanceUntilTimerStops / (1000 * 60 * 60 * 24));
                var hours = Math.floor((latestDistanceUntilTimerStops % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                var minutes = Math.floor((latestDistanceUntilTimerStops % (1000 * 60 * 60)) / (1000 * 60));
                var seconds = Math.floor((latestDistanceUntilTimerStops % (1000 * 60)) / 1000);

                if (latestDistanceUntilTimerStops > 0) {
                    if (days < 10 && days != 0) {
                        days = '0' + days;
                    }

                    if (hours < 10) {
                        hours = '0' + hours;
                    }

                    if (minutes < 10) {
                        minutes = '0' + minutes;
                    }

                    if (seconds < 10) {
                        seconds = '0' + seconds;
                    }

                    $(".timerActive" + influencerRequestDetail.campaign_id).html(days + " d " + hours + " h " + minutes + " min");
                } else {
                    days = '0';
                    hours = '0';
                    minutes = '0';
                    seconds = '0';

                    if (latestDistanceUntilTimerStops <= 0) {
                        if (timerObj) {
                            clearInterval(timerObj);
                        }

                        $(".timerActive" + influencerRequestDetail.campaign_id).html("Finished");

                        if (reviewTag) {
                            $('#phaseTag' + influencerRequestDetail.campaign_id).html('Review Phase');
                            $('#phaseTag' + influencerRequestDetail.campaign_id).removeClass('btn-submit-phase');
                            $('#phaseTag' + influencerRequestDetail.campaign_id).addClass('btn-review-phase');
                        }

                        if (!influencerRequestDetail['refund_reason'] && !influencerRequestDetail['is_complained'] && !influencerRequestDetail['is_paused']) {
                            $.ajax({
                                url: "{{ URL::to('/review-phase-closed') }}",
                                method: 'POST',
                                data: {
                                    '_token': "{{ csrf_token() }}",
                                    'rowId': influencerRequestDetail['id']
                                }
                            }).done(function(data) {
                                if ($('#btn-brand-review-submitted-content-' + influencerRequestDetail['id']).length) {
                                    $('#btn-brand-review-submitted-content-' + influencerRequestDetail['id']).attr('disabled', true);
                                }
                            }).fail(function() {
                                // TODO what to do when it fails?
                            });
                        }
                    }
                }
            }

            // Initialize the timer and UI
            updateTimer();

            if (reviewTag) {
                $('#phaseTag' + influencerRequestDetail.campaign_id).html('Review Phase');
                $('#phaseTag' + influencerRequestDetail.campaign_id).removeClass('btn-submit-phase');
                $('#phaseTag' + influencerRequestDetail.campaign_id).addClass('btn-review-phase');
            }

            if (activeCampaignsCount > 0 && shouldTimerRun) {
                timerObj = setInterval(updateTimer, 1000 * 60); // Runs every minute
            }
        });
    })(); // Immediately invoke the function
</script>
