<div data-bs-toggle="modal" data-bs-target="#campaignPhasesModal" style="cursor: pointer;">
    @if(isset($influencerDataItem->social_post_id) && $influencerDataItem->social_post_id != '')
        <div class="row campaign-status-badge-container">
            <span class="btn-review-phase campaign-status-badge">Review Phase</span>
        </div>
    @else
    <div class="row campaign-status-badge-container">
        <span class="btn-submit-phase campaign-status-badge">Submit Phase</span>
    </div>
    @endif
</div>
<div class="campaign-header">
    <div class="header-section">
        <div class="campagin_info" style="width: 50%">
            <span style="color: #AD80FF; padding: 0 10px;">ID # {{ $influencerDataItem->campaign_id }}</span>
            <h4 style="font-weight: 700;  padding: 0 10px;">{{ $influencerDataItem->compaign_title }}</h4>
            <div>
                <span class="badge mx-0">
                    <img src="{{ asset('/assets/front-end/images/new/brand_camp.svg') }}" width="20" height="20"> {{ $influencerDataItem->user->first_name }}
                    {{ $influencerDataItem->user->last_name }}
                </span>
                <span class="badge mx-0">
                    <img src="{{ asset('/assets/front-end/images/new/survey_camp.svg') }}">
                    {{ $influencerDataItem->post_type }}
                </span>
                <span class="badge mx-0">
                    <img src="{{ asset('/assets/front-end/images/icons/campaigns-' . $influencerDataItem->media. '.svg') }}" alt="" style="height: 20px;width:20px">
                    {{ $influencerDataItem->advertising }}
                </span>
            </div>
        </div>
        <div class="vertical-line"></div>
        <div class="details"
            style="width: 25%; display:flex; flex-direction:column;">
            <span class="text-success"
                style="font-size: 1.5em;font-weight: 700; padding: 5px;">
                @php
                $newLivetreamPrice = 0;
                $fieldName = $influencerDataItem->advertising . '_price';
                $user = App\Models\User::where('id', Auth::id())->first();
                if ($user->advertisingMethodPrice != null) {
                    $newLivetreamPrice = $user->advertisingMethodPrice->$fieldName;
                }
                @endphp

                € {{ number_format($influencerDataItem->cash_out_amount ?? 0, 2) }}
            </span>
            <span class="submit-text-color" style=" padding: 5px;">
                @php
                    $time =
                        isset($influencerDataItem->request_time_accept) &&
                        $influencerDataItem->request_time_accept == '1'
                            ? $influencerDataItem->request_time + $influencerDataItem->time
                            : $influencerDataItem->time;

                    $created_date = date(
                        'Y-m-d H:i:s',
                        strtotime($influencerDataItem->created_at),
                    );
                    $updated_date = date(
                        'Y-m-d H:i:s',
                        strtotime($influencerDataItem->updated_at),
                    );
                    $campaignDate = date(
                        'Y-m-d H:i:s',
                        strtotime($updated_date . ' + ' . $time . ' days'),
                    );
                    $date = date('Y-m-d H:i:s');
                    $seconds = strtotime($campaignDate) - strtotime($date);

                    $days = floor($seconds / 86400);
                    if ($days < $time && $days >= 0) {
                        $hours = floor(($seconds - $days * 86400) / 3600);

                        $minutes = floor(
                            ($seconds - $days * 86400 - $hours * 3600) / 60,
                        );

                        $seconds = floor(
                            $seconds -
                                $days * 86400 -
                                $hours * 3600 -
                                $minutes * 60,
                        );
                    } else {
                        'Time Passed';
                    }
                @endphp

                <span class="timing"
                    style="display:block; background-color:transparent; font-size:25px"
                    id="timer{{ $influencerDataItem->campaign_id }}">
                </span>
            </span>
        </div>
        <div class="vertical-line"></div>
        <div class="details" style="width: 25%;  display:flex; flex-direction:column;">
            <button class="btn  btn-show-details" style="width: 100%;"
                target="popup" data-bs-toggle="modal"
                data-bs-target="#requestForm{{ $influencerDataItem->id }}">Show Details
            </button>
            @foreach ($influencerRequestDetails as $influencerRequestDetail)
                {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                @if (empty($influencerRequestDetail->influencerdetails))
                    @php continue; @endphp
                @endif
                @if (empty($influencerRequestDetail->invoices->receipt))
                @php continue; @endphp
                @endif
                <a class="btn btn-show-my-invoice" href="{{ $influencerRequestDetail->invoices->receipt }}" target="_blank"
                    style="width: 100%; margin-top: 10px; border: solid 1px #AD80FF; color: white; background-color:#AD80FF; padding: 2px;">
                    <img name="pdf-icon-btn" src="{{ asset('/assets/front-end/images/icons/pdf-icon.svg') }}" />
                    My Invoice
                </a>
                @php break; @endphp
            @endforeach
            @if ($influencerDataItem->social_posts == '')
                @if ($influencerDataItem->social_posts == '' && $influencerDataItem->invoice_id != '' && $influencerDataItem->request_time_accept != '1')
                    @php $text = ''; @endphp
                    @if (isset($influencerDataItem->requested_time->request_time))
                        @php $text = 'You have already requested {{ $influencerDataItem->requested_time->request_time }} days'; @endphp
                        @if ($influencerDataItem->requested_time->status == '1')
                            @php $text .= '-  Accepted' @endphp
                        @endif
                        @if ($influencerDataItem->requested_time->status == '0')
                            @php $text .= '-  Rejected' @endphp
                        @endif @php $text .= '-  Rejected' @endphp
                    @endif
                @endif
            @endif

            @if (isset($influencerDataItem->influencer_request_accepts->complaints))
                @if ($influencerDataItem->influencer_request_accepts->complaints->status == 'Cancelled')
                    <input type="button" class="blueBtn smallBtn greensmallbtn " value="Completed">
                @elseif($influencerDataItem->influencer_request_accepts->complaints->status == 'Confirmed')
                    <input type="button" class="blueBtn smallBtn redbigBtn" value="Cancelled">
                @else
                    <input type="button" class="complaint-btn w-100" value="Brand has submitted complaint">
                @endif
            @elseif($influencerDataItem->refund_reason != '')
                <div class="ms-auto">
                    <input type="button" class="blueBtn smallBtn redbigBtn" value="Request is Cancelled">
                </div>
            @else
                @if ($influencerDataItem->social_posts == '' && $influencerDataItem->invoice_id != '')
                    <button class="btn btn-cancel-new web-cancel-campaign-by-influencer"
                        style="width: 100%; margin-top: 3%;" target="popup"
                        data-bs-toggle="modal"
                        data-bs-target="#cancelRequest{{ $influencerDataItem->id }}">Cancel Campaign
                    </button>
                    <button
                        class="btn btn-show-result1 startTImer{{ $influencerDataItem->campaign_id }}"
                        value="Submit"
                        onclick="influencerInitiateCampaignSubmissionSubmitButton('{{ $influencerDataItem->id }}')"
                        style="width: 100%;margin-top: 3%;">Submit
                    </button>
                @endif

                @if (isset($influencerDataItem->influencer_request_accepts->rating_reviews) && $influencerDataItem->review == 1)
                    <div class="ms-auto">
                        <input type="button"
                            class="blueBtn smallBtn submittedReview"
                            value="Brand has submitted review">
                    </div>
                @endif
            @endif
        </div>
    </div>
</div>
