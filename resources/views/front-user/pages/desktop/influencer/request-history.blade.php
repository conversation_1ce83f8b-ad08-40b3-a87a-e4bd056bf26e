@if ($influencerRequestData->refund_reason != '')
    @if ($influencerRequestData->refund_reason == 'Complaint Confirmed')
        <div class="row" style="margin-left: 0px; border-radius: 5px;">
            <span class="btn-show-result campaign-status-badge">Accepted</span>
        </div>
    @elseif(array_key_exists($influencerRequestData->refund_reason, $closeIconsReasons))
        <div class="row" style="margin-left: 0px;">
            <span class="btn-rejected campaign-status-badge">{{ $closeIconsReasons[$influencerRequestData->refund_reason] }}</span>
        </div>
    @else
        <div class="row" style="margin-left: 0px;">
            <span class="btn-rejected campaign-status-badge">Rejected</span>
        </div>
    @endif
@else
    @if (isset($influencerRequestData->influencer_request_accepts->request) && $influencerRequestData->influencer_request_accepts->request == '1')
        <div class="row" style="margin-left: 0px;">
            <span class="btn-show-result campaign-status-badge">Accepted</span>
        </div>
    @else
        <div class="row" style="margin-left: 0px;">
            <span class="btn-rejected campaign-status-badge">Rejected</span>
        </div>
    @endif
@endif
<div class="campaign-header">
    <div class="header-section">
        <div class="campagin_info" style="width: 50%;">
            <span style="color: #AD80FF; padding: 0 10px;">ID #
                {{ $influencerRequestData->compaign_id }}</span>
            <h4 style="font-weight: 700;  padding: 0 10px;">{{ $influencerRequestData->compaign_title }}</h4>
            <div>
                <span class="badge" style="margin-left: 0px !important;"><img
                        src="{{ asset('/assets/front-end/images/new/brand_camp.svg') }}"
                        width="20" height="20"> {{ $influencerRequestData->user->first_name }}
                    {{ $influencerRequestData->user->last_name }}</span>&nbsp;
                <span class="badge"><img
                        src="{{ asset('/assets/front-end/images/new/survey_camp.svg') }}">
                    {{ $influencerRequestData->post_type }}</span>&nbsp;
                <span class="badge"><img
                        src="{{ asset('/assets/front-end/images/icons/campaigns-' . $influencerRequestData->media . '.svg') }}"
                        alt="" style="height: 20px;width:20px">
                    {{ $influencerRequestData->advertising }}</span>&nbsp;
            </div>
        </div>
        <div class="vertical-line"></div>
        <div class="details" style="width: 25%; display:flex; flex-direction:column;">
            <span class="text-success" style="font-size: 1.9em; font-weight: 700;">
                € {{ number_format($influencerRequestData->total_amount ?? 0, 2) }}
            </span>
        </div>
        <div class="vertical-line"></div>
        <div class="details" style="width: 25%; display:flex; flex-direction:column;">
            <div style="padding: 0px 8px;">
                <button class="btn  btn-show-details " style="width: 100%;" target="popup"
                    data-bs-toggle="modal"
                    data-bs-target="#requestForm{{ $influencerRequestData->id }}">Show Details</button>
            </div>
        </div>
    </div>
</div>