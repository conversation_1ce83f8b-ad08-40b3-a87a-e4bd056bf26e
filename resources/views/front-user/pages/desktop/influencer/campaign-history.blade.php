<div class="row campaign-status-badge-container">
    @if($influencerRequestDetail->status == 'Cancelled' ||
            $influencerRequestDetail->refund_reason == 'Cancelled' ||
            $influencerRequestDetail->refund_reason == 'Complaint Confirmed' ||
            $influencerRequestDetail->refund_reason == 'Refunded On Dispute' ||
            $influencerRequestDetail->refund_reason == 'Refunded On Time Expired')
        <span class="btn-cancel-new campaign-status-badge">Cancelled</span>
    @elseif($influencerRequestDetail->status == 'Cancelled' || $influencerRequestDetail->refund_reason == 'Cancelled By Influencer')
        <span class="btn-cancel-new campaign-status-badge">Cancelled By Influencer</span>
    @elseif ($influencerRequestDetail->review == '1' || $influencerRequestDetail->finish == '1')
        <span class="btn-completed campaign-status-badge">Completed</span>
    @endif
</div>
<div class="campaign-header">
    <div class="header-section">
        <div class="campagin_info" style="width: 50%">
            <span style="color: #AD80FF; padding: 0 10px;">ID # {{ $influencerRequestDetail->compaign_id }}</span>
            <h4 style="font-weight: 700;  padding: 0 10px;">{{ $influencerRequestDetail->compaign_title }}</h4>
            <div>
                <span class="badge"><img
                        src="{{ asset('/') }}/assets/front-end/images/new/brand_camp.svg"
                        width="20" height="20"> {{ $influencerRequestDetail->user->first_name }}
                    {{ $influencerRequestDetail->user->last_name }}</span>&nbsp;
                <span class="badge"><img
                        src="{{ asset('/') }}/assets/front-end/images/new/survey_camp.svg">
                    {{ $influencerRequestDetail->post_type }}</span>&nbsp;
                <span class="badge"><img
                        src="{{ asset('/') }}/assets/front-end/images/icons/campaigns-{{ $influencerRequestDetail->media }}.svg"
                        alt="" style="height: 20px;width:20px">
                    {{ $influencerRequestDetail->advertising }}</span>&nbsp;
            </div>
        </div>
        <div class="vertical-line"></div>
        <div class="details" style="width: 30%;  display:flex; flex-direction:column;">
            <span
                class="@if ($influencerRequestDetail->review == '1' || $influencerRequestDetail->finish == '1') text-success @elseif($influencerRequestDetail->status == 'Cancelled' || $influencerRequestDetail->refund_reason == 'Cancelled')  text-gray @endif"
                style="font-size: 30px; font-weight: 700;  padding: 10px;" >
                @php
                    // Use modern pricing system (current_price completely deprecated 2025-01-29)
                    $influencerPrice = $influencerRequestDetail->cash_out_amount ?? 0;
                @endphp
                € {{ number_format($influencerPrice, 2) }}
            </span>
            <span class="likes_statistics" style=" padding: 10px;">
                <x-insight-stats :influencer-request-detail="$influencerRequestDetail" />
            </span>
        </div>
        <div class="vertical-line"></div>
        <div class="details" style="width: 20%; display:flex; flex-direction:column !important;">
            <button class="btn btn-show-details"
                style="width: 80%; margin: 0;"
                target="popup" data-bs-toggle="modal"
                data-bs-target="#requestForm{{ $influencerRequestDetail->id }}">Show Details
            </button>
            @if ($influencerRequestDetail->review == 1 && isset($influencerRequestDetail->influencer_request_accepts->rating_reviews))
                <button
                    class="btn btn-show-result1 btn-influencer-show-results-desktop"
                    data-bs-toggle="modal"
                    data-bs-target="#influencer-show-results-{{ $influencerRequestDetail->id }}"
                    style="width: 80%; margin: 5px 0 0 0;">Show Results
                </button>
                @if (!empty($influencerRequestDetail->invoices->receipt))
                    <a class="btn btn-show-my-invoice" href="{{ $influencerRequestDetail->invoices->receipt }}" target="_blank"
                        style="width: 80%; margin-top: 5px; margin-left: 0px; border: solid 1px #AD80FF; color: white; background-color:#AD80FF; padding: 2px;">
                        <img name="pdf-icon-btn" src="{{ asset('/assets/front-end/images/icons/pdf-icon.svg') }}" />
                        My Invoice
                    </a>
                @endif
                <div class="star-rating" data-bs-toggle="modal" style="margin: 0; font-size: 1.7rem;"
                    data-bs-target="#reviewRatingPopup{{ $influencerRequestDetail->influencer_request_accepts->rating_reviews->id }}">
                    @php $count = 5 - $influencerRequestDetail->influencer_request_accepts->rating_reviews->rating; @endphp
                    @for ($i = 0; $i < $influencerRequestDetail->influencer_request_accepts->rating_reviews->rating; $i++)
                        <span class="star filled">★</span>
                    @endfor
                    @for ($j = 0; $j < $count; $j++)
                        <span>★</span>
                    @endfor
                </div>
            @endif
        </div>
    </div>
</div>
