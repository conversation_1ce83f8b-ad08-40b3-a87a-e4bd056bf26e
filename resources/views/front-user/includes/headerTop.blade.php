<!DOCTYPE html>
<html lang="en-us">
<?php
header("Cache-Control: no-cache, must-revalidate");
header("Expires: Mon, 26 Jul 1997 05:00:00 GMT");
header("Content-Type: application/xml; charset=utf-8");
?>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script>
        //window.auth = {!!auth()->user()!!}
    </script>
    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}"> 

    <meta name="twitter:card" content="summary_large_image">
    @if(@$rink->main_photo!='')
    <meta property="twitter:image" content="{{ URL::asset('storage') }}/{{@$rink->main_photo}}" />
    <meta property="og:image" content="{{ URL::asset('storage') }}/{{@$rink->main_photo}}" />
    @else
    <meta property="twitter:image" content="{{ URL::asset('/assets/front-end/images/default-img.jpg')}}" />
    <meta property="og:image" content="{{ URL::asset('/assets/front-end/images/default-img.jpg')}}" />
    @endif
    <meta property="og:title" content="{{config('app.name')}} | {{@$rink->rink_name}}" />
    <meta property="twitter:text" content="{!! @$rink->description !!}" />
    <meta property="og:description" content="{!! @$rink->description !!}" />
    <meta property="og:site_name" content="{{config('app.name')}}" />

    

    <title>{{env('APP_NAME')}}</title>

    <!-- <link rel="icon" type="image/png" sizes="45x45" href="{{ asset('/') }}assets/frontend/images/logo.png"> -->

    <link rel="shortcut icon" href="{{ asset('/') }}assets/front-end/images/icons/favicon.ico" type="image/x-icon">
    <link rel="icon" href="{{ asset('/') }}assets/front-end/images/icons/favicon.ico" type="image/x-icon">

    <link rel="shortcode icon" type="image/png" href="{{ asset('/') }}assets/front-end/images/icons/favicon.ico"/>
    <!-- Scripts -->

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet"> 
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet"> 

    <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css?family=Roboto|Source+Sans+Pro:200,200i,300,300i,400,400i,600,600i,700,700i,900,900i&display=swap" rel="stylesheet"> 
    

    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.1/css/all.css">
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3" crossorigin="anonymous">
    <link rel="stylesheet" type="text/css" href="{{ asset('/assets/front-end/css/custom.css?v=') }}{{ time() }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('/assets/front-end/css/uicons-regular-rounded.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('/assets/front-end/css/uicons-solid-rounded.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('/assets/front-end/css/custom_theme.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('/assets/front-end/css/animate.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('/assets/front-end/css/responsive.css?v=9.0') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('/assets/front-end/css/jquery.mCustomScrollbar.css') }}">

    <link href="https://cdnjs.cloudflare.com/ajax/libs/datepicker/0.6.5/datepicker.min.css" rel="stylesheet"/>
    
    <link rel="dns-prefetch" href="https://fonts.gstatic.com">

    <link href="{{asset('/assets/admin/plugins/sweetalert/sweetalert.css') }}" rel="stylesheet"/>
    <link href="{{ URL::asset('/assets/admin/plugins/toastr/toastr.min.css') }}" rel="stylesheet"/>
    <!-- Styles -->
    <script src="https://code.jquery.com/jquery-3.6.1.min.js" integrity="sha256-o88AwQnZB+VDvE9tvIXrMQaPlFFSUTR+nldQm1LuPXQ=" crossorigin="anonymous"></script>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    {{-- <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet"> --}}
    <link rel="stylesheet" href="//code.jquery.com/ui/1.13.1/themes/base/jquery-ui.css">
    <script src="{{ URL::asset('/assets/admin/plugins/toastr/toastr.min.js') }}"></script>
    
    <script src="https://kit.fontawesome.com/00f8839059.js" crossorigin="anonymous"></script>
    
    <script type="text/javascript" src="https://platform-api.sharethis.com/js/sharethis.js#property=5f4cb344dc8707001906e0e5&product=inline-share-buttons" async="async"></script>
    <script src="https://code.jquery.com/ui/1.13.1/jquery-ui.js"></script>
    
    
    <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/tagmanager/3.0.2/tagmanager.min.css"> 
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.10.2/dist/umd/popper.min.js" integrity="sha384-7+zCNj/IqJ95wo16oMtfsKbZ9ccEh31eOz1HGyDuCQ6wgnyJNSYdrPa03rtR1zdB" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.min.js" integrity="sha384-QJHtvGhmr9XOIpI6YVutG+2QOK9T+ZnN4kzFN1RtK3zEFEIsxhlmWl5/YESvpZ13" crossorigin="anonymous"></script>
    <script type="text/javascript" src="{{ URL::asset('/assets/front-end/js/parsley.min.js') }}"></script>
    <script type="text/javascript" src="{{ URL::asset('/assets/front-end/js/confirmbutton.js') }}"></script>
    <script type="text/javascript" src="{{ URL::asset('/assets/front-end/js/custom.js') }}"></script>
    <script>
        $(document).ready(function(){
        // $( window ).on( "load", function() {
            $(document).on("click",".toggleLink", function(){
                $(".dashboard-left").toggleClass("open");
                $(".dashboard-right").toggleClass("open");
            })
            if($(window).width() <= 1499){
                // $(".dashboard-left").addClass("open");
                // $(".dashboard-right").addClass("open");
                $(document).on("click",".toggleLink", function(){
                    $(".menuoverlay").toggle();
                    $("span.tabText").toggleClass("openTogle");
                    $("span.numberTab").toggleClass("openNumber");
                })
                $(document).on("click",".menuoverlay", function(){
                    $(".dashboard-left").toggleClass("open");
                    $(".dashboard-right").toggleClass("open");
                    $(this).toggle();
                    $("span.tabText").toggleClass("openTogle");
                    $("span.numberTab").toggleClass("openNumber");
                })
            }
        })
        $(document).on("click",".havesubmenu ", function(){
            $(this).toggleClass("toggleMenu");
            $(this).next(".menusal").toggleClass("opwnMenu")
        })
    </script>


    @yield('style')
</head>

@if(Auth::check())
<body class="{{Request::segment(1)}}-layout logedin">
@else
<body class="{{Request::segment(1)}}-layout not-logedin">  
@endif
    
<!-- <body class="{{Request::segment(1)}}-layout "> -->