@extends('admin.layouts.master_admin')

@section('page_title')
{{config('app.name')}} | Manage Influencers
@endsection


@section('content')

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Manage Influencers</h1>
                </div>

                <div class="col-sm-6 text-right">
                    <a href="{{Request::root()}}/admin/add-influencer" class="btn btn-success btn-gold-styled pull-right w-auto d-inline-flex"><i class="fa fa-plus"></i> Add Influencer</a>
                </div>
            </div>
        </div><!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    
    <section class="content">

        <!-- Default box -->
        <div class="card">
            <div class="card-header">

                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse" data-toggle="tooltip"
                            title="Collapse">
                        <i class="fas fa-minus"></i></button>
                    <button type="button" class="btn btn-tool" data-card-widget="remove" data-toggle="tooltip"
                            title="Remove">
                        <i class="fas fa-times"></i></button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table" id="example1">
                        <thead>
                        <tr>
                            <th class="column-title">Created At </th>
                            <th class="column-title">Name </th>
                            <th class="column-title">Email </th>
                            <th class="column-title">Subscribed </th>
                            <th class="column-title" title="Tax Identification Number">Tax IN </th>
                            <th class="column-title">Status </th>
                            <th class="column-title">Activate</th>
                            <th class="column-title">Country </th>
                            <th class="column-title">Category </th>
                            <th class="column-title">Phone Number </th> 
                            <th class="column-title">Instagram </th>
                            <th class="column-title">Facebook </th>
                            <th class="column-title">Twitter </th>
                            <th class="column-title">Youtube </th>
                            <th class="column-title">Tiktok </th> 
                            <th class="column-title">Twitch </th> 
                            <th class="column-title text-center">Action</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php $i = 1;  ?>
                        @foreach($result as $row)
                            <tr>
                                
    
                                <td>{{ @$row->created_at }}</td>
                                <td>{{ @$row->first_name.' '.@$row->last_name }}</td>
                                <td>
                                    {{ @$row->email }}
                                </td>
                                
                                <td> 
                                    @if (@$row->activate == '1')
                                        <span class="badge badge-success">Yes</span>
                                    @else
                                        <span class="badge badge-danger">No</span>
                                    @endif
                                </td>
                                <td>
                                    @if (@$row->identification_no == '1')
                                    <span class="badge badge-success">Yes</span>
                                @else
                                    <span class="badge badge-danger">No</span>
                                @endif
                                </td>
                                <td> 
                                    <div class="smc-toggle-input">
                                        <input type="checkbox" class="activate_user" name="activate_user[]" value="{{ @$row->id }}" @if(@$row->flag == 1) checked @endif >
                                        <label></label>
                                    </div> 
                                </td>
                                <td> 
                                    <div class="smc-toggle-input">
                                        <input type="checkbox" class="set_password set_password " name="set_password []" value="{{ @$row->id }}" @if(@$row->set_password  == 1) checked @endif  @if(@$row->flag != 1) disabled @endif >
                                        <label></label>
                                    </div> 
                                </td>
                                <td>{{ @$row->countries->name }}</td>
                                <td>{{ @$row->category_id }}</td>
                                <td>{{ @$row->phone }}</td>


                                <td>{{ @$row->instagram_name }}</td>
                                <td>{{ @$row->facebook_name }}</td>
                                <td>{{ @$row->twitter_name }}</td>
                                <td>{{ @$row->youtube_name }}</td>
                                <td>{{ @$row->tiktok_name }}</td>
                                <td>{{ @$row->twitch_name }}</td>
                                <td class="text-center">
                                    <!-- Removed impersonate buttons from here -->
                                    <a href="{{URL::to('/admin/edit-influencer',['id'=>@$row->id])}}" title="Edit"><i class="fa fa-edit fa-fw fa-lg"></i></a>
                                    <a href="javascript:void(0);" class="deleteBusinessService" id="{{@$row->id}}" title="Delete"><i class="fa fa-trash fa-fw fa-lg"></i></a>
                                </td>

                            </tr>
                            <?php $i++; ?>
                        @endforeach
                        
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- /.card-body -->
            <div class="card-footer">
                {{--Footer--}}
            </div>
            <!-- /.card-footer-->
        </div>
        <!-- /.card -->

    </section>
    <!-- /.content -->
@endsection


@section('admin_script_links')  
@endsection
@section('admin_script_codes')
<script>
    $(document).ready(function() {
        $('#example1').DataTable(
        {
            "order": [[ 0, "desc" ]],
            "columnDefs": [
                {
                    "targets": [ 0 ],
                    "visible": false,
                    "searchable": false
                },
                {
                    "targets": [11],
                    "orderable": false
                }
            ]
        });
    } );
</script>
<script type="text/javascript">
    
    $('.set_password').click(function() { 
             var id =$(this).val();
             var type = $(this).is(':checked')?1:0;
        //  if($(this).is(':checked')){
             console.log($(this).val()); 
            $.ajax({
                url: "{{URL::to('/admin/set_password')}}/"+id+"/"+type,  
                method: 'GET',  
            }).done(function (data) {    
                toastr.success(data.msg);
                setTimeout(function () {
                    location.reload();
                }, 1000)
            }); 
      //  } 
    });

    $('.activate_user').click(function() { 
             var id =$(this).val();
         if($(this).is(':checked')){
             console.log($(this).val()); 
            $.ajax({
                url: "{{URL::to('/admin/activate-user')}}/"+id,  
                method: 'GET',  
            }).done(function (data) {    
                toastr.success(data.msg);
                setTimeout(function () {
                    location.reload();
                }, 5000)
            }); 
        }else{
            $.ajax({
                url: "{{URL::to('/admin/deactivate-user')}}/"+id,  
                method: 'GET',  
            }).done(function (data) {    
                toastr.success(data.msg);
                setTimeout(function () {
                    location.reload();
                }, 5000)
            }); 
        }
    });
    
    $(document).ready(function () {

            $("#example1").on("click", ".deleteBusinessService", function (e) {

                e.preventDefault();
                let id = $(this).attr('id');
                swal({
                        title: "Are you sure?",
                        text: "You will not be able to recover this Influencer!",
                        type: "warning",
                        showCancelButton: true,
                        confirmButtonClass: "btn-danger",
                        confirmButtonText: "Yes, delete it!",
                        cancelButtonText: "No, cancel please!",
                        closeOnConfirm: false,
                        closeOnCancel: false
                    },
                    function (isConfirm) {
                        if (isConfirm) {
                            $.ajax({
                                type: "post",
                                url: "{{ url('/admin/delete-influencer') }}",
                                data: {
                                    "_token": "{{ csrf_token() }}",
                                    "id": id
                                },
                                success: function (response) {

                                    if (response.status == "success") {
                                        toastr.success(response.msg);

                                        setTimeout(function () {
                                            location.reload();
                                        }, 5000)

                                    }
                                    if (response.status == "error") {
                                        toastr.info(response.msg);
                                        setTimeout(function () {
                                            location.reload();
                                        }, 5000)
                                    }
                                }
                            });
                            swal("Deleted!", "Influencer deleted successfully.", "success");
                        } else {
                            swal("Cancelled", "Influencer is safe :)", "error");
                        }
                    });
            });
        });

</script>
@endsection
