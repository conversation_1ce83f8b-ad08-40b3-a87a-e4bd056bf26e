@extends('admin.layouts.master_admin')

@section('page_title')
{{config('app.name')}} | Influencer Pricing Details
@endsection

@section('content')
    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Pricing Details: {{ $user->first_name }} {{ $user->last_name }}</h1>
                </div>
                <div class="col-sm-6 text-right">
                    <a href="{{URL::to('/admin/manage-influencers')}}" class="btn btn-secondary btn-gold-styled mr-2">
                        <i class="fa fa-arrow-left"></i> Back to Influencers
                    </a>
                    <button type="button" class="btn btn-warning btn-gold-styled" id="reprice-btn">
                        <i class="fa fa-calculator"></i> Reprice
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Main content -->
    <section class="content">
        <!-- Influencer Info Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="card-title">Influencer Information</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>Name:</strong> {{ $user->first_name }} {{ $user->last_name }}
                    </div>
                    <div class="col-md-3">
                        <strong>Email:</strong> {{ $user->email }}
                    </div>
                    <div class="col-md-3">
                        <strong>Followers:</strong> {{ number_format($influencer->followers) }}
                    </div>
                    <div class="col-md-3">
                        <strong>Gamification:</strong> {{ number_format($influencer->gamification_percentage * 100, 1) }}%
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-4">
                        <strong>Story Avg Reach:</strong> 
                        @if($influencer->flag_story_insight_missing)
                            <span class="badge badge-warning">Missing Data</span>
                        @else
                            {{ $influencer->ig_story_avg_reach ? number_format($influencer->ig_story_avg_reach) : 'N/A' }}
                        @endif
                    </div>
                    <div class="col-md-4">
                        <strong>Reel Avg Reach:</strong> 
                        @if($influencer->flag_reel_insight_missing)
                            <span class="badge badge-warning">Missing Data</span>
                        @else
                            {{ $influencer->ig_reel_avg_reach ? number_format($influencer->ig_reel_avg_reach) : 'N/A' }}
                        @endif
                    </div>
                    <div class="col-md-4">
                        <strong>Feed Avg Reach:</strong> 
                        @if($influencer->flag_feed_insight_missing)
                            <span class="badge badge-warning">Missing Data</span>
                        @else
                            {{ $influencer->ig_feed_avg_reach ? number_format($influencer->ig_feed_avg_reach) : 'N/A' }}
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Pricing Cards -->
        <div class="row">
            @foreach($pricingData as $campaignType => $priceRecord)
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-primary text-white">
                            <h4 class="card-title mb-0">{{ $campaignType }}</h4>
                        </div>
                        <div class="card-body">
                            @if($priceRecord)
                                <div class="pricing-info">
                                    <div class="price-display mb-3">
                                        <h2 class="text-success">${{ number_format($priceRecord->price, 2) }}</h2>
                                        <small class="text-muted">Post Type: {{ ucfirst($priceRecord->post_type) }}</small>
                                    </div>
                                    
                                    <div class="breakdown-info">
                                        <h6>Calculation Breakdown:</h6>
                                        @php $breakdown = $priceRecord->breakdown; @endphp
                                        
                                        <div class="breakdown-item">
                                            <strong>Tier CPT:</strong> ${{ $breakdown['tier']['cpt'] ?? 'N/A' }}
                                        </div>
                                        <div class="breakdown-item">
                                            <strong>Tier Rate:</strong> {{ ($breakdown['tier']['rate'] ?? 0) * 100 }}%
                                        </div>
                                        <div class="breakdown-item">
                                            <strong>Estimated Reach:</strong> {{ number_format($breakdown['estimated_reach'] ?? 0) }}
                                        </div>
                                        <div class="breakdown-item">
                                            <strong>Actual Avg Reach:</strong> {{ $breakdown['avg_reach'] ? number_format($breakdown['avg_reach']) : 'N/A' }}
                                        </div>
                                        @if(isset($breakdown['reach_multiplier']) && $breakdown['reach_multiplier'])
                                            <div class="breakdown-item">
                                                <strong>Reach Multiplier:</strong> {{ number_format($breakdown['reach_multiplier'], 2) }}x
                                            </div>
                                        @endif
                                        <div class="breakdown-item">
                                            <strong>Gamification Bonus:</strong> {{ number_format($breakdown['gamification_percentage'] * 100, 1) }}%
                                        </div>
                                    </div>
                                    
                                    <div class="pricing-meta mt-3">
                                        <small class="text-muted">
                                            <i class="fa fa-clock"></i> 
                                            Priced: {{ $priceRecord->priced_at->format('M j, Y g:i A') }}
                                        </small>
                                    </div>
                                </div>
                            @else
                                <div class="no-pricing text-center">
                                    <i class="fa fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                                    <h5>No Pricing Data</h5>
                                    <p class="text-muted">Click "Reprice" to calculate pricing for this campaign type.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </section>

    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" role="dialog" aria-labelledby="loadingModalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <h5>Repricing Influencer...</h5>
                    <p class="text-muted">This may take a few moments. Please wait.</p>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('admin_script_codes')
<script>
$(document).ready(function() {
    $('#reprice-btn').click(function() {
        // Show loading modal
        $('#loadingModal').modal('show');
        
        // Disable the button
        $(this).prop('disabled', true);
        
        $.ajax({
            url: "{{ route('admin.influencer-pricing.reprice', $user->id) }}",
            method: 'POST',
            data: {
                _token: "{{ csrf_token() }}"
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    // Refresh the page immediately since repricing is complete
                    location.reload();
                } else {
                    toastr.error(response.message || 'An error occurred');
                    $('#loadingModal').modal('hide');
                    $('#reprice-btn').prop('disabled', false);
                }
            },
            error: function(xhr) {
                let message = 'An error occurred while repricing';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                toastr.error(message);
                $('#loadingModal').modal('hide');
                $('#reprice-btn').prop('disabled', false);
            }
        });
    });
});
</script>

<style>
.breakdown-item {
    padding: 2px 0;
    font-size: 0.9em;
}

.price-display h2 {
    margin-bottom: 5px;
}

.pricing-info {
    font-size: 0.95em;
}

.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.no-pricing {
    padding: 20px 0;
}
</style>
@endsection
