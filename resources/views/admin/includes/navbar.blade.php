
<style>
.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.switch input { 
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: .4s;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  -webkit-transition: .4s;
  transition: .4s;
}

input:checked + .slider {
  background-color: green;
}

input:focus + .slider {
  box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
  -webkit-transform: translateX(26px);
  -ms-transform: translateX(26px);
  transform: translateX(26px);
}

/* Rounded sliders */
.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}
</style>

<!-- Navbar -->
<nav class="main-header navbar navbar-expand navbar-white navbar-light d-flex">
    <!-- Left navbar links -->
    <ul class="navbar-nav">
        <li class="nav-item">
            <a class="nav-link" data-widget="pushmenu" href="#"><i class="fas fa-bars"></i></a>
        </li>
    </ul>


    <!-- Right navbar links -->
    <ul class="navbar-nav ml-auto">

        @php $modes = DB::table('modes')->first(); @endphp 
        <label class="switch">
           <input onchange="makeLive()" class="activate make_live" type="checkbox" @if(@$modes->status==1) checked @endif >
          <span class="slider round"></span>
        </label>


        <!-- Messages Dropdown Menu -->
        @if (Route::has('admin.login'))
            <div class="top-right links">
                @if(Auth::guard('admin')->check() || (Auth::guard('web')->check() && Auth::guard('web')->user()->user_type === 'admin'))

                    {{--<a href="{{ route('admin.logout') }}">Logout</a>--}}

                    @php
                        $user = Auth::guard('admin')->check() ? Auth::guard('admin')->user() : Auth::guard('web')->user();
                        $notification = DB::table('notifications')->where('notifiable_id', $user->id)->where('read_at', NULL)->count();
                    @endphp
                    <li class="notification">
                        <a href="{{URL::to('admin/notifications')}}">
                          <img src="{{ asset('/') }}/assets/front-end/images/icons/ball_icon.svg" alt="">
                          <span class="count">{{$notification}}</span>
                        </a>
                    </li>
                    <li class="nav-item d-none d-sm-inline-block">
                        Welcome <strong>{{$user->first_name.' '.$user->last_name}}</strong>
                    </li>

                    <li class="nav-item d-none d-sm-inline-block"><a href="#"></a></li>
                    <li class="dropdown ">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown"> <span class="caret"></span></a>
                        <ul class="dropdown-menu" role="menu">
                            <li class="nav-item d-none d-sm-inline-block">
                                <a href="{{ route('admin.profile') }}" class="nav-link">   <i class="fas fa-id-badge"></i> Profile</a>
                            </li>
                            <li class="nav-item d-none d-sm-inline-block">
                                <a href="{{ route('admin.change-password') }}" class="nav-link">   <i class="fas fa-lock"></i> Change Password</a>
                            </li>
                            <li class="nav-item d-none d-sm-inline-block">
                                <a href="{{ route('admin.logout') }}" class="nav-link">   <i class="fas fa-power-off"></i> Logout</a>
                            </li>
                        </ul>
                    </li>
                @else
                    <a href="{{ route('admin.login') }}">Login</a>
                @endif
            </div>
        @endif
    </ul>
</nav>
<!-- /.navbar -->