
<div  class="top-secret">
    <aside id="main-sidebar" class="main-sidebar sidebar-dark-primary elevation-4 sidebarSmal">
        <!-- Brand Logo -->
        <a href="{{url('/')}}" class="brand-link">
            <img src="{{asset('/assets/admin/img/logo_white.svg')}}" class="brand-image">
        </a>

        <!-- Sidebar -->
        <div class="sidebar">
            <!-- Sidebar user panel (optional) -->
            {{-- <div class="user-panel mt-3 pb-3 mb-3 d-flex">
                <div class="image">

                    <?php
                    if(Auth::check() && Auth::guard('admin')->check() && (Auth::User()->profile_pic == '' || Auth::User()->profile_pic == null))
                        $src = URL::asset("/assets/front-end/images/icons/default-profile-pic.png").;
                    else
                        $src = URL::to('storage/app')."/".Auth::User()->profile_pic;
                    ?>

                    <img src="<?php echo ($src); ?>" class="img-circle elevation-2" id="adminProfileImgSidebar">
                </div>
                <div class="info">
                    <a href="#" class="d-block">{{ ucfirst(Auth::User()->first_name)}} {{ucfirst(Auth::User()->last_name)}}</a>
                </div>
            </div> --}}

            <!-- Sidebar Menu -->
            <nav class="mt-2">
                <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                    <!-- Add icons to the links using the .nav-icon class
                        with font-awesome or any other icon font library -->

                    <li class="nav-item has-treeview">
                        <a href="{{URL::to('admin')}}" class="nav-link {{ (Request::segment(1) === 'admin' && Request::segment(2) === '' ) ? 'active' : '' }}">
                            <i class="fas fa-tachometer-alt"></i>
                            <p>Dashboard</p>
                        </a>
                    </li>

                    <li class="nav-item has-treeview">
                        <a href="#" class="nav-link">
                            <i class="fas fa-users"></i>

                            <p>
                                Manage Users
                                <i class="right fas fa-angle-left"></i>
                            </p>
                        </a>
                        <ul class="nav nav-treeview">
                            <li class="nav-item">
                                <a href="{{URL::to('admin/manage-customers')}}" class="nav-link {{ URL::to('admin/manage-customers') == url()->current() || URL::to('admin/add-customer') == url()->current() || URL::to('admin/edit-customer').'/'.collect(request()->segments())->last() == url()->current() ? 'active' : ' ' }}">
                                <i class="fas fa-user-tie"></i>

                                    <p>Manage Brands</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="{{URL::to('admin/manage-influencers')}}" class="nav-link {{ URL::to('admin/manage-influencers') == url()->current() || URL::to('admin/add-influencer') == url()->current() || URL::to('admin/edit-influencer').'/'.collect(request()->segments())->last() == url()->current() ? 'active' : ' ' }}">
                                <i class="fas fa-user-alt"></i>

                                    <p>Manage Influencers</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="{{URL::to('admin/manage-users')}}" class="nav-link {{ URL::to('admin/manage-users') == url()->current() ? 'active' : ' ' }}">
                                    <i class="fas fa-mask"></i>
                                    <p>User Impersonation</p>
                                </a>
                            </li>

                        </ul>
                    </li>

                    <li class="nav-item has-treeview">
                        <a href="#" class="nav-link">
                            <i class="fas fa-clipboard-list"></i>

                            <p>
                                Manage CMS
                                <i class="right fas fa-angle-left"></i>
                            </p>
                        </a>
                        <ul class="nav nav-treeview">
                            
                            <li class="nav-item">
                                <a href="{{URL::to('admin/manage-blogs')}}" class="nav-link {{ URL::to('admin/manage-blogs') == url()->current() || URL::to('admin/add-blog') == url()->current() || URL::to('admin/edit-blog').'/'.collect(request()->segments())->last() == url()->current() ? 'active' : ' ' }}">
                                    <i class="fas fa-tasks"></i>

                                    <p>Manage Blogs</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="{{URL::to('admin/manage-faq-topics')}}" class="nav-link {{ URL::to('admin/manage-faq-topics') == url()->current() || URL::to('admin/add-faq-topic') == url()->current() || URL::to('admin/edit-faq-topic').'/'.collect(request()->segments())->last() == url()->current() ? 'active' : ' ' }}">
                                    <i class="fas fa-tasks"></i>

                                    <p>Manage FAQ Topics</p>
                                </a>
                            </li> 
                            <li class="nav-item">
                                <a href="{{URL::to('admin/manage-faqs')}}" class="nav-link {{ URL::to('admin/manage-faqs') == url()->current() || URL::to('admin/add-faq') == url()->current() || URL::to('admin/edit-faq').'/'.collect(request()->segments())->last() == url()->current() ? 'active' : ' ' }}">
                                    <i class="fas fa-tasks"></i>

                                    <p>Manage FAQs</p>
                                </a>
                            </li>  

                            <li class="nav-item">
                                <a href="{{URL::to('admin/manage-pages')}}" class="nav-link {{ URL::to('admin/manage-pages') == url()->current() || URL::to('admin/add-page') == url()->current() || URL::to('admin/edit-page').'/'.collect(request()->segments())->last() == url()->current() ? 'active' : ' ' }}">
                                    <i class="fas fa-tasks"></i>

                                    <p>Manage Other Pages</p>
                                </a>
                            </li>
                            
                        </ul>
                    </li>
                    <li class="nav-item has-treeview">
                        <a href="#" class="nav-link">
                            <i class="fas fa-clipboard-list"></i>

                            <p>
                                Manage Fields
                                <i class="right fas fa-angle-left"></i>
                            </p>
                        </a>
                        <ul class="nav nav-treeview">

                         <li class="nav-item has-treeview">
                            <a href="{{URL::to('admin/manage-category')}}"  class="nav-link {{ URL::to('admin/manage-category') == url()->current() || URL::to('admin/add-category') == url()->current() || URL::to('admin/edit-category').'/'.collect(request()->segments())->last() == url()->current() ? 'active' : ' ' }}" >
                                <i class="fas fa-tasks"></i>
                                <p>Categories</p>
                            </a>
                        </li>


                         <li class="nav-item has-treeview">
                            <a href="{{URL::to('admin/manage-social')}}"  class="nav-link {{ URL::to('admin/manage-social') == url()->current()   ? 'active' : ' ' }}" >
                                <i class="fas fa-tasks"></i>
                                <p>Social App Keys</p>
                            </a>
                        </li>

                         <li class="nav-item has-treeview">
                            <a href="{{URL::to('admin/manage-dialogue')}}"  class="nav-link {{ URL::to('admin/manage-dialogue') == url()->current() || URL::to('admin/edit-dialogue').'/'.collect(request()->segments())->last() == url()->current()  ? 'active' : ' ' }}" >
                                <i class="fas fa-tasks"></i>
                                <p>Dialogue Content</p>
                            </a>
                        </li>

                         <li class="nav-item has-treeview">
                            <a href="{{URL::to('admin/manage-campaign-time')}}"  class="nav-link {{ URL::to('admin/manage-campaign-time') == url()->current()  ? 'active' : ' ' }}" >
                                <i class="fas fa-tasks"></i>
                                <p>Campaign request time</p>
                            </a>
                        </li>


                         <li class="nav-item has-treeview">
                            <a href="{{URL::to('admin/manage-comission')}}"  class="nav-link {{ URL::to('admin/manage-comission') == url()->current()  ? 'active' : ' ' }}" >
                                <i class="fas fa-tasks"></i>
                                <p>Manage admin comission</p>
                            </a>
                        </li>

                         <li class="nav-item has-treeview">
                            <a href="{{URL::to('admin/manage-hashtag')}}"  class="nav-link {{ URL::to('admin/manage-hashtag') == url()->current()  ? 'active' : ' ' }}" >
                                <i class="fas fa-tasks"></i>
                                <p>Manage admin hashtag</p>
                            </a>
                        </li>

                    </ul>
                </li>

                <li class="nav-item has-treeview">
                    <a href="{{URL::to('admin/manage-modes')}}" class="nav-link {{ (Request::segment(1) === 'admin/manage-modes' ) ? 'active' : '' }}">
                        <i class="fas fa-tachometer-alt"></i>
                        <p>Manage Mode Values</p>
                    </a>
                </li>

                <li class="nav-item has-treeview">
                    <a href="{{URL::to('admin/manage-complaints')}}" class="nav-link {{ (Request::segment(1) === 'admin/manage-complaints' ) ? 'active' : '' }}">
                        <i class="fas fa-tachometer-alt"></i>
                        <p>Manage Complaints</p>
                    </a>
                </li>

                <li class="nav-item has-treeview">
                    <a href="{{URL::to('admin/manage-sm-campaign')}}" class="nav-link {{ (Request::segment(1) === 'admin/manage-sm-campaign' ) ? 'active' : '' }}">
                        <i class="fas fa-tachometer-alt"></i>
                        <p>SM & Campaign</p>
                    </a>
                </li>


                <li class="nav-item has-treeview">
                    <a href="{{URL::to('admin/manage-disputes')}}" class="nav-link {{ (Request::segment(1) === 'admin/manage-disputes' ) ? 'active' : '' }}">
                        <i class="fas fa-tachometer-alt"></i>
                        <p>Manage Disputes</p>
                    </a>
                </li>


                <li class="nav-item has-treeview">
                    <a href="{{URL::to('admin/manage-pricing')}}" class="nav-link {{ (Request::segment(1) === 'admin/manage-pricing' ) ? 'active' : '' }}">
                        <i class="fas fa-tachometer-alt"></i>
                        <p>Pricing</p>
                    </a>
                </li>
                <li class="nav-item has-treeview">
                    <a href="{{URL::to('admin/manage-gamification')}}" class="nav-link {{ (Request::segment(1) === 'admin/manage-gamification' ) ? 'active' : '' }}">
                        <i class="fas fa-tachometer-alt"></i>
                        <p>Gamification</p>
                    </a>
                </li>
                <li class="nav-item has-treeview">
                    <a href="{{URL::to('admin/manage-campaigns')}}" class="nav-link {{ (Request::segment(1) === 'admin/manage-campaigns' ) ? 'active' : '' }}">
                        <i class="fas fa-tachometer-alt"></i>
                        <p>Manage Campaigns</p>
                    </a>
                </li>
                <li class="nav-item has-treeview">
                    <a href="{{URL::to('admin/manage-paused-campaigns')}}" class="nav-link {{ (Request::segment(1) === 'admin/manage-paused-campaigns' ) ? 'active' : '' }}">
                        <i class="fas fa-tachometer-alt"></i>
                        <p>&nbsp;Manage Paused Campaigns</p>
                    </a>
                </li>

                <li class="nav-item has-treeview">
                    <a href="{{URL::to('admin/manage-tasks')}}" class="nav-link {{ (Request::segment(1) === 'admin/manage-tasks' ) ? 'active' : '' }}">
                        <i class="fas fa-tachometer-alt"></i>
                        <p>Manage Tasks</p>
                    </a>
                </li>


                </ul>
            </nav>
            <!-- /.sidebar-menu -->
        </div>
        <!-- /.sidebar -->
    </aside>
</div>


<script>
$(document).ready(function(){
    if ($('.nav-treeview li a.nav-link').hasClass('active')) {
        $('.nav-treeview li a.nav-link.active').parent().parent().parent().addClass('menu-open');
    }
});


</script>
