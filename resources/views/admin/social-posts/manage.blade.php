@extends('admin.layouts.master_admin')

@section('page_title')
{{config('app.name')}} | Manage Social Posts
@endsection

<style>
    .table-responsive {
        border: none !important;
    }
</style>

@section('content')

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Manage Social Posts</h1>
                </div>
            </div>
        </div><!-- /.container-fluid -->
    </section>

    <section class="content">

        <!-- Filters -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Filters</h3>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ url('/admin/manage-social-posts') }}">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Search</label>
                                <input type="text" name="search" class="form-control" placeholder="Search by Post ID, Name, Email, Instagram..." value="{{ request('search') }}">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Post Type</label>
                                <select name="post_category" class="form-control">
                                    <option value="">All Post Types</option>
                                    @foreach($postCategories as $category)
                                        <option value="{{ $category }}" {{ request('post_category') == $category ? 'selected' : '' }}>
                                            {{ ucfirst($category) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Content Type</label>
                                <select name="type" class="form-control">
                                    <option value="">All Content Types</option>
                                    @foreach($contentTypes as $type)
                                        <option value="{{ $type }}" {{ request('type') == $type ? 'selected' : '' }}>
                                            {{ ucfirst($type) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Instagram Name</label>
                                <select name="instagram_name" class="form-control">
                                    <option value="">All Instagram Names</option>
                                    @foreach($instagramNames as $instagramName)
                                        <option value="{{ $instagramName }}" {{ request('instagram_name') == $instagramName ? 'selected' : '' }}>
                                            {{ $instagramName }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Email</label>
                                <select name="email" class="form-control">
                                    <option value="">All Emails</option>
                                    @foreach($emails as $email)
                                        <option value="{{ $email }}" {{ request('email') == $email ? 'selected' : '' }}>
                                            {{ $email }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Campaign Deliverable</label>
                                <select name="campaign_deliverable" class="form-control">
                                    <option value="">All Posts</option>
                                    <option value="yes" {{ request('campaign_deliverable') == 'yes' ? 'selected' : '' }}>Campaign Deliverables</option>
                                    <option value="no" {{ request('campaign_deliverable') == 'no' ? 'selected' : '' }}>Non-Campaign Posts</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">Filter</button>
                                <a href="{{ url('/admin/manage-social-posts') }}" class="btn btn-secondary">Clear</a>
                                <a href="{{ url('/admin/manage-social-posts/export-csv') }}{{ request()->getQueryString() ? '?' . request()->getQueryString() : '' }}"
                                   class="btn btn-success">
                                    <i class="fas fa-download"></i> Export CSV
                                </a>
                                <small class="text-muted ml-2">
                                    Export {{ $socialPosts->total() }} posts
                                    @if(request()->hasAny(['search', 'post_category', 'type', 'instagram_name', 'email', 'campaign_deliverable']))
                                        (with current filters applied)
                                    @endif
                                </small>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Default box -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Social Posts ({{ $socialPosts->total() }} total)</h3>
                <div class="card-tools"></div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                        <tr>
                            <th>ID</th>
                            <th>User</th>
                            <th>Instagram</th>
                            <th>Post ID</th>
                            <th>Post Type</th>
                            <th>Content Type</th>
                            <th>Submitted to Campaign?</th>
                            <th>Text Preview</th>
                            <th>Published At</th>
                            <th>Created At</th>
                            <th class="text-center">Action</th>
                        </tr>
                        </thead>
                        <tbody>
                        @forelse($socialPosts as $post)
                            <tr>
                                <td>{{ $post->id }}</td>
                                <td>
                                    <div>
                                        <strong>{{ $post->first_name }} {{ $post->last_name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ $post->email }}</small>
                                        <br>
                                        <small class="text-muted">ID: {{ $post->user_id }}</small>
                                    </div>
                                </td>
                                <td>
                                    @if($post->instagram_name)
                                        <span class="badge badge-success">{{ $post->instagram_name }}</span>
                                    @else
                                        <em class="text-muted">Not set</em>
                                    @endif
                                </td>
                                <td>{{ $post->post_id }}</td>
                                <td>
                                    <span class="badge badge-primary">{{ ucfirst($post->post_category ?? 'Unknown') }}</span>
                                </td>
                                <td>
                                    <span class="badge badge-info">{{ ucfirst($post->type ?? 'Unknown') }}</span>
                                </td>
                                <td>
                                    {{ $post->campaign_deliverable ? 'Yes' : 'No' }}
                                </td>
                                <td>
                                    @if($post->text)
                                        {{ strlen($post->text) > 50 ? substr($post->text, 0, 50) . '...' : $post->text }}
                                    @else
                                        <em class="text-muted">No text</em>
                                    @endif
                                </td>
                                <td>
                                    @if($post->published_at)
                                        {{ $post->published_at->format('Y-m-d H:i') }}
                                    @else
                                        <em class="text-muted">Not set</em>
                                    @endif
                                </td>
                                <td>{{ $post->created_at->format('Y-m-d H:i') }}</td>
                                <td class="text-center">
                                    <a href="{{ url('/admin/social-posts/' . $post->id) }}" class="btn btn-primary" title="View Details">
                                        <i class="fa fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="11" class="text-center">
                                    <em class="text-muted">No social posts found</em>
                                </td>
                            </tr>
                        @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- /.card-body -->
            <div class="card-footer">
                {{ $socialPosts->appends(request()->query())->links('vendor.pagination.admin-bootstrap-4') }}
            </div>
            <!-- /.card-footer-->
        </div>
        <!-- /.card -->

    </section>
    <!-- /.content -->
@endsection

@section('admin_script_links')
@endsection

@section('admin_script_codes')
<style>
    /* Custom pagination styling for admin panel */
    .pagination {
        display: flex;
        justify-content: center;
        margin: 20px 0;
        flex-wrap: wrap;
    }

    .pagination .page-item {
        margin: 0 1px;
    }

    .pagination .page-link {
        color: #495057;
        background-color: #fff;
        border: 1px solid #dee2e6;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.25rem;
        text-decoration: none;
        transition: all 0.15s ease-in-out;
        min-width: 40px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .pagination .page-link:hover {
        color: #0056b3;
        background-color: #e9ecef;
        border-color: #adb5bd;
        text-decoration: none;
    }

    .pagination .page-item.active .page-link {
        color: #fff;
        background-color: #007bff;
        border-color: #007bff;
    }

    .pagination .page-item.disabled .page-link {
        color: #6c757d;
        background-color: #fff;
        border-color: #dee2e6;
        cursor: not-allowed;
        opacity: 0.65;
    }

    /* Fix for FontAwesome icons in pagination - ensure consistent height */
    .pagination .page-link i {
        font-size: 14px !important;
        line-height: 1.5;
        display: inline-block;
        vertical-align: middle;
    }

    /* Ensure all pagination buttons have consistent height and width */
    .pagination .page-link {
        min-height: 38px;
        min-width: 38px;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 1.5;
    }

    /* Ensure first and last page links (arrow buttons) are same size as numbered buttons */
    .pagination .page-item:first-child .page-link,
    .pagination .page-item:last-child .page-link {
        padding: 0.375rem 0.75rem;
        min-width: 38px;
        min-height: 38px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Remove any pseudo-elements that might cause issues */
    .pagination .page-link::before,
    .pagination .page-link::after {
        display: none !important;
    }

    /* Ensure proper spacing in card footer */
    .card-footer {
        padding: 0.75rem 1.25rem;
        background-color: rgba(0,0,0,.03);
        border-top: 1px solid rgba(0,0,0,.125);
    }

    /* Responsive pagination for mobile devices */
    @media (max-width: 576px) {
        .pagination {
            font-size: 0.75rem;
        }

        .pagination .page-link {
            padding: 0.25rem 0.5rem;
            min-width: 32px;
            min-height: 32px;
            font-size: 0.75rem;
        }

        .pagination .page-item:first-child .page-link,
        .pagination .page-item:last-child .page-link {
            padding: 0.25rem 0.5rem;
            min-width: 32px;
            min-height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .pagination .page-link i {
            font-size: 12px !important;
        }
    }

    /* Table responsive improvements */
    .table-responsive {
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
    }

    @media (max-width: 768px) {
        .table-responsive table {
            font-size: 0.875rem;
        }

        .table-responsive .btn-sm {
            padding: 0.125rem 0.25rem;
            font-size: 0.75rem;
        }
    }
</style>

<script>
    $(document).ready(function() {
        // Any additional JavaScript can go here
    });
</script>
@endsection
