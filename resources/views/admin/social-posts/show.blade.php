@extends('admin.layouts.master_admin')

@section('page_title')
{{config('app.name')}} | Social Post Details
@endsection

@section('content')

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Social Post Details</h1>
                </div>
                <div class="col-sm-6 text-right">
                    <a href="{{ url('/admin/manage-social-posts') }}" class="btn btn-secondary">
                        <i class="fa fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>
        </div><!-- /.container-fluid -->
    </section>

    <section class="content">

        <!-- Basic Information -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Basic Information</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">ID:</th>
                                <td>{{ $socialPost->id }}</td>
                            </tr>
                            <tr>
                                <th>User:</th>
                                <td>
                                    <div>
                                        <strong>{{ $socialPost->first_name }} {{ $socialPost->last_name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ $socialPost->email }}</small>
                                        <br>
                                        <small class="text-muted">ID: {{ $socialPost->user_id }}</small>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th>Instagram:</th>
                                <td>
                                    @if($socialPost->instagram_name)
                                        <span class="badge badge-success">{{ $socialPost->instagram_name }}</span>
                                    @else
                                        <em class="text-muted">Not connected</em>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <th>Post ID:</th>
                                <td>{{ $socialPost->post_id }}</td>
                            </tr>
                            <tr>
                                <th>Media:</th>
                                <td>{{ $socialPost->media }}</td>
                            </tr>
                            <tr>
                                <th>Post Type:</th>
                                <td><span class="badge badge-primary">{{ ucfirst($socialPost->post_category ?? 'Unknown') }}</span></td>
                            </tr>
                            <tr>
                                <th>Content Type:</th>
                                <td><span class="badge badge-info">{{ ucfirst($socialPost->type ?? 'Unknown') }}</span></td>
                            </tr>
                            <tr>
                                <th>Submitted to Campaign?:</th>
                                <td>
                                    <strong>{{ $socialPost->campaign_deliverable ? 'Yes' : 'No' }}</strong>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Published At:</th>
                                <td>
                                    @if($socialPost->published_at)
                                        {{ $socialPost->published_at->format('Y-m-d H:i:s') }}
                                        <span class="text-muted ml-2">({{ $socialPost->published_at->diffForHumans() }})</span>
                                    @else
                                        <em class="text-muted">Not set</em>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <th>Created At:</th>
                                <td>
                                    {{ $socialPost->created_at->format('Y-m-d H:i:s') }}
                                    <span class="text-muted ml-2">({{ $socialPost->created_at->diffForHumans() }})</span>
                                </td>
                            </tr>
                            <tr>
                                <th>Updated At:</th>
                                <td>
                                    {{ $socialPost->updated_at->format('Y-m-d H:i:s') }}
                                    <span class="text-muted ml-2">({{ $socialPost->updated_at->diffForHumans() }})</span>
                                </td>
                            </tr>
                            <tr>
                                <th>Original Post:</th>
                                <td>
                                    @if($socialPost->thumbnail)
                                        <a href="{{ $socialPost->thumbnail }}" target="_blank">
                                            View original post
                                        </a>&nbsp;
                                        <a href="{{ $socialPost->thumbnail }}" target="_blank"><i class="fa fa-external-link-alt"></i></a>
                                    @else
                                        <em class="text-muted">No original post available.</em>
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Post Text -->
        @if($socialPost->text)
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Post Text</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-light">
                    {{ $socialPost->text }}
                </div>
            </div>
        </div>
        @endif

        <!-- Media Content -->
        @if($mediaUrl && $mediaType)
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Media Content</h3>
            </div>
            <div class="card-body text-center">
                @if($mediaType === 'image')
                    <img src="{{ $mediaUrl }}" alt="Social Post Image" class="img-fluid" style="max-height: 500px;">
                @elseif($mediaType === 'video')
                    <video controls class="img-fluid" style="max-height: 500px;">
                        <source src="{{ $mediaUrl }}" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                @endif
                <div class="mt-2">
                    <small class="text-muted">File: {{ $socialPost->link }}</small>
                </div>
            </div>
        </div>
        @endif

        <!-- Historical Insights Data -->
        @php
            $historicalInsights = [];
            if (!empty($socialPost->insights) && is_array($socialPost->insights)) {
                foreach ($socialPost->insights as $key => $value) {
                    if (strpos($key, 'complete__') === 0) {
                        $timestamp = str_replace('complete__', '', $key);
                        $historicalInsights[$timestamp] = $value;
                    }
                }
                // Sort by timestamp descending (newest first)
                krsort($historicalInsights);
            }
        @endphp

        @if(!empty($historicalInsights))
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-chart-line"></i> Historical Insights Data
                    <small class="text-muted">({{ count($historicalInsights) }} updates)</small>
                </h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="thead-dark">
                            <tr>
                                <th width="20%">
                                    <i class="fas fa-clock"></i> Update Time
                                </th>
                                <th width="15%">
                                    <i class="fas fa-heart"></i> Likes
                                </th>
                                <th width="15%">
                                    <i class="fas fa-comment"></i> Comments
                                </th>
                                <th width="15%">
                                    <i class="fas fa-eye"></i> Views
                                </th>
                                <th width="15%">
                                    <i class="fas fa-users"></i> Reach
                                </th>
                                <th width="15%">
                                    <i class="fas fa-share"></i> Shares
                                </th>
                                <th width="5%">
                                    <i class="fas fa-info-circle"></i>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($historicalInsights as $timestamp => $data)
                                @php
                                    $metrics = [];
                                    if (isset($data['data']) && is_array($data['data'])) {
                                        foreach ($data['data'] as $insight) {
                                            $metricName = $insight['name'] ?? '';
                                            $metricValue = $insight['values'][0]['value'] ?? 0;
                                            $metrics[$metricName] = $metricValue;
                                        }
                                    }

                                    try {
                                        $updateTime = \Carbon\Carbon::createFromFormat('Y_m_d_H_i_s', $timestamp);
                                    } catch (Exception $e) {
                                        $updateTime = null;
                                    }
                                @endphp
                                <tr>
                                    <td>
                                        @if($updateTime)
                                            <strong>{{ $updateTime->format('Y-m-d H:i:s') }}</strong><br>
                                            <small class="text-muted">({{ $updateTime->diffForHumans() }})</small>
                                        @else
                                            <span class="text-muted">Invalid timestamp</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-danger">
                                            {{ $metrics['likes'] ?? 0 }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-primary">
                                            {{ $metrics['comments'] ?? 0 }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-info">
                                            {{ $metrics['views'] ?? 0 }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-success">
                                            {{ $metrics['reach'] ?? 0 }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-warning">
                                            {{ $metrics['shares'] ?? 0 }}
                                        </span>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-secondary"
                                                data-toggle="modal"
                                                data-target="#insightModal{{ $loop->index }}"
                                                title="View Raw Data">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Current Latest Values -->
                <div class="mt-4">
                    <h5><i class="fas fa-star"></i> Current Latest Values</h5>
                    <div class="row">
                        <div class="col-md-2">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h4>{{ $socialPost->insights['likes'] ?? 0 }}</h4>
                                    <small>Likes</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4>{{ $socialPost->insights['comments'] ?? 0 }}</h4>
                                    <small>Comments</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4>{{ $socialPost->insights['views'] ?? 0 }}</h4>
                                    <small>Views</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4>{{ $socialPost->insights['reach'] ?? 0 }}</h4>
                                    <small>Reach</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4>{{ $socialPost->insights['shares'] ?? 0 }}</h4>
                                    <small>Shares</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center">
                                    <h4>{{ $socialPost->insights['total_interactions'] ?? 0 }}</h4>
                                    <small>Interactions</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Raw Data Modals -->
        @foreach($historicalInsights as $timestamp => $data)
        <div class="modal fade" id="insightModal{{ $loop->index }}" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            Raw Insights Data - {{ \Carbon\Carbon::createFromFormat('Y_m_d_H_i_s', $timestamp)->format('Y-m-d H:i:s') }}
                        </h5>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <pre class="bg-light p-3" style="max-height: 400px; overflow-y: auto;">{{ json_encode($data, JSON_PRETTY_PRINT) }}</pre>
                    </div>
                </div>
            </div>
        </div>
        @endforeach
        @else
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-chart-line"></i> Historical Insights Data
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    No historical insights data available. Insights will be collected when the post is processed by our Instagram API integration.
                </div>
            </div>
        </div>
        @endif

        <!-- Current Insights Summary (if no historical data) -->
        @if(empty($historicalInsights) && !empty($socialPost->insights))
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-star"></i> Current Insights Summary
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    @if(isset($socialPost->insights['likes']))
                        <div class="col-md-2">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h4>{{ $socialPost->insights['likes'] }}</h4>
                                    <small>Likes</small>
                                </div>
                            </div>
                        </div>
                    @endif
                    @if(isset($socialPost->insights['comments']))
                        <div class="col-md-2">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4>{{ $socialPost->insights['comments'] }}</h4>
                                    <small>Comments</small>
                                </div>
                            </div>
                        </div>
                    @endif
                    @if(isset($socialPost->insights['views']))
                        <div class="col-md-2">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4>{{ $socialPost->insights['views'] }}</h4>
                                    <small>Views</small>
                                </div>
                            </div>
                        </div>
                    @endif
                    @if(isset($socialPost->insights['reach']))
                        <div class="col-md-2">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4>{{ $socialPost->insights['reach'] }}</h4>
                                    <small>Reach</small>
                                </div>
                            </div>
                        </div>
                    @endif
                    @if(isset($socialPost->insights['shares']))
                        <div class="col-md-2">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4>{{ $socialPost->insights['shares'] }}</h4>
                                    <small>Shares</small>
                                </div>
                            </div>
                        </div>
                    @endif
                    @if(isset($socialPost->insights['total_interactions']))
                        <div class="col-md-2">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center">
                                    <h4>{{ $socialPost->insights['total_interactions'] }}</h4>
                                    <small>Interactions</small>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        @endif

        <!-- Raw Insights JSON (for debugging) -->
        @if($socialPost->insights)
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Raw Insights Data</h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body" style="display: none;">
                <pre class="bg-light p-3" style="max-height: 400px; overflow-y: auto;">{{ json_encode($socialPost->insights, JSON_PRETTY_PRINT) }}</pre>
            </div>
        </div>
        @endif

    </section>
    <!-- /.content -->
@endsection

@section('admin_script_links')
@endsection

@section('admin_script_codes')
<style>
    /* Custom styling for social post details */
    .border-left-primary {
        border-left: 4px solid #007bff !important;
    }

    .card-body h6.text-primary {
        color: #007bff !important;
        font-weight: 600;
    }

    .card-body h4.text-dark {
        color: #495057 !important;
        font-weight: 700;
        margin: 0.5rem 0;
    }

    .badge {
        font-size: 0.75em;
        padding: 0.375rem 0.5rem;
    }

    /* Media content styling */
    .img-fluid {
        max-width: 100%;
        height: auto;
    }

    video.img-fluid {
        max-width: 100%;
        height: auto;
    }
</style>

<script>
    $(document).ready(function() {
        // Any additional JavaScript can go here
    });
</script>
@endsection
