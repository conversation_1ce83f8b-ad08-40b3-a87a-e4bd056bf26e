@extends('admin.layouts.master_admin')

@section('page_last_name')
{{config('app.name')}} | Campaign request time
@endsection

@section('content')
    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Manage Campaigns</h1>
                </div>
            </div>
        </div><!-- /.container-fluid -->
    </section>
    
    <!--Begin Content-->
    <section class="content">
        <div class="card">
            <div class="card-header">
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse" data-toggle="tooltip" title="Collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <form action="" method="post" id="add_business_term_form" enctype="multipart/form-data" data-parsley-validate>
                    @csrf  
                    <div class="filtertop d-flex">
                        <div class="form-group checkwidth">
                            <input type="text" placeholder="Campaign ID" class="form-control" name="compaign_id" id="compaign_id">
                        </div>
                    </div>
                    <input type="submit" class="btn btn-info" value="Search" style="color: white;">
                    <div class="connectPrising inner"> 
                        <div class="accordion compaign_id_table" id="accordionExample">
                            @include('admin.campaigns.manage_page')
                        </div>
                    </div>
                </form> 
            </div> 
            <div class="card-footer">
                {{--Footer--}}
            </div>
        </div>
    </section>
@endsection
@section('admin_script_codes')
    <script>  
        $('#compaign_id').change(function() { 
            var compaign_id = $("#compaign_id").val();
            $.ajax({
                url: "{{ url('admin/get-campaign_list') }}",
                data: {
                    compaign_id: compaign_id 
                }
            })
            .done(function(data) { 
                $(".compaign_id_table").html(data.generalPage);
            });
        });
    </script>
@endsection
    
