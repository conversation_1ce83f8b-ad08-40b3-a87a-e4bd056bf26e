@extends('admin.layouts.master_admin')

@section('page_last_name')
{{config('app.name')}} | Campaign request time
@endsection

@section('content')
    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Manage Campaigns</h1>
                </div>
            </div>
        </div><!-- /.container-fluid -->
    </section>
    
    <!--Begin Content-->
    <section class="content">

        <!-- Default box -->
        <div class="card">
            <div class="card-header">

                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse" data-toggle="tooltip"
                            title="Collapse">
                        <i class="fas fa-minus"></i></button>
                    <!-- <button type="button" class="btn btn-tool" data-card-widget="remove" data-toggle="tooltip"
                            title="Remove">
                        <i class="fas fa-times"></i></button> -->
                </div>
            </div>
            <div class="card-body">
                <div class="filtertop d-flex">
                    <div class="form-group checkwidth">
                        <label>Campaign ID:</label>
                        <input type="text" placeholder="Campaign ID" class="form-control" name="compaign_id" id="compaign_id">
                    </div>
                    <!-- <div class="form-group checkwidth">
                        <label>Brand ID:</label>
                        <input type="text" placeholder="Brand ID" class="form-control">
                    </div>
                    <div class="form-group checkwidth">
                        <label>Order ID:</label>
                        <input type="text" placeholder="Order ID" class="form-control">
                    </div> -->
                </div>
                <form action="" method="post" id="add_business_term_form" enctype="multipart/form-data" data-parsley-validate>
                @csrf  
                    <input type="submit" class="btn btn-danger" value="Update">
                   <!--  <table class="table" id="example1">
                        <thead>
                        <tr>
                            <th class="column-title">Created At </th>
                            <th class="column-title">Campaign ID</th>
                            <th class="column-title">Influencer</th>
                            <th class="column-title">Brand</th>
                            <th class="column-title">Status </th> 
                        </tr>
                        </thead>
                        <tbody>
                        <?php $i = 1;  ?>
                        @foreach($result as $row)
                            <tr> 
                                <td>{{ @$row->created_at }}</td>
                                <td ><span target="popup" data-toggle="modal" data-target="#requestForm{{$row->id}}" >{{ @$row->compaign_id }}</span>

                                <?php 
                                $newLivetreamPrice = 0; 
                                $fieldName = $row->advertising.'_price';
                                $user = App\Models\User::where('id',Auth::id())->first();
                                // print_r($user->advertisingMethodPrice);
                                if($user->advertisingMethodPrice != null)
                                {    
                                    $newLivetreamPrice = $user->advertisingMethodPrice->$fieldName; 
                                }    
                                ?> 
                           


                                </td>
                                <td> <a href="{{URL::to('/admin/edit-influencer',['id'=>@$row->influencerdetails->user->id])}}" title="Edit">{{ @$row->influencerdetails->user->first_name }} {{ @$row->influencerdetails->user->last_name }}</a></td>
                                <td> <a href="{{URL::to('/admin/edit-customer',['id'=>@$row->user->id])}}" title="Edit"> {{ @$row->user->first_name }} {{ @$row->user->last_name }}</a></td>
                                <td > 
                                    @if($row->finish == '1')
                                        Finished
                                    @elseif(@$row->status == 'Cancelled')
                                        Cancelled
                                    @elseif(@$row->status == '2')
                                        Payment Submitted
                                    @else
                                    <input type="hidden" name="ids[]" value="{{ @$row->id }}">
                                    <select name="finish[]">
                                        <option value="" >Select</option> 
                                        <option {{ @$row->status == 'Cancelled'?'selected':''}}  >Cancelled</option>
                                    </select>
                                    @endif
                                </td> 
                            </tr>

                          
                            <?php $i++; ?>
                        @endforeach 
                        </tbody>
                    </table> -->

                <div class="connectPrising inner"> 
                    <div class="accordion compaign_id_table" id="accordionExample">
                       
                        @include('admin.campaigns.manage_page')
                    </div>
                </div>  
            </div>  
            </form> 
        </div> 
            <!-- /.card-body -->
            <div class="card-footer">
                {{--Footer--}}
            </div>
            <!-- /.card-footer-->
        </div>
        <!-- /.card --> 
    </section>
    <!-- /.content -->
@endsection
@section('admin_script_codes')
 
    <script>  
        $('#compaign_id').change(function() { 
            var compaign_id = $("#compaign_id").val();
            $.ajax({
                url: "{{ url('admin/get-campaign_list') }}",
                data: {
                    compaign_id: compaign_id 
                }
            })
            .done(function(data) { 
                $(".compaign_id_table").html(data.generalPage);
            });
        });
    </script>
@endsection
    
