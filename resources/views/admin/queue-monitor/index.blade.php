@extends('admin.layouts.master_admin')

@section('title', 'Queue Monitor - All Jobs')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Queue Monitor - All Jobs</h1>
        <div>
            <a href="{{ route('admin.queue-monitor.dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-chart-line"></i> Dashboard
            </a>
            <button type="button" class="btn btn-warning" onclick="clearCompleted()">
                <i class="fas fa-trash"></i> Clear Old Jobs
            </button>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filters</h6>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-2">
                    <label class="form-label">Status</label>
                    <select name="status" class="form-control">
                        <option value="">All Statuses</option>
                        <option value="0" {{ request('status') === '0' ? 'selected' : '' }}>Running</option>
                        <option value="1" {{ request('status') === '1' ? 'selected' : '' }}>Succeeded</option>
                        <option value="2" {{ request('status') === '2' ? 'selected' : '' }}>Failed</option>
                        <option value="4" {{ request('status') === '4' ? 'selected' : '' }}>Queued</option>
                        <option value="3" {{ request('status') === '3' ? 'selected' : '' }}>Stale</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Job Type</label>
                    <input type="text" name="job" class="form-control" placeholder="Job Class" value="{{ request('job') }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">From Date</label>
                    <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">To Date</label>
                    <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">Filter</button>
                    <a href="{{ route('admin.queue-monitor.index') }}" class="btn btn-outline-secondary">Clear</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Statistics Summary -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white shadow">
                <div class="card-body">
                    <div class="text-white-50 small">Total Jobs</div>
                    <div class="text-lg font-weight-bold">{{ number_format($stats['total']) }}</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white shadow">
                <div class="card-body">
                    <div class="text-white-50 small">Succeeded</div>
                    <div class="text-lg font-weight-bold">{{ number_format($stats['succeeded']) }}</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white shadow">
                <div class="card-body">
                    <div class="text-white-50 small">Failed</div>
                    <div class="text-lg font-weight-bold">{{ number_format($stats['failed']) }}</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white shadow">
                <div class="card-body">
                    <div class="text-white-50 small">Running</div>
                    <div class="text-lg font-weight-bold">{{ number_format($stats['running']) }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Jobs Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Jobs ({{ $monitors->total() }} total)</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Job</th>
                            <th>Status</th>
                            <th>Started</th>
                            <th>Duration</th>
                            <th>Memory</th>
                            <th>Attempts</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($monitors as $monitor)
                        <tr>
                            <td>{{ $monitor->id }}</td>
                            <td>
                                <span class="badge badge-secondary">{{ $monitor->name ?? 'Unknown Job' }}</span>
                            </td>
                            <td>
                                @if($monitor->status === 1)
                                    <span class="badge badge-success">Success</span>
                                @elseif($monitor->status === 2)
                                    <span class="badge badge-danger">Failed</span>
                                @elseif($monitor->status === 0)
                                    <span class="badge badge-primary">
                                        <i class="fas fa-spinner fa-spin"></i> Running
                                    </span>
                                @elseif($monitor->status === 4)
                                    <span class="badge badge-info">Queued</span>
                                @elseif($monitor->status === 3)
                                    <span class="badge badge-warning">Stale</span>
                                @else
                                    <span class="badge badge-secondary">Unknown ({{ $monitor->status }})</span>
                                @endif
                            </td>
                            <td>
                                @if($monitor->started_at)
                                    {{ $monitor->started_at->format('M j, H:i:s') }}
                                    <br><small class="text-muted">{{ $monitor->started_at->diffForHumans() }}</small>
                                @else
                                    -
                                @endif
                            </td>
                            <td>
                                @if($monitor->finished_at && $monitor->started_at)
                                    {{ round($monitor->started_at->diffInSeconds($monitor->finished_at), 2) }}s
                                @elseif($monitor->status === 0 && $monitor->started_at)
                                    <span class="text-info">{{ round($monitor->started_at->diffInSeconds(now()), 2) }}s</span>
                                @else
                                    -
                                @endif
                            </td>
                            <td>
                                @if($monitor->memory_usage)
                                    {{ round($monitor->memory_usage / 1024 / 1024, 2) }} MB
                                @else
                                    -
                                @endif
                            </td>
                            <td>{{ $monitor->attempt ?? 1 }}</td>
                            <td>
                                <a href="{{ route('admin.queue-monitor.show', $monitor) }}" class="btn btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>&nbsp;
                                @if($monitor->status === 2)
                                    <form method="POST" action="{{ route('admin.queue-monitor.retry', $monitor) }}" class="d-inline">
                                        @csrf
                                        <button type="submit" class="btn btn-outline-warning" title="Retry">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                    </form>&nbsp;
                                @endif
                                <form method="POST" action="{{ route('admin.queue-monitor.delete', $monitor) }}" class="d-inline" onsubmit="return confirm('Are you sure?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-outline-danger" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="text-center">No jobs found</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center">
                {{ $monitors->appends(request()->query())->links() }}
            </div>
        </div>
    </div>
</div>

<script>
function clearCompleted() {
    if (!confirm('This will delete all completed jobs older than 7 days. Are you sure?')) {
        return;
    }

    fetch('{{ route("admin.queue-monitor.clear-completed") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while clearing completed jobs.');
    });
}

// Auto-refresh every 10 seconds for running jobs
if (document.querySelector('.badge-primary')) {
    setInterval(() => {
        location.reload();
    }, 10000);
}
</script>
@endsection
