@extends('admin.layouts.master_admin')

@section('title', 'Queue Monitor Dashboard')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Queue Monitor Dashboard</h1>
        <div>
            <button type="button" class="btn btn-primary" onclick="startRepricing()">
                <i class="fas fa-play"></i> Start Repricing
            </button>
            <a href="{{ route('admin.queue-monitor.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-list"></i> View All Jobs
            </a>
        </div>
    </div>

    @if(!$hasMonitorData)
    <!-- Queue Monitor Setup Notice -->
    <div class="alert alert-success" role="alert">
        <h4 class="alert-heading"><i class="fas fa-check-circle"></i> Queue Monitor Ready</h4>
        <p>The queue monitor is properly configured and ready to track jobs!</p>
        <p class="mb-0">Start a repricing job using the button above to see it in action.</p>
    </div>
    @endif

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Jobs</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['total']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tasks fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Success Rate</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['success_rate'] }}%</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Active Jobs</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['running'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-spinner fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Failed Jobs</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['failed'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity Chart -->
    <div class="row mb-4">
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Job Activity (Last 7 Days)</h6>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="jobChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Performance (24h)</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 mb-3">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">Jobs Processed</div>
                            <div class="h5 mb-0">{{ number_format($stats['recent_total']) }}</div>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">Success Rate</div>
                            <div class="h5 mb-0 text-success">{{ $stats['recent_success_rate'] }}%</div>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">Failed Jobs</div>
                            <div class="h5 mb-0 text-danger">{{ number_format($stats['recent_failed']) }}</div>
                        </div>
                        <div class="col-12">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">Avg Execution Time</div>
                            <div class="h5 mb-0">{{ $stats['avg_execution_time'] }}s</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Jobs -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Recent Jobs</h6>
            <div class="ml-auto">
                <a href="{{ route('admin.queue-monitor.index') }}" class="btn btn-primary">View All</a>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Job</th>
                            <th>Status</th>
                            <th>Started</th>
                            <th>Duration</th>
                            <th>Memory</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($recentJobs as $job)
                        <tr>
                            <td>
                                <span class="badge badge-secondary">{{ $job->name ?? 'Unknown Job' }}</span>

                            </td>
                            <td>
                                @if($job->status === 1)
                                    <span class="badge badge-success">Success</span>
                                @elseif($job->status === 2)
                                    <span class="badge badge-danger">Failed</span>
                                @elseif($job->status === 0)
                                    <span class="badge badge-primary">Running</span>
                                @elseif($job->status === 4)
                                    <span class="badge badge-info">Queued</span>
                                @elseif($job->status === 3)
                                    <span class="badge badge-warning">Stale</span>
                                @else
                                    <span class="badge badge-secondary">Unknown ({{ $job->status }})</span>
                                @endif
                            </td>
                            <td>
                                @if($job->started_at)
                                    {{ $job->started_at->format('M j, H:i') }}
                                    <br><small class="text-muted">{{ $job->started_at->diffForHumans() }}</small>
                                @else
                                    -
                                @endif
                            </td>
                            <td>
                                @if($job->finished_at && $job->started_at)
                                    {{ round($job->started_at->diffInSeconds($job->finished_at), 2) }}s
                                @else
                                    -
                                @endif
                            </td>
                            <td>
                                @if($job->memory_usage)
                                    {{ round($job->memory_usage / 1024 / 1024, 2) }} MB
                                @else
                                    -
                                @endif
                            </td>
                            <td>
                                <a href="{{ route('admin.queue-monitor.show', $job) }}" class="btn btn-outline-primary">View</a>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="text-center">No jobs found</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Job Activity Chart
const ctx = document.getElementById('jobChart').getContext('2d');
const jobChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: @json($chartData['labels']),
        datasets: [{
            label: 'Total Jobs',
            data: @json($chartData['total']),
            borderColor: 'rgb(78, 115, 223)',
            backgroundColor: 'rgba(78, 115, 223, 0.1)',
            tension: 0.3
        }, {
            label: 'Succeeded',
            data: @json($chartData['succeeded']),
            borderColor: 'rgb(28, 200, 138)',
            backgroundColor: 'rgba(28, 200, 138, 0.1)',
            tension: 0.3
        }, {
            label: 'Failed',
            data: @json($chartData['failed']),
            borderColor: 'rgb(231, 74, 59)',
            backgroundColor: 'rgba(231, 74, 59, 0.1)',
            tension: 0.3
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            },
            title: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Start Repricing Function
function startRepricing() {
    if (!confirm('Are you sure you want to start repricing all influencers? This may take a while.')) {
        return;
    }

    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Starting...';
    button.disabled = true;

    fetch('{{ route("admin.queue-monitor.start-repricing") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            // Refresh the page after 2 seconds to show the new job
            setTimeout(() => location.reload(), 2000);
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while starting the repricing job.');
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// Auto-refresh every 30 seconds
setInterval(() => {
    location.reload();
}, 30000);
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
</style>
@endsection
