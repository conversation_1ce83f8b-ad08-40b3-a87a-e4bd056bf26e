@extends('admin.layouts.master_admin')

@section('title', 'Job Details #' . $monitor->id)

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Job Details #{{ $monitor->id }}</h1>
        <div>
            <a href="{{ route('admin.queue-monitor.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
            @if($monitor->status === 2)
                <form method="POST" action="{{ route('admin.queue-monitor.retry', $monitor) }}" class="d-inline">
                    @csrf
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-redo"></i> Retry Job
                    </button>
                </form>
            @endif
        </div>
    </div>

    <!-- Job Status Alert -->
    @if($monitor->status === 1)
        <div class="alert alert-success" role="alert">
            <i class="fas fa-check-circle"></i> This job completed successfully.
        </div>
    @elseif($monitor->status === 2)
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle"></i> This job failed to complete.
        </div>
    @elseif($monitor->status === 0)
        <div class="alert alert-info" role="alert">
            <i class="fas fa-spinner fa-spin"></i> This job is currently running.
        </div>
    @elseif($monitor->status === 4)
        <div class="alert alert-info" role="alert">
            <i class="fas fa-clock"></i> This job is queued and waiting to be processed.
        </div>
    @elseif($monitor->status === 3)
        <div class="alert alert-warning" role="alert">
            <i class="fas fa-exclamation-triangle"></i> This job appears to be stale.
        </div>
    @endif

    <div class="row">
        <!-- Job Information -->
        <div class="col-md-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Job Information</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th width="40%">Job Name:</th>
                            <td>
                                <code>{{ $monitor->name ?? 'Unknown Job' }}</code>
                            </td>
                        </tr>
                        <tr>
                            <th>Status:</th>
                            <td>
                                @if($monitor->status === 1)
                                    <span class="badge badge-success badge-lg">Success</span>
                                @elseif($monitor->status === 2)
                                    <span class="badge badge-danger badge-lg">Failed</span>
                                @elseif($monitor->status === 0)
                                    <span class="badge badge-primary badge-lg">
                                        <i class="fas fa-spinner fa-spin"></i> Running
                                    </span>
                                @elseif($monitor->status === 4)
                                    <span class="badge badge-info badge-lg">Queued</span>
                                @elseif($monitor->status === 3)
                                    <span class="badge badge-warning badge-lg">Stale</span>
                                @else
                                    <span class="badge badge-secondary badge-lg">Unknown ({{ $monitor->status }})</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>Queue:</th>
                            <td>{{ $monitor->queue ?? 'default' }}</td>
                        </tr>
                        <tr>
                            <th>Attempts:</th>
                            <td>{{ $monitor->attempt ?? 1 }}</td>
                        </tr>
                        @if($monitor->name)
                        <tr>
                            <th>Job Name:</th>
                            <td>{{ $monitor->name }}</td>
                        </tr>
                        @endif
                    </table>
                </div>
            </div>
        </div>

        <!-- Timing Information -->
        <div class="col-md-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Timing & Performance</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th width="40%">Started At:</th>
                            <td>
                                @if($monitor->started_at)
                                    {{ $monitor->started_at->format('Y-m-d H:i:s') }}
                                    <br><small class="text-muted">{{ $monitor->started_at->diffForHumans() }}</small>
                                @else
                                    <span class="text-muted">Not started</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>Finished At:</th>
                            <td>
                                @if($monitor->finished_at)
                                    {{ $monitor->finished_at->format('Y-m-d H:i:s') }}
                                    <br><small class="text-muted">{{ $monitor->finished_at->diffForHumans() }}</small>
                                @else
                                    <span class="text-muted">{{ $monitor->status === 'running' ? 'Still running' : 'Not finished' }}</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>Duration:</th>
                            <td>
                                @if($monitor->finished_at && $monitor->started_at)
                                    <strong>{{ round($monitor->started_at->diffInSeconds($monitor->finished_at), 2) }} seconds</strong>
                                @elseif($monitor->status === 0 && $monitor->started_at)
                                    <span class="text-info">
                                        <strong>{{ round($monitor->started_at->diffInSeconds(now()), 2) }} seconds</strong>
                                        <small>(still running)</small>
                                    </span>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>Memory Usage:</th>
                            <td>
                                @if($monitor->memory_usage)
                                    <strong>{{ round($monitor->memory_usage / 1024 / 1024, 2) }} MB</strong>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Details (if failed) -->
    @if($monitor->status === 'failed' && ($monitor->exception_message || $monitor->exception_trace))
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow border-left-danger">
                <div class="card-header py-3 bg-danger text-white">
                    <h6 class="m-0 font-weight-bold">Error Details</h6>
                </div>
                <div class="card-body">
                    @if($monitor->exception_message)
                    <div class="mb-3">
                        <h6>Exception Message:</h6>
                        <div class="alert alert-danger">
                            <code>{{ $monitor->exception_message }}</code>
                        </div>
                    </div>
                    @endif

                    @if($monitor->exception_trace)
                    <div class="mb-3">
                        <h6>Stack Trace:</h6>
                        <details>
                            <summary class="btn btn-outline-secondary btn-sm">Show Stack Trace</summary>
                            <pre class="mt-3 p-3 bg-light border rounded" style="max-height: 400px; overflow-y: auto;"><code>{{ $monitor->exception_trace }}</code></pre>
                        </details>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Job Data -->
    @if($monitor->data)
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Job Data</h6>
                </div>
                <div class="card-body">
                    <pre class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;"><code>{{ json_encode($monitor->data, JSON_PRETTY_PRINT) }}</code></pre>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Progress Information (for RepriceAllJob) -->
    @if(str_contains($monitor->name, 'RepriceAllJob'))
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Repricing Job Information</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>About this job:</strong> This job reprices all influencers for all campaign types (Boost Me, Survey, Reaction Video). 
                        It processes influencers in chunks of 500 and includes reach-based pricing calculations.
                    </div>
                    
                    @if($monitor->status === 0)
                    <div class="alert alert-warning">
                        <i class="fas fa-clock"></i>
                        <strong>Currently Running:</strong> This job may take several minutes to hours depending on the number of influencers.
                        Check the logs for detailed progress information.
                    </div>
                    @endif

                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                <div class="font-weight-bold">Processes All Influencers</div>
                                <small class="text-muted">In chunks of 500</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="fas fa-tags fa-2x text-success mb-2"></i>
                                <div class="font-weight-bold">3 Campaign Types</div>
                                <small class="text-muted">Boost Me, Survey, Reaction Video</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="fas fa-chart-line fa-2x text-info mb-2"></i>
                                <div class="font-weight-bold">Reach-Based Pricing</div>
                                <small class="text-muted">Uses hardcoded pricing data</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

<script>
// Auto-refresh if job is running
@if($monitor->status === 0)
setInterval(() => {
    location.reload();
}, 5000); // Refresh every 5 seconds
@endif
</script>

<style>
.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}
.badge-lg {
    font-size: 0.9em;
    padding: 0.5em 0.75em;
}
</style>
@endsection
