@extends('admin.layouts.master_admin')

@section('page_last_name')
{{config('app.name')}} | Pricing
@endsection

@section('content')
    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Add Pricing</h1>
                </div>
            </div>
        </div><!-- /.container-fluid -->
    </section>
    
    <!--Begin Content-->
    <section class="content">
        
        <form action="" method="post" id="add_business_term_form" enctype="multipart/form-data" data-parsley-validate>
            @csrf
            <fieldset>

                <div class="am-selected-media">
                    <span id="error-select-media"></span>
                    <ul class="media-box"> 
        
                        <li class="">
                            <input type="radio" name="media" value="Instagram" required  >
                            <img src="{{ asset('/') }}assets/front-end/images/icons/new_social_media_instagram.png" class=" icon" alt="">
                        </li>
                        <li class="">
                            <input type="radio" name="media" value="Facebook" required >
                            <img src="{{ asset('/') }}assets/front-end/images/icons/new_social_media_facebook.png" class=" icon" alt="">
                        </li>
                        <li class="">
                            <input type="radio" name="media" value="Tiktok" required >
                            <img src="{{ asset('/') }}assets/front-end/images/icons/new_social_media_tiktok.png" class=" icon" alt="">
                        </li>
                        <li class="">
                            <input type="radio" name="media" value="Youtube" required >
                            <img src="{{ asset('/') }}assets/front-end/images/icons/new_social_media_youtube.png" class=" icon" alt="">
                        </li>
                        <li class="">
                            <input type="radio" name="media" value="Twitter" required >
                            <img src="{{ asset('/') }}assets/front-end/images/icons/new_social_media_twitter.png" class=" icon" alt="">
                        </li>
                    </ul>
                </div>


                <div class="form-group border-box d-flex">
                    <div class="one-line ">
                        <div class="custom-checkbox">
                            <input type="radio" name="type" value="Boost me" id="boost-me" required >
                            <label for="boost-me">Boost me</label>
                        </div>
                        <div class="custom-checkbox">
                            <input type="radio" name="type" value="Reaction-Video" id="reaction-video" required >
                            <label for="reaction-video">Reaction-Video</label>
                        </div>
                        <div class="custom-checkbox">
                            <input type="radio" name="type" value="Survey" id="survey" required >
                            <label for="survey">Survey</label>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="cntr-width" for="exampleInputEmail4">Country </label>
                    <label class="cntr-fr" for="exampleInputEmail4">Follower-Range</label>
                    <label class="cntr-cpt" for="exampleInputEmail4">CPT</label>
                    <label class="cntr-reach" for="exampleInputEmail4">Estimated Reach (%)</label>
                </div>

                <div class="task-list-admin">
                    <div class="take-list d-flex block optionBox">
                        <input type="text" name="country[]" class="country" value="Standard" readonly>

                        <select name="range[]" class="range" required>
                            <option  >All</option>
                            <option  >5</option>
                            <option  >500</option>
                            <option  >1000</option>
                            <option  >10000</option>
                            <option  >50000</option>
                            <option  >100000</option>
                            <option  >500000</option>
                            <option  >1000000+</option>
                        </select>
                        <input type="number" class="add-price form-control" name="cpt[]" placeholder="Enter pricing" required="" >
                        <input type="number" class="add-reach form-control" name="estimated_reach[]" placeholder="Enter estimated reach %" step="0.01" min="0" max="100" >
                    </div>
                </div>
                <div class="d-flex">
                    <button type="button" class="add_counntry working-icon mr-2"><img src="{{ asset('/') }}assets/front-end/images/icons/admin-add-icon.png" alt=""></button> 
                        
                    <input type="submit" class="btn btn-danger" value="Save">
                </div>

            </fieldset>
        </form>

    </section>
    <!-- /.content -->
@endsection
@section('admin_script_codes')
 
 <script type="text/javascript">
$('.add_counntry').click(function() {
    $('.block:last').after('<div class="take-list d-flex"> \
                            <select  class="country" name="country[]"     >\
                              <option value="">Select Country</option>\
                              @foreach($countries as $country)\
                              <option value="{{$country->id}}">{{$country->name}}</option>\
                              @endforeach \
                            </select>\
                            <select name="range[]" required class="range">\
                                <option>All</option>\
                                <option  >5</option>\
                                <option  >500</option>\
                                <option  >1000</option>\
                                <option  >10000</option>\
                                <option  >50000</option>\
                                <option  >100000</option>\
                                <option  >500000</option>\
                                <option  >1000000+</option>\
                            </select>\
                            <input type="number" class="add-price  form-control" name="cpt[]" placeholder="Enter pricing" required="" >\
                            <input type="number" class="add-reach form-control" name="estimated_reach[]" placeholder="Enter estimated reach %" step="0.01" min="0" max="100" >\
                        <button type="button" class="remove working-icon"><img src="{{ asset('/') }}assets/front-end/images/icons/admin-remove-icon.png" alt=""></button></div>');
});
$(document).on('click', '.remove', function() {
    $(this).closest('.take-list').remove();
 });


$(document).on('change', '.range', function() {
    console.log($(this).closest(".form-group").find('.country').val());

});
 </script>
@endsection
    
