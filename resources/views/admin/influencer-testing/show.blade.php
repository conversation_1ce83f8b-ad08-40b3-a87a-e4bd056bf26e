@extends('admin.layouts.master_admin')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2>
                        <i class="fas fa-flask mr-2"></i>
                        Pricing Test:
                        @if($influencer->user)
                            {{ $influencer->user->first_name }} {{ $influencer->user->last_name }}
                        @else
                            Influencer #{{ $influencer->id }}
                        @endif
                    </h2>
                    <p class="text-muted">Test and validate the pricing algorithm with real and fake data</p>
                </div>
                <a href="/admin/influencer-testing" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-1"></i> Back to List
                </a>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle mr-2"></i>
            {{ session('success') }}
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle mr-2"></i>
            {{ session('error') }}
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        </div>
    @endif

    <div class="row">
        <!-- Left Column: Influencer Data & Controls -->
        <div class="col-md-6">
            <!-- Influencer Basic Data -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-user mr-2"></i>Influencer Data</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="/admin/influencer-testing/{{ $influencer->id }}/update">
                        @csrf
                        @method('PUT')
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Followers</label>
                                    <input type="number" name="followers" class="form-control" 
                                           value="{{ $influencer->followers ?? 0 }}" min="0" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Gamification %</label>
                                    <div class="input-group">
                                        <input type="number" name="gamification_percentage" class="form-control" 
                                               value="{{ ($influencer->gamification_percentage ?? 0) * 100 }}" 
                                               min="0" max="100" step="0.1" required>
                                        <div class="input-group-append">
                                            <span class="input-group-text">%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-1"></i> Update Data
                        </button>
                    </form>
                </div>
            </div>

            <!-- Test Scenarios -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-magic mr-2"></i>Quick Test Scenarios</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Generate predefined test scenarios with fake data:</p>
                    <form method="POST" action="/admin/influencer-testing/{{ $influencer->id }}/test-scenario">
                        @csrf
                        <div class="row">
                            <div class="col-md-8">
                                <select name="scenario" class="form-control" required>
                                    <option value="">Select a test scenario...</option>
                                    <option value="high_performer">🚀 High Performer (150% of estimated reach)</option>
                                    <option value="low_performer">📉 Low Performer (50% of estimated reach)</option>
                                    <option value="mixed_performance">📊 Mixed Performance (alternating high/low)</option>
                                    <option value="insufficient_data">❌ Insufficient Data (< minimum posts)</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-warning btn-block">
                                    <i class="fas fa-magic mr-1"></i> Create Scenario
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Current Reach Data -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-chart-line mr-2"></i>Current Reach Data</h5>
                    <div class="ml-auto">
                        <form method="POST" action="/admin/influencer-testing/{{ $influencer->id }}/recalculate-reach" class="d-inline" id="recalculate-form">
                            @csrf
                            <button type="submit" class="btn btn-sm btn-info" style="color: white;" id="recalculate-btn">
                                <i class="fas fa-sync mr-1"></i> Recalculate Avg. Reach
                            </button>
                        </form>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <h6>Stories</h6>
                            @if($influencer->ig_story_avg_reach)
                                <span class="badge badge-success badge-lg">{{ number_format($influencer->ig_story_avg_reach) }}</span>
                            @else
                                <span class="badge badge-secondary">No Data</span>
                            @endif
                            @if($influencer->flag_story_insight_missing)
                                <br><small class="text-warning">⚠️ Insufficient Data</small>
                            @endif
                            <br><small class="text-muted">{{ $postCounts['story'] }} posts</small>
                        </div>
                        <div class="col-md-4 text-center">
                            <h6>Reels</h6>
                            @if($influencer->ig_reel_avg_reach)
                                <span class="badge badge-success badge-lg">{{ number_format($influencer->ig_reel_avg_reach) }}</span>
                            @else
                                <span class="badge badge-secondary">No Data</span>
                            @endif
                            @if($influencer->flag_reel_insight_missing)
                                <br><small class="text-warning">⚠️ Insufficient Data</small>
                            @endif
                            <br><small class="text-muted">{{ $postCounts['reel'] }} posts</small>
                        </div>
                        <div class="col-md-4 text-center">
                            <h6>Feed</h6>
                            @if($influencer->ig_feed_avg_reach)
                                <span class="badge badge-success badge-lg">{{ number_format($influencer->ig_feed_avg_reach) }}</span>
                            @else
                                <span class="badge badge-secondary">No Data</span>
                            @endif
                            @if($influencer->flag_feed_insight_missing)
                                <br><small class="text-warning">⚠️ Insufficient Data</small>
                            @endif
                            <br><small class="text-muted">{{ $postCounts['feed'] }} posts</small>
                        </div>
                    </div>
                    @if($influencer->avg_reach_computed_at)
                        <small class="text-muted">Last computed: {{ $influencer->avg_reach_computed_at->diffForHumans() }}</small>
                    @endif
                </div>
            </div>

            <!-- Generate Custom Posts -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-plus-circle mr-2"></i>Generate Custom Posts</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="/admin/influencer-testing/{{ $influencer->id }}/generate-posts">
                        @csrf
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Post Type</label>
                                    <select name="post_type" class="form-control" required>
                                        <option value="story">Stories</option>
                                        <option value="reel">Reels</option>
                                        <option value="post">Feed Posts</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Number of Posts</label>
                                    <input type="number" name="count" class="form-control" 
                                           value="10" min="1" max="100" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Min Reach</label>
                                    <input type="number" name="min_reach" class="form-control" 
                                           value="500" min="0" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Max Reach</label>
                                    <input type="number" name="max_reach" class="form-control" 
                                           value="2000" min="0" required>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Days Back (posts will be distributed over this period)</label>
                            <input type="number" name="days_back" class="form-control" 
                                   value="60" min="1" max="365" required>
                        </div>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-plus mr-1"></i> Generate Posts
                        </button>
                    </form>
                </div>
            </div>

            <!-- Clear Data -->
            <div class="card mb-4">
                <div class="card-header bg-danger text-white">
                    <h5><i class="fas fa-trash mr-2"></i>Clear Test Data</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Remove all IG posts and reset reach data for this influencer.</p>
                    <form method="POST" action="/admin/influencer-testing/{{ $influencer->id }}/clear-posts"
                          onsubmit="return confirm('Are you sure you want to clear all test data?')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash mr-1"></i> Clear All Data
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Right Column: Pricing Results -->
        <div class="col-md-6">
            <!-- Current Pricing -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-dollar-sign mr-2"></i>Current Pricing</h5>
                </div>
                <div class="card-body">
                    @foreach(['Boost Me', 'Survey', 'Reaction Video'] as $campaignType)
                        <div class="campaign-pricing mb-4" data-campaign="{{ $campaignType }}">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">{{ $campaignType }}</h6>
                                <button class="btn btn-primary btn-sm calculate-price-btn" 
                                        data-campaign="{{ $campaignType }}">
                                    <i class="fas fa-calculator mr-1"></i> Calculate Price
                                </button>
                            </div>
                            
                            <div class="pricing-result" id="result-{{ str_replace(' ', '-', strtolower($campaignType)) }}">
                                @if(isset($currentPrices[$campaignType]['price']))
                                    <div class="pricing-result-card bg-success text-white p-3 rounded mb-3">
                                        <div class="row">
                                            <div class="col-md-8">
                                                <h4 class="mb-0">Price: €{{ number_format($currentPrices[$campaignType]['price'], 2) }}</h4>
                                                <small>Post Type: {{ ucfirst($currentPrices[$campaignType]['breakdown']['post_type']) }}</small>
                                                @if(isset($currentPrices[$campaignType]['stored']))
                                                    <br><small class="badge badge-{{ $currentPrices[$campaignType]['stored'] ? 'info' : 'warning' }}">
                                                        {{ $currentPrices[$campaignType]['stored'] ? 'Stored Price' : 'Calculated Price' }}
                                                    </small>
                                                    @if($currentPrices[$campaignType]['stored'] && isset($currentPrices[$campaignType]['priced_at']))
                                                        <br><small>Priced: {{ $currentPrices[$campaignType]['priced_at']->format('M j, Y g:i A') }}</small>
                                                    @endif
                                                    @if($currentPrices[$campaignType]['stored'] && isset($currentPrices[$campaignType]['record_id']))
                                                        <br><small class="text-muted">DB Record ID: {{ $currentPrices[$campaignType]['record_id'] }}</small>
                                                    @endif
                                                @endif
                                            </div>
                                            <div class="col-md-4 text-right">
                                                <span class="badge badge-dark">{{ strtoupper($currentPrices[$campaignType]['breakdown']['post_type']) }}</span>
                                            </div>
                                        </div>
                                    </div>
                                @elseif(isset($currentPrices[$campaignType]['error']))
                                    <div class="bg-danger text-white p-3 rounded mb-3">
                                        <strong>Error:</strong> {{ $currentPrices[$campaignType]['error'] }}
                                    </div>
                                @else
                                    <div class="bg-light p-3 rounded mb-3 text-muted">
                                        <i class="fas fa-info-circle mr-2"></i>Click "Calculate Price" to see pricing breakdown
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>

            <!-- Existing Posts Summary -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list mr-2"></i>Existing Posts Summary</h5>
                </div>
                <div class="card-body">
                    @if($igPosts->count() > 0)
                        @foreach(['story', 'reel', 'post'] as $postType)
                            @if($igPosts->has($postType))
                                <div class="mb-3">
                                    <h6>
                                        @if($postType === 'post')
                                            Feed Posts ({{ $igPosts[$postType]->count() }})
                                        @else
                                            {{ ucfirst($postType) }}s ({{ $igPosts[$postType]->count() }})
                                        @endif
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <small class="text-muted">Min Reach:</small><br>
                                            <strong>{{ number_format($igPosts[$postType]->min('reach')) }}</strong>
                                        </div>
                                        <div class="col-md-4">
                                            <small class="text-muted">Max Reach:</small><br>
                                            <strong>{{ number_format($igPosts[$postType]->max('reach')) }}</strong>
                                        </div>
                                        <div class="col-md-4">
                                            <small class="text-muted">Avg Reach:</small><br>
                                            <strong>{{ number_format($igPosts[$postType]->avg('reach')) }}</strong>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    @else
                        <p class="text-muted text-center">
                            <i class="fas fa-inbox fa-2x mb-2"></i><br>
                            No posts found. Generate some test data to get started.
                        </p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('admin_script_codes')
<script>
$(document).ready(function() {
    console.log('Influencer testing JavaScript loaded');
    console.log('Found calculate buttons:', $('.calculate-price-btn').length);

    // Recalculate button loading state
    $('#recalculate-form').on('submit', function() {
        const button = $('#recalculate-btn');
        button.prop('disabled', true);
        button.html('<i class="fas fa-spinner fa-spin mr-1"></i> Recalculating...');
    });

    // Calculate price button click handler
    $('.calculate-price-btn').click(function() {
        console.log('Calculate price button clicked');
        const campaignType = $(this).data('campaign');
        const resultDiv = $('#result-' + campaignType.toLowerCase().replace(' ', '-'));
        const button = $(this);

        // Show loading state
        button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i> Calculating...');
        resultDiv.html('<div class="alert alert-info"><i class="fas fa-spinner fa-spin mr-2"></i>Calculating price...</div>');

        // Make AJAX request
        $.ajax({
            url: '/admin/influencer-testing/{{ $influencer->id }}/calculate-price',
            method: 'POST',
            data: {
                campaign_type: campaignType,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    displayPricingResult(resultDiv, response);
                } else {
                    displayError(resultDiv, response.error || 'Unknown error occurred');
                }
            },
            error: function(xhr) {
                let errorMessage = 'Request failed';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMessage = xhr.responseJSON.error;
                }
                displayError(resultDiv, errorMessage);
            },
            complete: function() {
                // Reset button state
                button.prop('disabled', false).html('<i class="fas fa-calculator mr-1"></i> Calculate Price');
            }
        });
    });

    function displayPricingResult(resultDiv, response) {
        const breakdown = response.breakdown;
        const price = response.price;

        let html = `
            <div class="pricing-result-card bg-success text-white p-3 rounded mb-3">
                <div class="row">
                    <div class="col-md-8">
                        <h4 class="mb-0">Price: €${parseFloat(price).toFixed(2)}</h4>
                        <small>Post Type: ${breakdown.post_type.charAt(0).toUpperCase() + breakdown.post_type.slice(1)}</small>
                    </div>
                    <div class="col-md-4 text-right">
                        <span class="badge badge-dark">${breakdown.post_type.toUpperCase()}</span>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Pricing Breakdown</h6>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>Followers:</strong></td>
                            <td>${breakdown.followers.toLocaleString()}</td>
                        </tr>
                        <tr>
                            <td><strong>Tier CPT:</strong></td>
                            <td>€${breakdown.tier.cpt}</td>
                        </tr>
                        <tr>
                            <td><strong>Estimated Reach Rate:</strong></td>
                            <td>${(breakdown.tier.rate * 100).toFixed(1)}%</td>
                        </tr>
                        <tr>
                            <td><strong>Gamification Bonus:</strong></td>
                            <td>${(breakdown.gamification_percentage * 100).toFixed(1)}%</td>
                        </tr>
                        <tr>
                            <td><strong>Estimated Reach:</strong></td>
                            <td>${breakdown.estimated_reach.toLocaleString()}</td>
                        </tr>
                        <tr>
                            <td><strong>Actual Avg Reach:</strong></td>
                            <td>${breakdown.avg_reach ? breakdown.avg_reach.toLocaleString() : 'N/A'}</td>
                        </tr>
                        <tr>
                            <td><strong>Reach Multiplier:</strong></td>
                            <td>${breakdown.reach_multiplier ? (breakdown.reach_multiplier * 100).toFixed(1) + '%' : 'N/A (skipped)'}</td>
                        </tr>
                    </table>

                    <div class="mt-3">
                        <h6>Calculation Steps:</h6>
                        <ol class="small">
                            <li>Base Price = €${breakdown.tier.cpt} × (${breakdown.followers.toLocaleString()} ÷ 1,000) = €${(breakdown.tier.cpt * (breakdown.followers / 1000)).toFixed(2)}</li>
                            <li>With Gamification = €${(breakdown.tier.cpt * (breakdown.followers / 1000)).toFixed(2)} × (1 + ${(breakdown.gamification_percentage * 100).toFixed(1)}%) = €${(breakdown.tier.cpt * (breakdown.followers / 1000) * (1 + breakdown.gamification_percentage)).toFixed(2)}</li>
                            ${breakdown.reach_multiplier ?
                                `<li>With Reach Adjustment = €${(breakdown.tier.cpt * (breakdown.followers / 1000) * (1 + breakdown.gamification_percentage)).toFixed(2)} × ${(breakdown.reach_multiplier * 100).toFixed(1)}% = €${price}</li>` :
                                '<li>Reach adjustment skipped (insufficient data or zero reach)</li>'
                            }
                        </ol>
                    </div>
                </div>
            </div>
        `;

        resultDiv.html(html);
    }

    function displayError(resultDiv, errorMessage) {
        resultDiv.html(`
            <div class="bg-danger text-white p-3 rounded mb-3">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                <strong>Error:</strong> ${errorMessage}
            </div>
        `);
    }

    // Auto-convert gamification percentage input
    $('input[name="gamification_percentage"]').on('input', function() {
        let value = parseFloat($(this).val()) || 0;
        if (value > 1) {
            // Convert percentage to decimal for form submission
            $(this).data('decimal-value', value / 100);
        } else {
            $(this).data('decimal-value', value);
        }
    });

    // Convert percentage to decimal before form submission
    $('form').on('submit', function() {
        const gamificationInput = $(this).find('input[name="gamification_percentage"]');
        if (gamificationInput.length) {
            let value = parseFloat(gamificationInput.val()) || 0;
            if (value > 1) {
                gamificationInput.val(value / 100);
            }
        }
    });
});
</script>
@endsection
