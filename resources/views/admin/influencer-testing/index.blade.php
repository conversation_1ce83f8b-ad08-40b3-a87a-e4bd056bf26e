@extends('admin.layouts.master_admin')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-flask mr-2"></i>
                        Influencer Pricing Testing Panel
                    </h3>
                    <div class="card-tools">
                        <span class="badge badge-info">{{ $influencers->total() }} Total Influencers</span>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        <strong>Testing Panel:</strong> Select an influencer to test the pricing system with fake data, calculate prices, and validate the pricing algorithm.
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Followers</th>
                                    <th>Gamification %</th>
                                    <th>Story Reach</th>
                                    <th><PERSON>el Reach</th>
                                    <th>Feed Reach</th>
                                    <th>Flags</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($influencers as $influencer)
                                <tr>
                                    <td>{{ $influencer->id }}</td>
                                    <td>
                                        @if($influencer->user)
                                            {{ $influencer->user->first_name }} {{ $influencer->user->last_name }}
                                            <br><small class="text-muted">{{ $influencer->user->email }}</small>
                                        @else
                                            <span class="text-warning">⚠️ No User Linked</span>
                                            <br><small class="text-muted">Influencer #{{ $influencer->id }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-primary">
                                            {{ number_format($influencer->followers ?? 0) }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-success">
                                            {{ ($influencer->gamification_percentage ?? 0) * 100 }}%
                                        </span>
                                    </td>
                                    <td>
                                        @if($influencer->ig_story_avg_reach)
                                            <span class="badge badge-info">{{ number_format($influencer->ig_story_avg_reach) }}</span>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($influencer->ig_reel_avg_reach)
                                            <span class="badge badge-info">{{ number_format($influencer->ig_reel_avg_reach) }}</span>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($influencer->ig_feed_avg_reach)
                                            <span class="badge badge-info">{{ number_format($influencer->ig_feed_avg_reach) }}</span>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($influencer->flag_story_insight_missing)
                                            <span class="badge badge-warning">Story</span>
                                        @endif
                                        @if($influencer->flag_reel_insight_missing)
                                            <span class="badge badge-warning">Reel</span>
                                        @endif
                                        @if($influencer->flag_feed_insight_missing)
                                            <span class="badge badge-warning">Feed</span>
                                        @endif
                                        @if(!$influencer->flag_story_insight_missing && !$influencer->flag_reel_insight_missing && !$influencer->flag_feed_insight_missing)
                                            <span class="badge badge-success">All Good</span>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="/admin/influencer-testing/{{ $influencer->id }}"
                                           class="btn btn-primary btn-test-pricing">
                                            <i class="fas fa-flask mr-1"></i>
                                            Test Pricing
                                        </a>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="9" class="text-center text-muted">
                                        <i class="fas fa-users-slash fa-2x mb-2"></i>
                                        <br>
                                        <p class="mb-3">No influencers found. Create some influencers first.</p>

                                        <form method="POST" action="{{ route('influencer-testing.create-from-users') }}" class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-success" id="create-influencers-btn">
                                                <i class="fas fa-magic mr-2"></i>
                                                Create Influencers from Users
                                            </button>
                                        </form>

                                        <p class="small text-muted mt-2">
                                            This will create influencer records for all users with type "influencer"
                                        </p>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        {{ $influencers->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('admin_script_codes')
<script>
$(document).ready(function() {
    // Handle create influencers button loading state
    $('#create-influencers-btn').closest('form').on('submit', function() {
        const button = $('#create-influencers-btn');
        button.prop('disabled', true);
        button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Creating Influencers...');
    });
});
</script>
@endsection
