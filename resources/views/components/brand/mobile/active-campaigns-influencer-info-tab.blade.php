<div id="mobile-collapse{{ $influencerCampaignDetail->campaign_id }}"
    class="accordion-collapse collapse"
    aria-labelledby="mobile-heading{{ $influencerCampaignDetail->campaign_id }}"
    data-bs-parent="#accordionExample" style="padding: 0px 10px;">
    <div style="width: 100%;height: 2px;background-color: #AD80FF; margin-bottom:10px"></div>
    <ul class="nav nav-tabs tabs new-tabs-ul" id="mobile-myTab{{ $influencerCampaignDetail->id }}"
        role="tablist"
        style="display:flex; justify-content: space-evenly; margin-bottom: 10px;">
        <li class="nav-item" style="margin:0;">
            <a class="nav-link active btn-campaign-status all"
                style="margin: 0; border-radius: 5px; font-size: 10px !important;"
                id="mobile-all-tab{{ $influencerCampaignDetail->id }}" data-bs-toggle="tab"
                data-bs-target="#mobile-all{{ $influencerCampaignDetail->id }}" type="button"
                role="tab" aria-controls="mobile-all{{ $influencerCampaignDetail->id }}"
                aria-selected="true">All</a>
        </li>
        <li class="nav-item" style="margin:0;">
            <a class="nav-link btn-campaign-status action"
                style="margin: 0; border-radius: 5px; font-size: 10px !important;"
                id="mobile-action-tab{{ $influencerCampaignDetail->id }}" data-bs-toggle="tab"
                data-bs-target="#mobile-action{{ $influencerCampaignDetail->id }}" type="button"
                role="tab" aria-controls="mobile-action{{ $influencerCampaignDetail->id }}"
                aria-selected="false">Action</a>
        </li>
        <li class="nav-item" style="margin:0;">
            <a class="nav-link btn-campaign-status inprogress"
                style="margin: 0; border-radius: 5px; font-size: 10px !important;"
                id="mobile-inprgoress-tab{{ $influencerCampaignDetail->id }}" data-bs-toggle="tab"
                data-bs-target="#mobile-inprgoress{{ $influencerCampaignDetail->id }}"
                type="button" role="tab"
                aria-controls="mobile-inprgoress{{ $influencerCampaignDetail->id }}"
                aria-selected="false">Inprogress</a>
        </li>
        <li class="nav-item" style="margin:0;">
            <a class="nav-link btn-campaign-status completed"
                style="margin: 0; border-radius: 5px; font-size: 10px !important;"
                id="mobile-completed-tab{{ $influencerCampaignDetail->id }}" data-bs-toggle="tab"
                data-bs-target="#mobile-completed{{ $influencerCampaignDetail->id }}" type="button"
                role="tab" aria-controls="mobile-completed{{ $influencerCampaignDetail->id }}"
                aria-selected="false">Completed</a>
        </li>
        <li class="nav-item" style="margin:0;">
            <a class="nav-link btn-campaign-status cancelled"
                style="margin: 0; border-radius: 5px; font-size: 10px !important;"
                id="mobile-cancelled-tab{{ $influencerCampaignDetail->id }}" data-bs-toggle="tab"
                data-bs-target="#mobile-cancelled{{ $influencerCampaignDetail->id }}" type="button"
                role="tab" aria-controls="mobile-cancelled{{ $influencerCampaignDetail->id }}"
                aria-selected="false">Cancelled</a>
        </li>
    </ul>
    <div class="tab-content">
        <div id="mobile-all{{ $influencerCampaignDetail->id }}" role="tabpanel"
            aria-labelledby="mobile-all-tab{{ $influencerCampaignDetail->id }}"
            class="tab-pane active">
            <table>
                @foreach ($influencerCampaignDetail->influencer_request_details as $influencerRequestDetail)
                    {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                    @if (empty($influencerRequestDetail->influencerdetails))
                        @php continue; @endphp
                    @endif
                    @php
                        // Get social link once for this influencer
                        $socialLink = App\Models\SocialConnect::where(
                            'user_id',
                            $influencerRequestDetail->influencerdetails->user->id,
                        )
                            ->where('media', $influencerCampaignDetail->media)
                            ->first();

                        $isComplained = App\Models\Complaint::where(
                            'influencer_request_accept_id',
                            $influencerRequestDetail->influencer_request_accept_id,
                        );
                    @endphp
                    @if ($influencerRequestDetail->is_reviewable)
                        @php $btnStatus = 'Review'; @endphp
                        <tr class="action">
                    @elseif($influencerRequestDetail->is_cancelled)
                        @php $btnStatus = 'Cancelled'; @endphp
                        <tr class="cancelled">
                    @elseif ($influencerRequestDetail->is_onhold)
                        @php $btnStatus = 'On Hold'; @endphp
                        <tr class="inprogress complained">
                    @elseif($influencerRequestDetail->is_completed)
                        @php $btnStatus = 'Completed'; @endphp
                        <tr class="completed">
                    @elseif($influencerRequestDetail->is_waiting)
                        @php $btnStatus = "Waiting"; @endphp
                        <tr class="inprogress">
                    @endif
                    @if ($influencerRequestDetail->is_reviewable)
                        <td style="width: 5%;">
                            <img width="20" height="20"
                                src="{{ asset('/assets/front-end/images/new/ph_warning.svg') }}">
                        </td>
                    @elseif($influencerRequestDetail->is_cancelled)
                        <td style="width: 5%;">
                            <img width="20" height="20"
                                src="{{ asset('/assets/front-end/images/new/ph_cancel.svg') }}">
                        </td>
                    @elseif($influencerRequestDetail->is_onhold || $influencerRequestDetail->is_waiting)
                        <td style="width: 5%;">
                            @if ($influencerRequestDetail->is_onhold)
                                <img width="20" height="20"
                                    src="{{ asset('/assets/front-end/images/new/ph_hold.svg') }}">
                            @else
                                <img width="20" height="20"
                                    src="{{ asset('/assets/front-end/images/data-time.svg') }}"
                                    alt="">
                            @endif
                        </td>
                    @elseif($influencerRequestDetail->is_completed)
                        <td style="width: 5%;">
                            <img width="20" height="20"
                                src="{{ asset('/assets/front-end/images/new/ph_complete.svg') }}">
                        </td>
                    @endif
                    @if ($socialLink != '')
                        <td style="width: 30%; font-weight: 700; font-size: 8px; text-align: center;">
                            <img width="15" height="15"
                                src="{{ $socialLink->picture_url }}">
                            <a style="font-weight:700; font-size: 8px; cursor: pointer;" target="popup"
                                data-bs-toggle="modal"
                                data-bs-target="#influncerdetailpopup{{ $socialLink->id }}">
                                {{ '@' . (isset($socialLink->name) ? $socialLink->name : '') }}
                            </a>
                        </td>
                        <td
                            style="width:20%; font-weight:700; font-size: 8px; text-align:center;">
                            {{ @$socialLink->followers }} Followers
                        </td>
                    @endif
                    <td style="width:15%; font-weight:700; font-size: 8px;">
                        € {{ number_format($influencerRequestDetail->discount_price, 2) }}
                    </td>

                    <td style="width:30%; font-weight: 800; font-size: 8px;">
                        <div class="pdf-button-container">
                        @if ($influencerRequestDetail->is_reviewable)
                            @php $content = $influencerRequestDetail->post_content_type; @endphp
                            <button
                                class="btn btn-review" id="btn-brand-review-submitted-content-{{ $influencerRequestDetail->id }}"
                                style="font-size: 8px; width: 100%; padding: 0; height: auto;"
                                onclick="brandReviewSubmittedContent('{{ $influencerRequestDetail->id }}','{{ $influencerRequestDetail->advertising }}','{{ $influencerRequestDetail->media }}','{{ $influencerRequestDetail->post_type }}','{{ $content }}')">Review
                            </button>
                        @elseif ($influencerRequestDetail->is_waiting)
                            <button class="btn btn-waiting"
                                style="font-size: 8px; width: 100%; padding: 0; height: auto;color: #AD80FF !important; border: solid 1px #AD80FF;  background-color: white;">Waiting
                            </button>
                        @elseif ($influencerRequestDetail->is_cancelled)
                            <button class="btn btn-cancel"
                                style="font-size: 8px; width: 100%; padding: 0; height: auto; color: #ffffff !important; background-color:red;">Cancelled
                            </button>
                        @elseif ($influencerRequestDetail->is_completed)
                            @if (isset($influencerRequestDetail->invoices->receipt))
                                <a href="{{ $influencerRequestDetail->invoices->receipt }}" target="_blank">
                                    <img src="{{ asset('/assets/front-end/images/icons/pdf-icon.svg') }}" class="mobile-pdf-icon">
                                </a>
                            @else
                            <img src="{{ asset('/assets/front-end/images/icons/pdf-icon.svg') }}" class="mobile-pdf-icon">
                            @endif
                            <button
                                class="btn btn-show-result btn-campaign-brand-show-results"
                                target="popup"
                                style="font-size: 8px; width: 100%; padding: 0; height: auto;"
                                data-bs-toggle="modal"
                                data-bs-target="#influencer-show-results-{{ $influencerRequestDetail->id }}">Show Results
                            </button>
                        @elseif ($influencerRequestDetail->is_onhold)
                            <button
                                style="font-size: 8px; width: 100%; padding: 0; height: auto;"
                                class="btn btn-hold">On hold
                            </button>
                        @endif
                        </div>{{-- end of pdf-button-container --}}
                        </td>
                    </tr>
                @endforeach
            </table>
        </div>
        <div id="mobile-action{{ $influencerCampaignDetail->id }}" role="tabpanel"
            aria-labelledby="mobile-action-tab{{ $influencerCampaignDetail->id }}" class="tab-pane">
            <table>
                @php $actionCount = 0; @endphp
                @foreach ($influencerCampaignDetail->influencer_request_details as $influencerRequestDetail)
                    {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                    @if (empty($influencerRequestDetail->influencerdetails))
                        @php continue; @endphp
                    @endif
                    @if ($influencerRequestDetail->is_reviewable)
                        @php
                            $actionCount++;
                            $socialLink = App\Models\SocialConnect::where(
                                'user_id',
                                $influencerRequestDetail->influencerdetails->user->id,
                            )
                                ->where('media', $influencerCampaignDetail->media)
                                ->first();
                        @endphp

                        <tr class="action">
                            <td style="width: auto">
                                <img width="20" height="20"
                                    src="{{ asset('/assets/front-end/images/new/ph_warning.svg') }}">
                            </td>
                            @if ($socialLink != '')
                                <td style="width: 30%; font-weight: 700; font-size: 8px; text-align: center;">
                                    <img width="15" height="15"
                                        src="{{ $socialLink->picture_url }}">
                                    <a style="font-weight:700; font-size: 8px; cursor: pointer;"
                                        target="popup" data-bs-toggle="modal"
                                        data-bs-target="#influncerdetailpopup{{ $socialLink->id }}">
                                        {{ '@' . (isset($socialLink->name) ? $socialLink->name : '') }}
                                    </a>
                                </td>
                                <td
                                    style="width:25%; font-weight:700; font-size: 8px; text-align:center;">
                                    {{ @$socialLink->followers }} Followers
                                </td>
                            @endif
                            <td style="width:15%; font-weight:700; font-size: 8px;">
                                € {{ number_format($influencerRequestDetail->discount_price, 2) }}
                            </td>
                            <td style="width:20%; font-weight: 800; font-size: 8px;">
                                @if ($influencerRequestDetail->is_onhold)
                                    <button class="btn btn-hold" style="font-size: 8px; width: 100%; padding: 0; height: auto;">On hold</button>
                                @endif
                                <button
                                    class="btn btn-review" id="btn-brand-review-submitted-content-{{ $influencerRequestDetail->id }}"
                                    style="font-size: 8px; width: 100%; padding: 0; height: auto;"
                                    onclick="brandReviewSubmittedContent('{{ $influencerRequestDetail->id }}','{{ $influencerRequestDetail->advertising }}','{{ $influencerRequestDetail->media }}','{{ $influencerRequestDetail->post_type }}','{{ $influencerRequestDetail->post_content_type }}')">Review
                                </button>
                            </td>
                        </tr>
                    @endif
                @endforeach
                @if ($actionCount == 0)
                    <tr>
                        <td class="text-center"></td>
                    </tr>
                @endif
            </table>
        </div>
        <div id="mobile-inprgoress{{ $influencerCampaignDetail->id }}" role="tabpanel"
            aria-labelledby="mobile-inprgoress-tab{{ $influencerCampaignDetail->id }}"
            class="tab-pane">
            <table>
                @php $inprogressCount = 0; @endphp
                @foreach ($influencerCampaignDetail->influencer_request_details as $influencerRequestDetail)
                    {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                    @if (empty($influencerRequestDetail->influencerdetails))
                        @php continue; @endphp
                    @endif
                    @if ($influencerRequestDetail->is_waiting || $influencerRequestDetail->is_onhold)
                        @php
                            $inprogressCount++;
                            $isComplained = App\Models\Complaint::where(
                                'influencer_request_accept_id',
                                $influencerRequestDetail->influencer_request_accept_id,
                            );
                        @endphp
                        <tr class="inprogress">
                            <td style="width: auto">
                                @if ($influencerRequestDetail->is_onhold)
                                    <img width="20" height="20"
                                        src="{{ asset('/assets/front-end/images/new/ph_hold.svg') }}">
                                @else
                                    <img width="20" height="20"
                                        src="{{ asset('/assets/front-end/images/data-time.svg') }}"
                                        alt="">
                                @endif
                            </td>

                            @php
                                $socialLink = App\Models\SocialConnect::where(
                                    'user_id',
                                    $influencerRequestDetail->influencerdetails->user->id,
                                )
                                ->where('media', $influencerCampaignDetail->media)
                                ->first();
                            @endphp

                            @if ($socialLink != '')
                                <td style="width: 30%; ">
                                    <img width="15" height="15"
                                        src="{{ $socialLink->picture_url }}">
                                    <span><a style="font-weight:700; font-size: 8px; cursor: pointer;"
                                            target="popup" data-bs-toggle="modal"
                                            data-bs-target="#influncerdetailpopup{{ $socialLink->id }}">
                                            {{ '@' . (isset($socialLink->name) ? $socialLink->name : '') }}
                                        </a></span>
                                </td>
                                <td style="width:25%; font-weight:700; font-size: 8px; text-align:center;">
                                    {{ @$socialLink->followers }} Followers
                                </td>
                            @endif
                            <td style="width:15%; font-weight:700; font-size: 8px;">
                                € {{ number_format($influencerRequestDetail->discount_price, 2) }}
                            </td>
                            <td style="width:20%; font-weight: 800; font-size: 8px;">
                                @if ($influencerRequestDetail->is_waiting)
                                    <button class="btn btn-waiting" style="font-size: 8px; width: 100%; padding: 0; height: auto;color: #AD80FF !important; border: solid 1px #AD80FF;  background-color: white;">
                                        Waiting
                                    </button>
                                @elseif ($influencerRequestDetail->is_onhold)
                                    <button style="font-size: 8px; width: 100%; padding: 0; height: auto;" class="btn btn-hold">
                                        On hold
                                    </button>
                                @endif
                            </td>
                        </tr>
                    @endif
                @endforeach
                @if ($inprogressCount == 0)
                    <tr>
                        <td class="text-center"></td>
                    </tr>
                @endif
            </table>
        </div>
        <div id="mobile-completed{{ $influencerCampaignDetail->id }}" role="tabpanel"
            aria-labelledby="mobile-completed-tab{{ $influencerCampaignDetail->id }}"
            class="tab-pane">
            <table>
                @php $completedCount = 0; @endphp
                @foreach ($influencerCampaignDetail->influencer_request_details as $influencerRequestDetail)
                    {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                    @if (empty($influencerRequestDetail->influencerdetails))
                        @php continue; @endphp
                    @endif
                    @if ($influencerRequestDetail->is_completed && $influencerRequestDetail->refund_reason != 'Refunded On Time Expired')
                        @php
                            $completedCount++;
                            $socialLink = App\Models\SocialConnect::where(
                                'user_id',
                                $influencerRequestDetail->influencerdetails->user->id,
                            )
                                ->where('media', $influencerCampaignDetail->media)
                                ->first();
                        @endphp

                        <tr class="completed">
                            <td style="width: 5%;">
                                <img width="20" height="20"
                                    src="{{ asset('/assets/front-end/images/new/ph_complete.svg') }}">
                            </td>
                            @if ($socialLink != '')
                                <td style="width: 30%; font-weight: 700; font-size: 8px; text-align: center;">
                                    <img width="15" height="15"
                                        src="{{ $socialLink->picture_url }}">
                                    <span><a target="popup" data-bs-toggle="modal"
                                            style="font-weight:700; font-size: 8px; cursor: pointer;"
                                            data-bs-target="#influncerdetailpopup{{ $socialLink->id }}">
                                            {{ '@' . (isset($socialLink->name) ? $socialLink->name : '') }}
                                        </a></span>
                                </td>
                                <td
                                    style="width:20%; font-weight:700; font-size: 8px; text-align:center;">
                                    {{ @$socialLink->followers }} Followers
                                </td>
                            @endif
                            <td style="width:15%; font-weight:700; font-size: 8px;">
                                € {{ number_format($influencerRequestDetail->discount_price, 2) }}
                            </td>
                            <td style="width:30%; font-weight: 800; font-size: 8px;">
                                <button
                                    class="btn btn-show-result btn-campaign-brand-show-results"
                                    target="popup"
                                    style="font-size: 8px; width: 100%; padding: 0; height: auto;"
                                    data-bs-toggle="modal"
                                    data-bs-target="#influencer-show-results-{{ $influencerRequestDetail->id }}">Show Results
                                </button>
                                @if ($influencerRequestDetail->is_onhold)
                                    <button class="btn btn-hold" style="font-size: 8px; width: 100%; padding: 0; height: auto;">
                                        On hold
                                    </button>
                                @endif
                            </td>
                        </tr>
                    @endif
                @endforeach
                @if ($completedCount == 0)
                    <tr>
                        <td class="text-center"></td>
                    </tr>
                @endif
            </table>
        </div>
        <div id="mobile-cancelled{{ $influencerCampaignDetail->id }}" role="tabpanel"
            aria-labelledby="mobile-cancelled-tab{{ $influencerCampaignDetail->id }}"
            class="tab-pane">
            <table>
                @php $cancelledCount = 0; @endphp

                @foreach ($influencerCampaignDetail->influencer_request_details as $influencerRequestDetail)
                    {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                    @if (empty($influencerRequestDetail->influencerdetails))
                        @php continue; @endphp
                    @endif
                    @if ($influencerRequestDetail->is_cancelled)
                        @php
                            $cancelledCount++;
                            $socialLink = App\Models\SocialConnect::where(
                                'user_id',
                                $influencerRequestDetail->influencerdetails->user->id,
                            )
                                ->where('media', $influencerCampaignDetail->media)
                                ->first();
                        @endphp
                        <tr class="cancelled">
                            <td style="width: auto">
                                <img width="20" height="20"
                                    src="{{ asset('/assets/front-end/images/new/ph_cancel.svg') }}">
                            </td>
                            @if ($socialLink != '')
                                <td style="width: 30%; font-weight: 700; font-size: 8px; text-align: center;">
                                    <img width="15" height="15"
                                        src="{{ $socialLink->picture_url }}">
                                    <a target="popup" data-bs-toggle="modal"
                                        style="font-weight:700; font-size: 8px; cursor: pointer;"
                                        data-bs-target="#influncerdetailpopup{{ $socialLink->id }}">
                                        {{ '@' . (isset($socialLink->name) ? $socialLink->name : '') }}
                                    </a>
                                </td>
                                <td style="width:25%; font-weight:700; font-size: 8px; text-align:center;">
                                    {{ @$socialLink->followers }} Followers
                                </td>
                            @endif
                            <td style="width:15%; font-weight:700; font-size: 8px;">
                                € {{ number_format($influencerRequestDetail->discount_price, 2) }}
                            </td>
                            <td style="width:20%; font-weight: 800; font-size: 8px;">
                                <button class="btn btn-cancel" target="popup"
                                    style="font-size: 8px; width: 100%; padding: 0; height: auto; color: #ffffff !important; background-color:red;"
                                    data-bs-toggle="modal"
                                    data-bs-target="#show-campaign-details-{{ $influencerCampaignDetail->id }}">Cancelled</button>
                                @if ($influencerRequestDetail->is_onhold)
                                    <button style="font-size: 8px; width: 100%; padding: 0; height: auto;" class="btn btn-hold">
                                        On hold
                                    </button>
                                @endif
                            </td>
                        </tr>
                    @endif
                @endforeach

                @if ($cancelledCount == 0)
                    <tr>
                        <td class="text-center"></td>
                    </tr>
                @endif
            </table>
        </div>
    </div>
</div>
