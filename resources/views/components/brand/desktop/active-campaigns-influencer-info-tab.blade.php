<div id="desktop-collapse{{ $influencerCampaignDetail->campaign_id }}"
    class="accordion-collapse collapse"
    aria-labelledby="heading{{ $influencerCampaignDetail->campaign_id }}"
    data-bs-parent="#accordionExample"
    style="padding: 0px 10px;">
    <ul class="nav nav-tabs tabs new-tabs-ul row" id="myTab{{ $influencerCampaignDetail->id }}" role="tablist">
        <li class="nav-item col">
            <a class="nav-link active btn-campaign-status all"
                style="margin:0; border-radius: 5px; text-align: center; border: solid 1px #AD80FF; color:#AD80FF"
                id="desktop-all-tab{{ $influencerCampaignDetail->id }}" data-bs-toggle="tab"
                data-bs-target="#desktop-all{{ $influencerCampaignDetail->id }}" type="button"
                role="tab" aria-controls="desktop-all{{ $influencerCampaignDetail->id }}"
                aria-selected="true">All</a>
        </li>
        <li class="nav-item col">
            <a class="nav-link btn-campaign-status action"
                style="margin:0; border-radius: 5px; text-align: center; border: solid 1px #AD80FF; color:#AD80FF"
                id="desktop-action-tab{{ $influencerCampaignDetail->id }}" data-bs-toggle="tab"
                data-bs-target="#desktop-action{{ $influencerCampaignDetail->id }}" type="button"
                role="tab" aria-controls="desktop-action{{ $influencerCampaignDetail->id }}"
                aria-selected="false">Action</a>
        </li>
        <li class="nav-item col">
            <a class="nav-link btn-campaign-status inprogress"
                style="margin:0; border-radius: 5px; text-align: center; border: solid 1px #AD80FF; color:#AD80FF"
                id="desktop-inprgoress-tab{{ $influencerCampaignDetail->id }}" data-bs-toggle="tab"
                data-bs-target="#desktop-inprgoress{{ $influencerCampaignDetail->id }}"
                type="button" role="tab"
                aria-controls="desktop-inprgoress{{ $influencerCampaignDetail->id }}"
                aria-selected="false">Inprogress</a>
        </li>
        <li class="nav-item col">
            <a class="nav-link btn-campaign-status completed"
                style="margin:0; border-radius: 5px; text-align: center; border: solid 1px #AD80FF; color:#AD80FF"
                id="desktop-completed-tab{{ $influencerCampaignDetail->id }}" data-bs-toggle="tab"
                data-bs-target="#desktop-completed{{ $influencerCampaignDetail->id }}" type="button"
                role="tab" aria-controls="desktop-completed{{ $influencerCampaignDetail->id }}"
                aria-selected="false">Completed</a>
        </li>
        <li class="nav-item col">
            <a class="nav-link btn-campaign-status cancelled"
                style="margin:0; border-radius: 5px; text-align: center; border: solid 1px #AD80FF; color:#AD80FF"
                id="desktop-cancelled-tab{{ $influencerCampaignDetail->id }}" data-bs-toggle="tab"
                data-bs-target="#desktop-cancelled{{ $influencerCampaignDetail->id }}" type="button"
                role="tab" aria-controls="desktop-cancelled{{ $influencerCampaignDetail->id }}"
                aria-selected="false">Cancelled</a>
        </li>
    </ul>
    <div class="tab-content">
        <div id="desktop-all{{ $influencerCampaignDetail->id }}" role="tabpanel"
            aria-labelledby="desktop-all-tab{{ $influencerCampaignDetail->id }}"
            style="margin: 0; max-width: 100% !important;"
            class="container tab-pane active"><br>
            <table class="campaign-table">
                @foreach ($influencerCampaignDetail->influencer_request_details as $influencerRequestDetail)
                    {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                    @if (empty($influencerRequestDetail->influencerdetails))
                        @php continue; @endphp
                    @endif
                    @php
                        // Get social link once for this influencer
                        $socialLink = App\Models\SocialConnect::where(
                            'user_id',
                            $influencerRequestDetail->influencerdetails->user->id,
                        )
                            ->where('media', $influencerCampaignDetail->media)
                            ->first();

                        $isComplained = App\Models\Complaint::where(
                            'influencer_request_accept_id',
                            $influencerRequestDetail->influencer_request_accept_id,
                        );
                    @endphp
                    @if ($influencerRequestDetail->is_reviewable)
                        @php $btnStatus = 'Review'; @endphp
                        <tr class="action">
                    @elseif($influencerRequestDetail->is_cancelled)
                        @php $btnStatus = 'Cancelled'; @endphp
                        <tr class="cancelled">
                    @elseif ($influencerRequestDetail->is_onhold)
                        @php $btnStatus = 'On Hold'; @endphp
                        <tr class="inprogress complained">
                    @elseif($influencerRequestDetail->is_completed)
                        @php $btnStatus = 'Completed'; @endphp
                        <tr class="completed">
                    @elseif($influencerRequestDetail->is_waiting)
                        @php $btnStatus = "Waiting"; @endphp
                        <tr class="inprogress">
                    @endif
                    @if ($influencerRequestDetail->is_reviewable)
                        <td style="width: 10%; text-align:center;">
                            <img src="{{ asset('/assets/front-end/images/new/ph_warning.svg') }}">
                        </td>
                    @elseif($influencerRequestDetail->is_cancelled)
                        <td style="width: 10%; text-align:center;">
                            <img src="{{ asset('/assets/front-end/images/new/ph_cancel.svg') }}">
                        </td>
                    @elseif($influencerRequestDetail->is_onhold || $influencerRequestDetail->is_waiting)
                        <td style="width: 10%; text-align:center;">
                            @if (($isComplained->exists() && $isComplained->first()->status == 'Inprogress') || $influencerCampaignDetail->is_paused == 1)
                                <img src="{{ asset('/assets/front-end/images/new/ph_hold.svg') }}">
                            @else
                                <img src="{{ asset('/assets/front-end/images/data-time.svg') }}">
                            @endif
                        </td>
                    @elseif($influencerRequestDetail->is_completed)
                        <td style="width: 5%; text-align:center;">
                            <img src="{{ asset('/assets/front-end/images/new/ph_complete.svg') }}">
                        </td>
                    @endif
                    @if ($socialLink != '')
                        <td style="width: 30%;">
                            <img src="{{ $socialLink->picture_url }}" alt="User">
                            <span>
                                <a target="popup" data-bs-toggle="modal" data-bs-target="#influncerdetailpopup{{ $socialLink->id }}" style="cursor: pointer;">
                                    {{ '@' . (isset($socialLink->name) ? $socialLink->name : '') }}
                                </a>
                            </span>
                        </td>
                        <td style="width: 20%">{{ @$socialLink->followers }} Followers</td>
                    @endif
                    <td style="width: 15%">
                        € {{ number_format($influencerRequestDetail->influencer_price, 2) }}
                    </td>
                    <td style="width: 30%; text-align: center;">
                        <div class="pdf-button-container">
                        @if ($influencerRequestDetail->is_reviewable)
                            @php $content = $influencerRequestDetail->post_content_type; @endphp
                            <button
                                class="btn btn-review" id="btn-brand-review-submitted-content-{{ $influencerRequestDetail->id }}"
                                onclick="brandReviewSubmittedContent('{{ $influencerRequestDetail->id }}','{{ $influencerRequestDetail->advertising }}','{{ $influencerRequestDetail->media }}','{{ $influencerRequestDetail->post_type }}','{{ $content }}')">Review
                            </button>
                        @elseif ($influencerRequestDetail->is_waiting)
                            <button class="btn btn-waiting">Waiting</button>
                        @elseif ($influencerRequestDetail->is_cancelled)
                            <button class="btn btn-cancel"
                                style="color: #ffffff !important; background-color:red; width: 50%;">Cancelled
                            </button>
                        @elseif ($influencerRequestDetail->is_completed)
                            @if (isset($influencerRequestDetail->invoices->receipt))
                                <a href="{{ $influencerRequestDetail->invoices->receipt }}" target="_blank">
                                    <img src="{{ asset('/assets/front-end/images/icons/pdf-icon.svg') }}" style="width: 35px !important; height: 35px !important;">
                                </a>
                            @else
                            <img src="{{ asset('/assets/front-end/images/icons/pdf-icon.svg') }}" style="width: 35px !important; height: 35px !important;">
                            @endif
                            <button class="btn btn-show-result btn-campaign-brand-show-results"
                                target="popup"
                                data-bs-toggle="modal"
                                data-bs-target="#influencer-show-results-{{ $influencerRequestDetail->id }}">Show Results
                            </button>
                        @elseif ($influencerRequestDetail->is_onhold)
                            <button class="btn btn-hold">On hold</button>
                        @endif
                        </div> {{-- end of pdf-button-container --}}
                    </td>
                    </tr>
                @endforeach
            </table>
        </div>
        <div id="desktop-action{{ $influencerCampaignDetail->id }}" role="tabpanel"
            aria-labelledby="desktop-action-tab{{ $influencerCampaignDetail->id }}"
            style="margin: 0; max-width: 100% !important;" class="container tab-pane">
            <br>
            <table class="campaign-table">
                @php $actionCount = 0; @endphp
                @foreach ($influencerCampaignDetail->influencer_request_details as $influencerRequestDetail)
                    {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                    @if (empty($influencerRequestDetail->influencerdetails))
                        @php continue; @endphp
                    @endif
                    @if ($influencerRequestDetail->is_reviewable)
                        @php
                            $actionCount++;
                            $socialLink = App\Models\SocialConnect::where(
                                'user_id',
                                $influencerRequestDetail->influencerdetails->user->id,
                            )
                                ->where('media', $influencerCampaignDetail->media)
                                ->first();
                        @endphp

                        <tr class="action">
                            <td style="width: 10%; text-align:center;">
                                <img src="{{ asset('/assets/front-end/images/new/ph_warning.svg') }}">
                            </td>
                            @if ($socialLink != '')
                                <td style="width: 25%;">
                                    <img src="{{ $socialLink->picture_url }}" alt="User">
                                    <span>
                                        <a target="popup"
                                            data-bs-toggle="modal"
                                            data-bs-target="#influncerdetailpopup{{ $socialLink->id }}"
                                            style="cursor: pointer;">
                                            {{ '@' . (isset($socialLink->name) ? $socialLink->name : '') }}
                                        </a>
                                    </span>
                                </td>
                                <td style="width: 15%">
                                    {{ @$socialLink->followers }} Followers
                                </td>
                            @endif
                            <td style="width: 15%">€ {{ number_format($influencerRequestDetail->influencer_price, 2) }}</td>
                            <td style="width: 20%; text-align:center;">
                                @if ($influencerRequestDetail->is_onhold)
                                    <button class="btn btn-hold">On hold</button>
                                @endif
                                <button class="btn btn-review" id="btn-brand-review-submitted-content-{{ $influencerRequestDetail->id }}"
                                    onclick="brandReviewSubmittedContent(
                                        '{{ $influencerRequestDetail->id }}',
                                        '{{ $influencerRequestDetail->advertising }}',
                                        '{{ $influencerRequestDetail->media }}',
                                        '{{ $influencerRequestDetail->post_type }}',
                                        '{{ $influencerRequestDetail->post_content_type }}')"
                                    > Review
                                </button>
                            </td>
                        </tr>
                    @endif
                @endforeach
                @if ($actionCount == 0)
                    <tr>
                        <td class="text-center">Empty</td>
                    </tr>
                @endif
            </table>
        </div>
        <div id="desktop-inprgoress{{ $influencerCampaignDetail->id }}" role="tabpanel"
            aria-labelledby="desktop-inprgoress-tab{{ $influencerCampaignDetail->id }}"
            style="margin: 0; max-width: 100% !important;" class="container tab-pane">
            <br>
            <table class="campaign-table">
                @php $inprogressCount = 0; @endphp
                @foreach ($influencerCampaignDetail->influencer_request_details as $influencerRequestDetail)
                    {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                    @if (empty($influencerRequestDetail->influencerdetails))
                        @php continue; @endphp
                    @endif
                    @if ($influencerRequestDetail->is_waiting || $influencerRequestDetail->is_onhold)
                        @php
                            $inprogressCount++;
                            $isComplained = App\Models\Complaint::where(
                                'influencer_request_accept_id',
                                $influencerRequestDetail->influencer_request_accept_id,
                            );
                        @endphp
                        <tr class="inprogress">
                            <td style="width: 10%; text-align:center;">
                                @if ($influencerRequestDetail->is_onhold)
                                    <img src="{{ asset('/assets/front-end/images/new/ph_hold.svg') }}">
                                @else
                                    <img src="{{ asset('/assets/front-end/images/data-time.svg') }}" alt="">
                                @endif
                            </td>

                            @php
                                $socialLink = App\Models\SocialConnect::where(
                                    'user_id',
                                    $influencerRequestDetail->influencerdetails->user->id,
                                )
                                ->where('media', $influencerCampaignDetail->media)
                                ->first();
                            @endphp

                            @if ($socialLink != '')
                                <td style="width: 25%;">
                                    <img src="{{ $socialLink->picture_url }}" alt="User">
                                    <span>
                                        <a target="popup" data-bs-toggle="modal" data-bs-target="#influncerdetailpopup{{ $socialLink->id }}" style="cursor: pointer;">
                                            {{ '@' . (isset($socialLink->name) ? $socialLink->name : '') }}
                                        </a>
                                    </span>
                                </td>
                                <td style="width: 15%">
                                    {{ @$socialLink->followers }} Followers
                                </td>
                            @endif
                            <td style="width: 15%">
                                € {{ number_format($influencerRequestDetail->influencer_price, 2) }}
                            </td>
                            <td style="width: 20%; text-align:center;">
                                @if ($influencerRequestDetail->is_waiting)
                                    <button class="btn btn-waiting">Waiting</button>
                                @elseif ($influencerRequestDetail->is_onhold)
                                    <button class="btn btn-hold">On hold</button>
                                @endif
                            </td>
                        </tr>
                    @endif
                @endforeach
                @if ($inprogressCount == 0)
                    <tr>
                        <td class="text-center"></td>
                    </tr>
                @endif
            </table>
        </div>
        <div id="desktop-completed{{ $influencerCampaignDetail->id }}" role="tabpanel"
            aria-labelledby="desktop-completed-tab{{ $influencerCampaignDetail->id }}"
            style="margin: 0; max-width: 100% !important;" class="container tab-pane">
            <table class="campaign-table">
                @php $completedCount = 0; @endphp

                @foreach ($influencerCampaignDetail->influencer_request_details as $influencerRequestDetail)
                    {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                    @if (empty($influencerRequestDetail->influencerdetails))
                        @php continue; @endphp
                    @endif
                    @if ($influencerRequestDetail->is_completed && $influencerRequestDetail->refund_reason != 'Refunded On Time Expired')
                        @php
                            $completedCount++;
                            $socialLink = App\Models\SocialConnect::where(
                                'user_id',
                                $influencerRequestDetail->influencerdetails->user->id,
                            )
                                ->where('media', $influencerCampaignDetail->media)
                                ->first();
                        @endphp

                        <tr class="completed">
                            <td style="width: 5%; text-align:center;">
                                <img src="{{ asset('/assets/front-end/images/new/ph_complete.svg') }}">
                            </td>
                            @if ($socialLink != '')
                                <td style="width: 30%;">
                                    <img src="{{ $socialLink->picture_url }}" alt="User">
                                    <span>
                                        <a target="popup"
                                            data-bs-toggle="modal"
                                            data-bs-target="#influncerdetailpopup{{ $socialLink->id }}"
                                            style="cursor: pointer;">
                                            {{ '@' . (isset($socialLink->name) ? $socialLink->name : '') }}
                                        </a>
                                    </span>
                                </td>
                                <td style="width: 20%">
                                    {{ @$socialLink->followers }} Followers
                                </td>
                            @endif
                            <td style="width: 15%">€ {{ number_format($influencerRequestDetail->influencer_price, 2) }}</td>
                            <td style="width: 30%; text-align:center;">
                                <button
                                    class="btn btn-show-result btn-campaign-brand-show-results"
                                    target="popup"
                                    data-bs-toggle="modal"
                                    data-bs-target="#influencer-show-results-{{ $influencerRequestDetail->id }}">Show Results
                                </button>
                                @if ($influencerRequestDetail->is_onhold)
                                    <button class="btn btn-hold">On hold</button>
                                @endif
                            </td>
                        </tr>
                    @endif
                @endforeach

                @if ($completedCount == 0)
                    <tr>
                        <td class="text-center"></td>
                    </tr>
                @endif
            </table>
        </div>
        <div id="desktop-cancelled{{ $influencerCampaignDetail->id }}" role="tabpanel"
            aria-labelledby="desktop-cancelled-tab{{ $influencerCampaignDetail->id }}"
            style="margin: 0; max-width: 100% !important;" class="container tab-pane">
            <br>
            <table class="campaign-table">
                @php $cancelledCount = 0; @endphp

                @foreach ($influencerCampaignDetail->influencer_request_details as $influencerRequestDetail)
                    {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                    @if (empty($influencerRequestDetail->influencerdetails))
                        @php continue; @endphp
                    @endif
                    @if ($influencerRequestDetail->is_cancelled)
                        @php
                            $cancelledCount++;
                            $socialLink = App\Models\SocialConnect::where(
                                'user_id',
                                $influencerRequestDetail->influencerdetails->user->id,
                            )
                                ->where('media', $influencerCampaignDetail->media)
                                ->first();
                        @endphp
                        <tr class="accordian-table cancelled">
                            <td style="width: 10%; text-align:center;">
                                <img src="{{ asset('/assets/front-end/images/new/ph_cancel.svg') }}">
                            </td>
                            @if ($socialLink != '')
                                <td style="width: 25%;">
                                    <img src="{{ $socialLink->picture_url }}" alt="User">
                                    <span>
                                        <a target="popup" data-bs-toggle="modal" data-bs-target="#influncerdetailpopup{{ $socialLink->id }}" style="cursor: pointer;">
                                            {{ '@' . (isset($socialLink->name) ? $socialLink->name : '') }}
                                        </a>
                                    </span>
                                </td>
                                <td style="width: 15%">
                                    {{ @$socialLink->followers }} Followers
                                </td>
                            @endif
                            <td style="width: 15%">€ {{ number_format($influencerRequestDetail->influencer_price, 2) }}</td>
                            <td style="width: 20%; text-align:center;">
                                <button class="btn btn-cancel" target="popup"
                                    data-bs-toggle="modal"
                                    style="color: #ffffff !important; background-color:red; width: 50%;"
                                    data-bs-target="#show-campaign-details-{{ $influencerCampaignDetail->id }}">Cancelled</button>
                                @if ($influencerRequestDetail->is_onhold)
                                    <button class="btn btn-hold">On hold</button>
                                @endif
                            </td>
                        </tr>
                    @endif
                @endforeach

                @if ($cancelledCount == 0)
                    <tr>
                        <td class="text-center"></td>
                    </tr>
                @endif
            </table>
        </div>
    </div>
</div>
