<div class="modal fade influencer wewPopup request-popup"
    id="influencerCheckRequestModal-{{ $influencerCampaignData->id }}"
    data-bs-backdrop="static"
    data-bs-keyboard="false"
    tabindex="-1"
    aria-labelledby="requestFormLabel"
    aria-hidden="true">
    <div class="modal-dialog default-width modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button" class="btn-close" data-bs-dismiss="modal"
                    aria-label="Close">
                    <img src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}"
                        alt="">
                </button>
                <div class="popup-title">
                    {{ $influencerCampaignData->compaign_title }}
                </div>
                <div class="popup-title-id">
                    Campaign ID: {{ $influencerCampaignData->compaign_id }}
                </div>
                <form method="post" id="requestFormSubmit{{ $influencerCampaignData->id }}"
                    action="{{ url('/request-form') }}" data-parsley-validate>
                    @csrf
                    <input type="hidden" name="influencer_request_detail_id"
                        id="influencer_request_detail_id"
                        value="{{ isset($influencerCampaignData->id) ? $influencerCampaignData->id : '' }}">
                    <ul class="nav nav-tabs ordertab pointer-e-none" id="myTab"
                        role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active"
                                id="general-information-tab{{ $influencerCampaignData->id }}"
                                data-bs-toggle="tab"
                                data-bs-target="#general-information{{ $influencerCampaignData->id }}"
                                type="button" role="tab"
                                aria-controls="general-information{{ $influencerCampaignData->id }}"
                                aria-selected="true">General Information
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link"
                                id="order-detail-tab{{ $influencerCampaignData->id }}"
                                data-bs-toggle="tab"
                                data-bs-target="#order-detail{{ $influencerCampaignData->id }}"
                                type="button" role="tab"
                                aria-controls="order-detail{{ $influencerCampaignData->id }}"
                                aria-selected="false">My Tasks
                            </button>
                        </li>
                    </ul>
                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade show active"
                            id="general-information{{ $influencerCampaignData->id }}"
                            role="tabpanel"
                            aria-labelledby="general-information-tab{{ $influencerCampaignData->id }}">
                            <div class="inside-table request-content">
                                <div class="inside-table-row">
                                    <span class="type-label">Company Name</span>
                                    <span class="type-image"><img
                                            src="{{ asset('/assets/front-end/images/icons/icon-user-black.svg') }}"
                                            class="" alt=""></span>
                                    <span
                                        class="type-content">{{ $influencerCampaignData->user->company_name }}</span>
                                </div>
                                <div class="inside-table-row">
                                    <span class="type-label">Request date</span>
                                    <span class="type-image"><img
                                            src="{{ asset('/assets/front-end/images/icons/icon-calender-black.svg') }}"
                                            class="" alt=""></span>
                                    <span
                                        class="type-content">{{ date('d.m.Y', strtotime($influencerCampaignData->created_at)) }}</span>
                                </div>
                                <div class="inside-table-row">
                                    <span class="type-label">You get</span>
                                    <span class="type-image">
                                        <img src="{{ asset('/assets/front-end/images/icons/req-money.svg') }}" alt="">
                                    </span>
                                    <span class="type-content">
                                        {{ number_format($influencerCampaignData->total_amount, 2) }} €
                                    </span>
                                </div>
                                <div class="inside-table-row">
                                    <span class="type-label">Social Media</span>
                                    <span class="type-image">
                                        <img src="{{ asset('assets/front-end/images/icons/icon-cb-' . strtolower($influencerCampaignData->media) . '.svg') }}"
                                            alt="{{ ucfirst($influencerCampaignData->media) }} icon"
                                            class="social-media-icon">
                                    </span>
                                    <span
                                        class="type-content text-capitalize">{{ ucfirst($influencerCampaignData->media) }}</span>
                                </div>
                                <div class="inside-table-row">
                                    <span class="type-label">Brand name</span>
                                    <span class="type-image">
                                        <img src="{{ asset('/assets/front-end/images/icons/icon-brandname-black.svg') }}"></span>
                                    <span
                                        class="type-content">{{ $influencerCampaignData->name }}</span>
                                </div>
                                <div class="inside-table-row">
                                    <span class="type-label">Campaign type</span>
                                    <span class="type-image"><img
                                            src="{{ asset('/assets/front-end/images/icons/icon-boostme-black.svg') }}"
                                            class="" alt=""></span>
                                    <span
                                        class="type-content">{{ $influencerCampaignData->post_type }}</span>
                                </div>

                                @if (isset($influencerCampaignData->category))
                                    <div class="inside-table-row">
                                        <span class="type-label">Category</span>
                                        <span class="type-image"><img
                                                src="{{ asset('/assets/front-end/images/icons/icon-category-black.svg') }}"
                                                class="" alt=""></span>
                                        <span
                                            class="type-content">{{ $influencerCampaignData->category->name }}</span>
                                    </div>
                                @endif
                                <div class="inside-table-row">
                                    <span class="type-label">Results in </span>
                                    <span class="type-image"><img
                                            src="{{ asset('/assets/front-end/images/icons/icon-clock-black.svg') }}"
                                            class="" alt=""></span>
                                    <span class="type-content">
                                        @php
                                            $time =
                                                isset($influencerCampaignData->request_time_accept) &&
                                                $influencerCampaignData->request_time_accept == 1
                                                    ? $influencerCampaignData->request_time + $influencerCampaignData->time
                                                    : $influencerCampaignData->time;

                                            $created_date = date(
                                                'Y-m-d H:i:s',
                                                strtotime($influencerCampaignData->created_at),
                                            );
                                            $updated_date = date(
                                                'Y-m-d H:i:s',
                                                strtotime($influencerCampaignData->updated_at),
                                            );
                                            $campaignDate = date(
                                                'Y-m-d H:i:s',
                                                strtotime(
                                                    $created_date .
                                                        ' + ' .
                                                        $campaignRequestTime->request_time .
                                                        ' days',
                                                ),
                                            );
                                            $date = date('Y-m-d H:i:s');
                                            $seconds =
                                                strtotime($campaignDate) -
                                                strtotime($date);

                                            $days = floor($seconds / 86400);
                                            if ($days < 3 && $days >= 0) {
                                                $hours = floor(
                                                    ($seconds - $days * 86400) / 3600,
                                                );

                                                $minutes = floor(
                                                    ($seconds -
                                                        $days * 86400 -
                                                        $hours * 3600) /
                                                        60,
                                                );

                                                $seconds = floor(
                                                    $seconds -
                                                        $days * 86400 -
                                                        $hours * 3600 -
                                                        $minutes * 60,
                                                );
                                            }
                                        @endphp
                                    </span>
                                    <div class="timing">10 days</div>
                                </div>
                            </div>
                            <div class="continue-link-outer text-center m-0 mb-5">
                                <button type="button" class="continue-link continue-open">Continue</button>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="order-detail{{ $influencerCampaignData->id }}" role="tabpanel"
                            aria-labelledby="order-detail-tab{{ $influencerCampaignData->id }}">
                            <div class="request-content-data icon-before">
                                @php $tasks = $influencerCampaignData->tasks ; @endphp
                                @if (isset($tasks))
                                    @foreach ($tasks as $task)
                                        @if (isset($task->taskDetail) && $task->type == 'SocialMediaName')
                                            <div class="inside-table-row">
                                                <div class="order-titles">
                                                    {{ $task->taskDetail->task }}
                                                </div>
                                                <div class="order-content">
                                                    {{ $task->value }}
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach
                                    @foreach ($tasks as $task)
                                        @if (isset($task->taskDetail) && $task->type == 'Link')
                                            <div class="inside-table-row">
                                                <div class="order-titles">
                                                    {{ $task->taskDetail->task }}
                                                </div>
                                                <div class="order-content">
                                                    <div class="order-link">
                                                        <div class="link"
                                                            id="myInput{{ $task->id }}">
                                                            {{ $task->value }}</div>
                                                        <div class="copy-link">
                                                            <a class="copy_text"
                                                                id="jjhu"
                                                                data-toggle="tooltip"
                                                                title="Copy to Clipboard"
                                                                href="{{ $task->value }}">
                                                                <span
                                                                    class="">COPY</span>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach
                                    @foreach ($tasks as $task)
                                        @if (isset($task->taskDetail) && $task->type == 'UploadContent')
                                            <div class="inside-table-row">
                                                <div class="order-titles">
                                                    {{ $task->taskDetail->task }}
                                                </div>
                                                <div class="order-content">
                                                    <a class="table-btn"
                                                        href="{{ asset('storage/' . $task->value) }}"
                                                        download
                                                        style="color: black !important;width:186px !important;height:40px;box-shadow:none !important;">
                                                        Download
                                                    </a>
                                                    @if ($influencerCampaignData->post_content_type == 'video')
                                                        <img src="{{ url('/assets/front-end/icons/video_placeholder.png') }}"
                                                            width="40">
                                                    @else
                                                        <img src="{{ url('/assets/front-end/icons/image_placholder.png') }}"
                                                            width="40">
                                                    @endif
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach

                                    @foreach ($tasks as $task)
                                        @if (isset($task->taskDetail) && $task->type == 'Hashtag')
                                            <div class="inside-table-row">
                                                <div class="order-titles">
                                                    {{ $task->taskDetail->task }}
                                                </div>
                                                <div class="order-content">
                                                    @php $tags = explode(',', $task->value); @endphp
                                                    @foreach ($tags as $tag)
                                                        @if ($tag)
                                                            <div class="order-hash-tag">
                                                                <img src="{{ asset('/assets/front-end/images/icon-hash.png') }}" alt=""> {{ $tag }}
                                                            </div>
                                                        @endif
                                                    @endforeach

                                                </div>
                                            </div>
                                        @endif
                                    @endforeach
                                    @foreach ($tasks as $task)
                                        @if (isset($task->taskDetail) && $task->type == 'Info')
                                            <div class="inside-table-row">
                                                <div class="order-titles">
                                                    {{ $task->taskDetail->task }}
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach
                                @endif
                            </div>
                            <h5 style="text-align: center;padding:0 65px 0 65px;">
                                By submitting, I confirm that if I don't complete all
                                the agreed tasks, the client has the right to get a full
                                refund.
                            </h5>
                            <div class="order-accept-div"
                                style="display: flex; flex-direction: column; align-items: center; text-align: center;">
                                <div class="order-accept-text"
                                    style="margin-bottom: 20px;padding-top:20px;">
                                    Do you want to accept the campaign request?
                                </div>
                                <div class="order-accept-buttons"
                                    style="display: flex; flex-direction: row; justify-content: center; width: 100%; gap: 32px;">
                                    @if ($influencerCampaignData->request == '1')
                                        <button
                                            class="loaderShow table-btn green-btn ds accept"
                                            type="button" name="accept"
                                            value="Accepted" disabled
                                            style="font-size: 17px;width:200px;">
                                            <img class="icon-image_yes"
                                                src="{{ asset('/assets/front-end/images/icons/icon-green-check-new.svg') }}"
                                                alt=""
                                                style="padding-right:8px;">
                                            Yes
                                        </button>
                                    @else
                                        <button
                                            class="loaderShow table-btn green-btn ds accept"
                                            type="submit" name="accept"
                                            value="Accept Request"
                                            style="font-size: 17px;width:200px;">
                                            <img class="icon-image_yes"
                                                src="{{ asset('/assets/front-end/images/icons/icon-green-check-new.svg') }}"
                                                alt=""
                                                style="padding-right:8px;">
                                            Yes
                                        </button>
                                    @endif

                                    @if ($influencerCampaignData->request == '0')
                                        <button class="table-btn red-btn ds reject"
                                            type="button" name="reject"
                                            value="Rejected" disabled
                                            style="font-size: 17px;width:200px;">
                                            <img class="icon-image_no"
                                                src="{{ asset('/assets/front-end/images/icons/close-one-new.svg') }}"
                                                alt=""
                                                style="padding-right:8px;">
                                            No
                                        </button>
                                    @else
                                        <button class="table-btn red-btn ds reject"
                                            type="button" name="reject"
                                            id="reject"
                                            onclick="requestReject('{{ $influencerCampaignData->id }}')"
                                            value="Reject Request"
                                            style="font-size: 17px;width:200px;">
                                            <img class="icon-image_no"
                                                src="{{ asset('/assets/front-end/images/icons/close-one-new.svg') }}"
                                                alt=""
                                                style="padding-right:8px;">
                                            No
                                        </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
            </div>
            <script>
                document.querySelector("#myInput{{ $influencerCampaignData->id }}").onclick = (e) => {
                    navigator.clipboard.writeText(e.currentTarget.innerText);
                }
            </script>
            </form>
        </div>
    </div>
</div>