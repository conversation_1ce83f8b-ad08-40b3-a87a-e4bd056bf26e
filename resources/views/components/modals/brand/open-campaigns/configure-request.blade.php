{{-- Configure Request Modal Component --}}
<div class="modal fade takel influencer" id="configure{{ $influencerCampaignDetail->id }}"
    data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="configureLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button" class="btn-close" data-bs-dismiss="modal"
                    aria-label="Close"><img
                        src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}"
                        alt=""></button>
                <div class="wizardHeading">Product Information</div>

                <form method="post" action="{{ url('/request-now-update') }}"
                    data-parsley-validate>
                    @csrf
                    <input type="hidden" name="id" id="id"
                        value="{{ $influencerCampaignDetail->id }}">
                    <div class="wizardForm">
                        <div class="row">

                            <input class="form-control" type="hidden" name="compaign_id"
                                id="compaign_id" required value="{{ $influencerCampaignDetail->campaign_id }}"
                                readonly>

                            <div class="col-md-6 col-12">
                                <div class="floating-label smalSpace">
                                    <label>Compaign Title</label>
                                    <input class="form-control" type="text"
                                        name="compaign_title" id="compaign_title"
                                        value="{{ $influencerCampaignDetail->campaign_title }}">
                                </div>
                            </div>
                            <div class="col-md-6 col-12">
                                <div class="floating-label smalSpace">
                                    <label>Product/Service Name</label>
                                    <input class="form-control" type="text" name="name"
                                        id="name" required value="{{ $influencerCampaignDetail->name }}"
                                        readonly>
                                </div>
                            </div>
                            <div class="col-md-6 col-12">
                                <div class="floating-label smalSpace">
                                    <label>Online store link</label>
                                    <input class="form-control" type="text" name="link"
                                        id="link" value="{{ $influencerCampaignDetail->link }}">
                                </div>
                            </div>
                            <div class="col-md-6 col-12">
                                <div class="floating-label smalSpace">
                                    <label>Should the influencer publish the Online Store Link
                                        ?</label>
                                    <div class="tasklistinput">
                                        <div class="taslListCustomRatio">
                                            <input type="radio" id="publish_yes"
                                                name="publish" value="Yes"
                                                {{ $influencerCampaignDetail->publish == 'Yes' ? 'checked' : '' }}>
                                            <label for="publish_yes">yes</label>
                                        </div>
                                        <div class="taslListCustomRatio">
                                            <input type="radio" id="publish_no"
                                                name="publish" value="No"
                                                {{ $influencerCampaignDetail->publish == 'No' ? 'checked' : '' }}>
                                            <label for="publish_no">No</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 col-12">
                                <div class="floating-label smalSpace">
                                    <label>Short Product/Service Description</label>
                                    <textarea class="form-control textarea" name="short_product" id="name">{{ $influencerCampaignDetail->short_product }}</textarea>
                                </div>
                            </div>
                            <div class="col-md-12 col-12">
                                <label>Following Hashtags/ Mentions should be used by the
                                    influencer</label>
                                <div class="row">
                                    <div class="col-md-6 col-12">
                                        <div class="floating-label smalSpace">
                                            <div class="input-group">
                                                <span class="input-group-text"
                                                    id="basic-addon1">#</span>
                                                <input class="form-control" type="text"
                                                    name="hashtags" id="name"
                                                    value="{{ $influencerCampaignDetail->hashtags }}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-12">
                                        <div class="floating-label smalSpace">
                                            <div class="input-group">
                                                <span class="input-group-text"
                                                    id="basic-addon1">@</span>
                                                <input class="form-control" type="text"
                                                    name="mentions" id="name"
                                                    value="{{ $influencerCampaignDetail->mentions }}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-12">
                                <div class="floating-label smalSpace">
                                    <label>Following Social Media OR Website should be linked by
                                        the influencer</label>
                                    <input class="form-control" type="text" name="social"
                                        id="social" value="{{ $influencerCampaignDetail->social }}">
                                </div>
                            </div>
                            <div class="col-md-6 col-12">
                                <div class="floating-label smalSpace">
                                    <label>Which sub-category describes your product <br />the
                                        most</label>
                                    <input class="form-control" type="text"
                                        name="subcategory" id="subcategory"
                                        value="{{ $influencerCampaignDetail->subcategory }}">
                                </div>
                            </div>
                            <div class="col-md-6 col-12">
                                <div class="floating-label smalSpace">
                                    <label>How much time do you give the influencer to react
                                        (days)</label>
                                    <select class="form-control" name="time"
                                        id="time">
                                        <option {{ $influencerCampaignDetail->time == '3' ? 'selected' : '' }}>3
                                        </option>
                                        <option {{ $influencerCampaignDetail->time == '5' ? 'selected' : '' }}>5
                                        </option>
                                        <option {{ $influencerCampaignDetail->time == '7' ? 'selected' : '' }}>7
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <input class="form-control" type="hidden" name="current_price"
                                id="current_price{{ $influencerCampaignDetail->id }}"
                                value="{{ $influencerCampaignDetail->current_price }}" disabled>
                            <input type="hidden" id="admin_comission" value="5">
                        </div>
                    </div>
                    <div class="wizardForm">
                        <!-- Selected users //-->
                        <div class="popup2btns d-flex">
                            <input type="submit" class="loaderShow et-submit mx-2"
                                name="confirm" value="Confirm">
                            <a href="#" class="et-submit red-btn mx-2"
                                data-bs-dismiss="modal" aria-label="Close">Close</a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
    /* Ensure modal is hidden by default */
    #configure{{ $influencerCampaignDetail->id }} {
        display: none !important;
    }
    
    #configure{{ $influencerCampaignDetail->id }}.show {
        display: block !important;
    }
</style>
