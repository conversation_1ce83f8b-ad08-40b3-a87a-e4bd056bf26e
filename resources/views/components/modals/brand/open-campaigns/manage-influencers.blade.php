{{-- Manage Influencers Modal Component --}}
<div class="modal fade influencer influncerList" id="showInfluencers{{ $influencerCampaignDetail->id }}"
    data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="showInfluencersLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button" class="btn-close" data-bs-dismiss="modal"
                    aria-label="Close"><img
                        src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}"
                        alt=""></button>
                <div class="wizardHeading">Manage your Influencer</div>
                <div class="wizardForm">

                    <form method="post" action="{{ url('/update-influencer') }}"
                        data-parsley-validate>
                        @csrf
                        <input type="hidden" id="acceptedRequest"
                            value="{{ $influencerCampaignDetail->accept_request > 0 ? $influencerCampaignDetail->accept_request : 0 }}">
                        <input class="form-control" type="hidden" name="compaign_id"
                            id="compaign_id" required value="{{ $influencerCampaignDetail->campaign_id }}"
                            readonly>
                        @php
                            $lists = App\Models\InfluencerRequestDetail::where(
                                'compaign_id',
                                $influencerCampaignDetail->campaign_id,
                            )
                                ->where('status', null)
                            ->get(); @endphp
                        <div class="influncerlist-dropdown pb-5 ">
                            <div class="accordion" id="accordionExample">
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="heading1">
                                        <button class="accordion-button" type="button"
                                            data-bs-toggle="collapse"
                                            data-bs-target="#collapse1" aria-expanded="true"
                                            aria-controls="collapse1">
                                            <div class="img-to"><img
                                                    src="{{ asset('/assets/front-end/images/icons/icon-green-check.svg') }}"
                                                    alt=""></div>
                                            <div class="dp-text">
                                                {{ $influencerCampaignDetail->accept_request > 0 ? $influencerCampaignDetail->accept_request : 0 }}
                                                Influencer have joined the campaign</div>
                                            <div class="img-dropdown"><img
                                                    src="{{ asset('/assets/front-end/images/dropdown-black.png') }}"
                                                    alt=""></div>
                                        </button>
                                    </h2>
                                    <div id="collapse1"
                                        class="accordion-collapse collapse show"
                                        aria-labelledby="heading1"
                                        data-bs-parent="#accordionExample">
                                        <div class="accordion-body hide-check">
                                            <div class="influencer-list">
                                                @php $countinf = 0; @endphp
                                                @foreach ($lists as $list)
                                                    {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                                                    @if (empty($list->influencerdetails))
                                                        @php continue; @endphp
                                                    @endif
                                                    @if (isset($list->influencer_request_accepts->request) && $list->influencer_request_accepts->request == 1)
                                                        @php
                                                            $socialName = App\Models\SocialConnect::where(
                                                                'user_id',
                                                                $list->influencerdetails
                                                                    ->user->id,
                                                            )
                                                                ->where(
                                                                    'media',
                                                                    $influencerCampaignDetail->media,
                                                                )
                                                                ->first();
                                                        @endphp

                                                        <div class="dr-row">

                                                            <input
                                                                id="selectInfluencer{{ $list->influencerdetails->id }}"
                                                                type="hidden"
                                                                name="selectInfluencer[]"
                                                                value="{{ $list->influencerdetails->id }}">
                                                            <div class="customcheckbox">
                                                                <input type="checkbox">
                                                                <label
                                                                    class="customcheckboxlabel">
                                                                    <i
                                                                        class="fas fa-check"></i>
                                                                </label>
                                                            </div>
                                                            <div class="delete-inf">
                                                                <img src="{{ asset('/assets/front-end/images/icon-delete-red.png') }}"
                                                                    alt="">
                                                            </div>
                                                            @if ($socialName)
                                                                <div class="influencer-image">
                                                                    <img src="{{ $socialName->picture_url }}"
                                                                        alt="">
                                                                </div>

                                                                <a>
                                                                    <div class="influencer-name"
                                                                        data-bs-toggle="modal"
                                                                        data-bs-target="#influncerdetailpopup{{ $socialName->id }}"
                                                                        data-bs-dismiss="modal">
                                                                        {{ $socialName->name }}
                                                                    </div>
                                                                </a>
                                                                <div class="influencer-follo">
                                                                    {{ $socialName->followers }}
                                                                    Follower
                                                                </div>
                                                            @endif
                                                            <div class="influencer-price">
                                                                {{ number_format($list->influencer_price, 2) }}
                                                                €
                                                            </div>
                                                        </div>
                                                        @php $countinf++; @endphp
                                                    @endif
                                                @endforeach
                                                @if ($countinf == 0)
                                                    <div class="text-center">Empty</div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="heading2">
                                        <button class="accordion-button collapsed"
                                            type="button" data-bs-toggle="collapse"
                                            data-bs-target="#collapse2" aria-expanded="false"
                                            aria-controls="collapse2">
                                            <div class="img-to"><img
                                                    src="{{ asset('/assets/front-end/images/icon-clock-yellow.png') }}"
                                                    alt=""></div>

                                            @php $respond=0; @endphp
                                            @php $countinf = 0; @endphp
                                            @foreach ($lists as $list)
                                                {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                                                @if (empty($list->influencerdetails))
                                                    @php continue; @endphp
                                                @endif
                                                @if (!isset($list->influencer_request_accepts->request))
                                                    @php $respond++; @endphp
                                                @endif
                                            @endforeach
                                            <input type="hidden" id="not_responded"
                                                value="{{ $respond }}">
                                            <div class="dp-text">{{ $respond }}
                                                Influencer did not respond yet </div>
                                            <div class="img-dropdown"><img
                                                    src="{{ asset('/assets/front-end/images/dropdown-black.png') }}"
                                                    alt=""></div>
                                        </button>
                                    </h2>
                                    <div id="collapse2" class="accordion-collapse collapse"
                                        aria-labelledby="heading2"
                                        data-bs-parent="#accordionExample">
                                        <div class="accordion-body hide-check">
                                            <div class="influencer-list">
                                                @foreach ($lists as $list)
                                                    {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                                                    @if (empty($list->influencerdetails))
                                                        @php continue; @endphp
                                                    @endif
                                                    @if (!isset($list->influencer_request_accepts->request))
                                                        @php
                                                            $socialName = App\Models\SocialConnect::where(
                                                                'user_id',
                                                                $list->influencerdetails
                                                                    ->user->id,
                                                            )
                                                                ->where(
                                                                    'media',
                                                                    $influencerCampaignDetail->media,
                                                                )
                                                                ->first();
                                                        @endphp
                                                        <div class="dr-row">
                                                            <input
                                                                id="selectInfluencer{{ $list->influencerdetails->id }}"
                                                                type="hidden"
                                                                name="selectInfluencer[]"
                                                                value="{{ $list->influencerdetails->id }}">
                                                            <div class="customcheckbox">
                                                                <input type="checkbox">
                                                                <label
                                                                    class="customcheckboxlabel">
                                                                    <i
                                                                        class="fas fa-check"></i>
                                                                </label>
                                                            </div>
                                                            <div class="delete-inf">
                                                                <img src="{{ asset('/assets/front-end/images/icon-delete-red.png') }}"
                                                                    alt="">
                                                            </div>

                                                            @if ($socialName)
                                                                <div class="influencer-image">
                                                                    <img src="{{ $socialName->picture_url }}"
                                                                        alt="">
                                                                </div>
                                                                <a>
                                                                    <div class="influencer-name"
                                                                        data-bs-toggle="modal"
                                                                        data-bs-target="#influncerdetailpopup{{ $socialName->id }}"
                                                                        data-bs-dismiss="modal">
                                                                        {{ $socialName->name }}
                                                                    </div>
                                                                </a>
                                                                <div class="influencer-follo">
                                                                    {{ $socialName->followers }}
                                                                    Follower
                                                                </div>
                                                            @endif
                                                            <div class="influencer-price">
                                                                <?php
                                                                $newLivetreamPrice = 0;
                                                                $addNewLivetreamPrice = 0;

                                                                $user = App\Models\User::find($list->influencerdetails->user->id);

                                                                // Use the new InfluencerPrice system instead of legacy AdvertisingMethodPrice
                                                                if ($user->influencer) {
                                                                    // Map legacy advertising types to proper campaign types
                                                                    $campaignType = match ($list->advertising) {
                                                                        App\Constants\CampaignType::BOOST_ME, 'Boost me', 'boost me', 'Story', 'Story - Picture', 'Story - Video' => App\Constants\CampaignType::BOOST_ME,
                                                                        App\Constants\CampaignType::REACTION_VIDEO, 'Reaction video', 'reaction video', 'Reel' => App\Constants\CampaignType::REACTION_VIDEO,
                                                                        App\Constants\CampaignType::SURVEY, 'survey', 'Post - Picture' => App\Constants\CampaignType::SURVEY,
                                                                        default => App\Constants\CampaignType::BOOST_ME // fallback
                                                                    };

                                                                    $influencerPrice = $user->influencer->prices()
                                                                        ->where('campaign_type', $campaignType)
                                                                        ->first();

                                                                    if ($influencerPrice) {
                                                                        $newLivetreamPrice = $influencerPrice->price;
                                                                    }
                                                                }

                                                                ?>
                                                                {{ number_format($list->influencer_price, 2) }}
                                                                €
                                                            </div>
                                                        </div>
                                                        @php $countinf++; @endphp
                                                    @endif
                                                @endforeach
                                                @if ($countinf == 0)
                                                    <div class="text-center">Empty</div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="heading3">
                                        <button class="accordion-button collapsed"
                                            type="button" data-bs-toggle="collapse"
                                            data-bs-target="#collapse3" aria-expanded="false"
                                            aria-controls="collapse3">
                                            <div class="img-to"><img
                                                    src="{{ asset('/assets/front-end/images/icon-attention-red.png') }}"
                                                    alt=""></div>

                                            @php $reject=0; @endphp
                                            @php $countinf = 0; @endphp
                                            @foreach ($lists as $list)
                                                {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                                                @if (empty($list->influencerdetails))
                                                    @php continue; @endphp
                                                @endif
                                                @if (isset($list->influencer_request_accepts->request) && $list->influencer_request_accepts->request == 0)
                                                    @php $reject++; @endphp
                                                @endif
                                            @endforeach
                                            <input type="hidden" id="rejected_inf"
                                                value="{{ $reject }}">
                                            <div class="dp-text">{{ $reject }}
                                                Influencer have rejected the campaign </div>
                                            <div class="img-dropdown"><img
                                                    src="{{ asset('/assets/front-end/images/dropdown-black.png') }}"
                                                    alt=""></div>
                                        </button>
                                    </h2>
                                    <div id="collapse3" class="accordion-collapse collapse"
                                        aria-labelledby="heading3"
                                        data-bs-parent="#accordionExample">
                                        <div class="accordion-body hide-check">
                                            <div class="influencer-list">
                                                @foreach ($lists as $list)
                                                    {{-- TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel  --}}
                                                    @if (empty($list->influencerdetails))
                                                        @php continue; @endphp
                                                    @endif
                                                    @if (isset($list->influencer_request_accepts->request) && $list->influencer_request_accepts->request == 0)
                                                        @php
                                                            $socialName = App\Models\SocialConnect::where(
                                                                'user_id',
                                                                $list->influencerdetails
                                                                    ->user->id,
                                                            )
                                                                ->where(
                                                                    'media',
                                                                    $influencerCampaignDetail->media,
                                                                )
                                                                ->first();
                                                        @endphp
                                                        <input
                                                            id="selectInfluencer{{ $list->influencerdetails->id }}"
                                                            type="hidden"
                                                            name="selectInfluencerJoin[]"
                                                            value="{{ $list->influencerdetails->id }}">
                                                        <div class="dr-row">
                                                            <div class="delete-inf dsd">
                                                                <img src="{{ asset('/assets/front-end/images/icon-attention-red.png') }}"
                                                                    alt="">
                                                            </div>
                                                            @if ($socialName)
                                                                <div class="influencer-image">
                                                                    <img src="{{ $socialName->picture_url }}"
                                                                        alt="">
                                                                </div>
                                                                <a>
                                                                    <div class="influencer-name"
                                                                        target="popup"
                                                                        data-bs-toggle="modal"
                                                                        data-bs-target="#influncerdetailpopup{{ $socialName->id }}">
                                                                        {{ $socialName->name }}
                                                                    </div>
                                                                </a>
                                                                <div class="influencer-follo">
                                                                    {{ $socialName->followers }}
                                                                    Follower
                                                                </div>
                                                            @endif
                                                            <div class="influencer-price">
                                                                @php
                                                                    $newLivetreamPrice = 0;
                                                                    $addNewLivetreamPrice = 0;

                                                                    $user = App\Models\User::find($list->influencerdetails->user->id);

                                                                    // Use the new InfluencerPrice system instead of legacy AdvertisingMethodPrice
                                                                    if ($user->influencer) {
                                                                        // Map legacy advertising types to proper campaign types
                                                                        $campaignType = match ($list->advertising) {
                                                                            App\Constants\CampaignType::BOOST_ME, 'Boost me', 'boost me', 'Story', 'Story - Picture', 'Story - Video' => App\Constants\CampaignType::BOOST_ME,
                                                                            App\Constants\CampaignType::REACTION_VIDEO, 'Reaction video', 'reaction video', 'Reel' => App\Constants\CampaignType::REACTION_VIDEO,
                                                                            App\Constants\CampaignType::SURVEY, 'survey', 'Post - Picture' => App\Constants\CampaignType::SURVEY,
                                                                            default => App\Constants\CampaignType::BOOST_ME // fallback
                                                                        };

                                                                        $influencerPrice = $user->influencer->prices()
                                                                            ->where('campaign_type', $campaignType)
                                                                            ->first();

                                                                        if ($influencerPrice) {
                                                                            $newLivetreamPrice = $influencerPrice->price;
                                                                        }
                                                                    }
                                                                @endphp
                                                                {{ number_format($list->influencer_price, 2) }} €
                                                            </div>
                                                        </div>
                                                        @php $countinf++; @endphp
                                                    @endif
                                                @endforeach
                                                @if ($countinf == 0)
                                                    <div class="text-center">Empty</div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="heading4">
                                        @php
                                            $time = $influencerCampaignDetail->time;
                                            $created_date = date(
                                                'Y-m-d H:i:s',
                                                strtotime($influencerCampaignDetail->created_at),
                                            );
                                            $updated_date = date(
                                                'Y-m-d H:i:s',
                                                strtotime($influencerCampaignDetail->updated_at),
                                            );
                                            $campaignDate = date(
                                                'Y-m-d H:i:s',
                                                strtotime(
                                                    $created_date .
                                                        ' + ' .
                                                        $campaignRequestTime->request_time .
                                                        ' days',
                                                ),
                                            );
                                            $date = date('Y-m-d H:i:s');
                                            $seconds =
                                                strtotime($campaignDate) - strtotime($date);
                                            $hours = floor($seconds / 3600);

                                        @endphp

                                        <button class="accordion-button collapsed"
                                            type="button" data-bs-toggle="collapse"
                                            data-bs-target="#collapse4" aria-expanded="false"
                                            aria-controls="collapse4"
                                            @if ($hours <= 24) disabled @endif>
                                            <div class="img-to"><img
                                                    src="{{ asset('/assets/front-end/images/icon-plus-green.png') }}"
                                                    alt=""></div>
                                            <div class="dp-text">Add new influencer</div>
                                            <div class="img-dropdown"><img
                                                    src="{{ asset('/assets/front-end/images/dropdown-black.png') }}"
                                                    alt=""></div>
                                        </button>
                                    </h2>
                                    <div id="collapse4" class="accordion-collapse collapse"
                                        aria-labelledby="heading4"
                                        data-bs-parent="#accordionExample">
                                        <div class="accordion-body show-check">
                                            <div class="influencer-list" id="new_influncer">
                                                @foreach ($influencerCampaignDetail->influencerslist as $list)
                                                    @php
                                                        $req_count = App\Models\InfluencerRequestDetail::where(
                                                            'influencer_detail_id',
                                                            $list->i_id,
                                                        )
                                                            ->where('review', null)
                                                            ->where('refund_reason', null)
                                                            ->count();
                                                    @endphp

                                                    @if ($req_count < 5 || (isset($list->request_status) && $list->request_status != null))
                                                        @if ($list->media == $influencerCampaignDetail->media && $list->username != '' && isset($list->user_id) && $list->request_status == null)
                                                            @php
                                                                $socialName = App\Models\SocialConnect::where(
                                                                    'user_id',
                                                                    $list->user_id,
                                                                )
                                                                    ->where(
                                                                        'media',
                                                                        $influencerCampaignDetail->media,
                                                                    )
                                                                    ->first();
                                                            @endphp
                                                            <div class="dr-row">
                                                                <div class="customcheckbox">
                                                                    <input type="checkbox"
                                                                        class="add_new_influencer"
                                                                        name="selectInfluencer[]"
                                                                        value="{{ $list->i_id }}">
                                                                    <label
                                                                        class="customcheckboxlabel">
                                                                        <i
                                                                            class="fas fa-check"></i>
                                                                    </label>
                                                                </div>
                                                                <div class="influencer-image">
                                                                    <img src="{{ $list->picture_url }}"
                                                                        alt="">
                                                                </div>

                                                                @if ($socialName)
                                                                    <a>
                                                                        <div class="influencer-name"
                                                                            target="popup"
                                                                            data-bs-toggle="modal"
                                                                            data-bs-target="#influncerdetailpopup{{ $socialName->id }}">
                                                                            {{ $socialName->name }}
                                                                        </div>
                                                                    </a>
                                                                    <div
                                                                        class="influencer-follo">
                                                                        {{ $socialName->followers }}
                                                                        Follower
                                                                    </div>
                                                                @endif
                                                                <div class="influencer-price">
                                                                    <?php
                                                                    $newLivetreamPrice = 0;
                                                                    $addNewLivetreamPrice = 0;

                                                                    $user = App\Models\User::find($list->user_id);

                                                                    // Use the new InfluencerPrice system instead of legacy AdvertisingMethodPrice
                                                                    if ($user->influencer) {
                                                                        // Map legacy advertising types to proper campaign types
                                                                        $campaignType = match ($influencerCampaignDetail->advertising) {
                                                                            App\Constants\CampaignType::BOOST_ME, 'Boost me', 'boost me', 'Story', 'Story - Picture', 'Story - Video' => App\Constants\CampaignType::BOOST_ME,
                                                                            App\Constants\CampaignType::REACTION_VIDEO, 'Reaction video', 'reaction video', 'Reel' => App\Constants\CampaignType::REACTION_VIDEO,
                                                                            App\Constants\CampaignType::SURVEY, 'survey', 'Post - Picture' => App\Constants\CampaignType::SURVEY,
                                                                            default => App\Constants\CampaignType::BOOST_ME // fallback
                                                                        };

                                                                        $influencerPrice = $user->influencer->prices()
                                                                            ->where('campaign_type', $campaignType)
                                                                            ->first();

                                                                        if ($influencerPrice) {
                                                                            $newLivetreamPrice = $influencerPrice->price;
                                                                        }
                                                                    }
                                                                    ?>
                                                                    {{ number_format($list->type_price, 2) }}
                                                                    €
                                                                </div>
                                                            </div>
                                                        @endif
                                                    @endif
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="text-center start-camp mt-0"
                            style="margin-top:0 !important;">
                            <input type="submit" class="start-camp-btn" name="Update"
                                value="Update Influencers">
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Ensure modal is hidden by default */
    #showInfluencers{{ $influencerCampaignDetail->id }} {
        display: none !important;
    }
    
    #showInfluencers{{ $influencerCampaignDetail->id }}.show {
        display: block !important;
    }
</style>
