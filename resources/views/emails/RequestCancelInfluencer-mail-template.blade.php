@include('emails.mail-header-template')
<!-- Email Body -->
<tr>
    <td class="body" width="100%" cellpadding="0" cellspacing="0"
        style="box-sizing: border-box; position: relative; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%; background-color: #edf2f7; border-bottom: 1px solid #edf2f7; border-top: 1px solid #edf2f7; margin: 0; padding: 0; width: 100%;">
        <table class="inner-body" align="center" width="570" cellpadding="0" cellspacing="0"
            role="presentation"
            style="box-sizing: border-box; position: relative; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 720px; background-color: #ffffff; box-shadow: 0 2px 0 rgba(0, 0, 150, 0.025), 2px 4px 0 rgba(0, 0, 150, 0.015); margin: 0 auto; padding: 0; width: 720px; font-family: 'Roboto', sans-serif;">
            <!-- Body content -->
            <tbody>
                <tr>
                    <td class="content-cell"
                        style="box-sizing: border-box;position: relative;max-width: 100vw;padding-top: 6%;border: 0;text-align:center;">
                        <div class="page-mail-title"
                        style="box-sizing: border-box;position: relative;color: #AD80FF;text-align: center;font-size: calc(17px + 1vw);font-style: normal;font-weight: 700;line-height: 35px;font-family: 'Roboto', sans-serif;padding-bottom: 24px;">
                        Influencer Request Cancel</div>
                    </td>
                </tr>
                <tr>

                    <td class="content-cell"
                        style="box-sizing: border-box;position: relative;max-width: 100vw;padding: 6% 12%;border: 0;text-align:center; padding-top: 0">
                        
                        <p
                            style="box-sizing: border-box;position: relative;font-size: 15px;line-height: 24px;margin-top: 0;text-align: center;font-family: 'Roboto', sans-serif;color: #4D5043;margin-bottom: 11px;">
                            Hello {{ ucfirst($influencer->first_name) . ' ' . ucfirst($influencer->last_name) }},</p>
                        <p
                            style="box-sizing: border-box;position: relative;font-size: 15px;line-height: 24px;margin-top: 0;text-align: center;font-family: 'Roboto', sans-serif;color: #4D5043;margin-bottom: 11px;">
                            This is to inform that user {{ ucfirst($user->first_name) . '  ' . ucfirst($user->last_name) }} has cancelled the request. The details are as below:</p>
                        
                        <table border="0" cellpadding="0" cellspacing="0" width="auto" style="box-sizing: border-box; position: relative; font-size: 16px; line-height: 1.5em; margin-top: 0; text-align: left; font-family: 'Roboto', sans-serif;margin-left:auto;margin-right:auto;margin-bottom:20px;vertical-align: top">
                            <tr>
                                <th style="padding-right:15px; padding-bottom:5px;vertical-align: top">Name:</th>
                                <td style="padding-bottom:5px">{{ (isset($influencerDetail->media) ? $influencerDetail->media : 'Not mentioned') }}</td>
                            </tr>
                            <tr>
                                <th style="padding-right:15px; padding-bottom:5px;vertical-align: top">Hashtags:</th>
                                <td style="padding-bottom:5px">{{ (isset($influencerDetail->advertising) ? $influencerDetail->advertising : 'Not mentioned') }}</td>
                            </tr>
                            <tr>
                                <th style="padding-right:15px; padding-bottom:5px;vertical-align: top">Time:</th>
                                <td style="padding-bottom:5px">{{ (isset($influencerDetail->name) ? $influencerDetail->name : 'Not mentioned') }}</td>
                            </tr>
                            <tr>
                                <th style="padding-right:15px; padding-bottom:5px;vertical-align: top">Store Link:</th>
                                <td style="padding-bottom:5px">{{ ((isset($influencerDetail->hashtags) && strlen($influencerDetail->hashtags) > 1) ? $influencerDetail->hashtags : 'Not mentioned') }}</td>
                            </tr>
                        </table>

                        <p
                            style="box-sizing: border-box;position: relative;font-size: 15px;line-height: 24px;margin-top: 0;text-align: center;font-family: 'Roboto', sans-serif;color: #4D5043;margin-bottom: 11px;">
                            <a target="_blank" rel="noopener noreferrer" href="{{ url('verify-user-profile/' . $user->verify_token) }}" class="button button-primary" style="box-sizing: border-box; position: relative; font-family: 'Roboto', sans-serif; -webkit-text-size-adjust: none; border-radius: 10px; color: #fff; display: inline-block; overflow: hidden; text-decoration: none; font-size: 16px; background-color: #AD80FF; border-bottom: 8px solid #AD80FF; border-left: 18px solid #AD80FF; border-right: 18px solid #AD80FF; border-top: 8px solid #AD80FF;">View Request</a>
                        </p>

                        <p
                            style="box-sizing: border-box;position: relative;font-size: 15px;line-height: 24px;margin-top: 0;text-align: center;font-family: 'Roboto', sans-serif;color: #4D5043;margin-bottom: 11px;">
                            Thank you and best regards,<br>Your ClickItFame Team
                        </p>
                    </td>
                </tr>
            </tbody>
        </table>
    </td>
</tr> 
@include('emails.mail-footer-template')