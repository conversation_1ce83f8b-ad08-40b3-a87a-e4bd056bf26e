@font-face {
    font-family: Outfit;
    src: url('../fonts/Outfit-ExtraBold.eot');
    src: url('../fonts/Outfit-ExtraBold.eot?#iefix') format('embedded-opentype'),
        url('../fonts/Outfit-ExtraBold.woff2') format('woff2'),
        url('../fonts/Outfit-ExtraBold.woff') format('woff'),
        url('../fonts/Outfit-ExtraBold.ttf') format('truetype'),
        url('../fonts/Outfit-ExtraBold.svg#Outfit-ExtraBold') format('svg');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: Outfit;
    src: url('../fonts/Outfit-Black.eot');
    src: url('../fonts/Outfit-Black.eot?#iefix') format('embedded-opentype'),
        url('../fonts/Outfit-Black.woff2') format('woff2'),
        url('../fonts/Outfit-Black.woff') format('woff'),
        url('../fonts/Outfit-Black.ttf') format('truetype'),
        url('../fonts/Outfit-Black.svg#Outfit-Black') format('svg');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: Outfit;
    src: url('../fonts/Outfit-Bold.eot');
    src: url('../fonts/Outfit-Bold.eot?#iefix') format('embedded-opentype'),
        url('../fonts/Outfit-Bold.woff2') format('woff2'),
        url('../fonts/Outfit-Bold.woff') format('woff'),
        url('../fonts/Outfit-Bold.ttf') format('truetype'),
        url('../fonts/Outfit-Bold.svg#Outfit-Bold') format('svg');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: Outfit;
    src: url('../fonts/Outfit-SemiBold.eot');
    src: url('../fonts/Outfit-SemiBold.eot?#iefix') format('embedded-opentype'),
        url('../fonts/Outfit-SemiBold.woff2') format('woff2'),
        url('../fonts/Outfit-SemiBold.woff') format('woff'),
        url('../fonts/Outfit-SemiBold.ttf') format('truetype'),
        url('../fonts/Outfit-SemiBold.svg#Outfit-SemiBold') format('svg');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: Outfit;
    src: url('../fonts/Outfit-Thin.eot');
    src: url('../fonts/Outfit-Thin.eot?#iefix') format('embedded-opentype'),
        url('../fonts/Outfit-Thin.woff2') format('woff2'),
        url('../fonts/Outfit-Thin.woff') format('woff'),
        url('../fonts/Outfit-Thin.ttf') format('truetype'),
        url('../fonts/Outfit-Thin.svg#Outfit-Thin') format('svg');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: Outfit;
    src: url('../fonts/Outfit-Light.eot');
    src: url('../fonts/Outfit-Light.eot?#iefix') format('embedded-opentype'),
        url('../fonts/Outfit-Light.woff2') format('woff2'),
        url('../fonts/Outfit-Light.woff') format('woff'),
        url('../fonts/Outfit-Light.ttf') format('truetype'),
        url('../fonts/Outfit-Light.svg#Outfit-Light') format('svg');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: Outfit;
    src: url('../fonts/Outfit-ExtraLight.eot');
    src: url('../fonts/Outfit-ExtraLight.eot?#iefix') format('embedded-opentype'),
        url('../fonts/Outfit-ExtraLight.woff2') format('woff2'),
        url('../fonts/Outfit-ExtraLight.woff') format('woff'),
        url('../fonts/Outfit-ExtraLight.ttf') format('truetype'),
        url('../fonts/Outfit-ExtraLight.svg#Outfit-ExtraLight') format('svg');
    font-weight: 200;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: Outfit;
    src: url('../fonts/Outfit-Regular.eot');
    src: url('../fonts/Outfit-Regular.eot?#iefix') format('embedded-opentype'),
        url('../fonts/Outfit-Regular.woff2') format('woff2'),
        url('../fonts/Outfit-Regular.woff') format('woff'),
        url('../fonts/Outfit-Regular.ttf') format('truetype'),
        url('../fonts/Outfit-Regular.svg#Outfit-Regular') format('svg');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: Outfit;
    src: url('../fonts/Outfit-Medium.eot');
    src: url('../fonts/Outfit-Medium.eot?#iefix') format('embedded-opentype'),
        url('../fonts/Outfit-Medium.woff2') format('woff2'),
        url('../fonts/Outfit-Medium.woff') format('woff'),
        url('../fonts/Outfit-Medium.ttf') format('truetype'),
        url('../fonts/Outfit-Medium.svg#Outfit-Medium') format('svg');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}
.nav-item i {
    width: 26px;
    text-align: center;
}
ul.nav.nav-treeview {
    margin: 0 0 0 14px;
}
ul.nav.nav-treeview {
    margin: 0 0 0 14px;
}
ul.navbar-nav.ml-auto .top-right.links li {
    float: left;
    padding: 00 0 0 9px;
}
ul.navbar-nav.ml-auto .top-right.links li ul.dropdown-menu {
    right: 0;
    left: auto;
    width: 193px;
}
ul.parsley-errors-list li {list-style: none;font-size: 14px;color: red;}
ul.parsley-errors-list {
    padding: 0;
    margin: 0;
}
.form-group img {
    border: solid 1px #ccc;
    padding: 5px;
    margin-bottom: 8px;
}
form .jFiler-theme-default .jFiler-input {
    width: 100%;
    box-shadow: none;
    font-size: 1rem;
    line-height: 37px;
    padding: 0 12px;
    height: 40px;
    padding-right: 0;
}
form .jFiler-input-button {
    float: right;
    background: #343a40;
    color: #fff;
    font-size: 17px;
    padding: 1px 22px;
}
.listing_medias {
    display: inline-block;
}
form .jFiler-theme-default .jFiler-input-caption {
    padding: 0;
}
form .listing_media_main {
    float: left;
    display: inline-block;
    border: solid 1px #ccc;
    position: relative;
    padding: 5px;
    margin: 0 19px 19px -13px;
}
form span.badge.badge-dark.delete_rink_photo {
    position: absolute;
    right: 0;
    top: 0;
}
.login-logo {color: #fff;margin-top: 100px;}
#surfacesDiv .surfaceRow { margin-bottom: 8px; }
#surfacesDiv .surfaceRow input[type="text"]{border:1px solid #ced4da;border-radius: .25rem;}
.addMoreSurface {background: #0032A0;height: 40px;border: 0;color: #fff;font-size:22px;width: 40px;text-align: center;}
.removeSurface {background: #ff0000;height: 40px;border: 0;color: #fff;font-size:22px;width: 40px;text-align: center;line-height: 1;}
.slotsTabs [type="checkbox"]:not(:checked), .slotsTabs [type="checkbox"]:checked {position: absolute;left: -9999px;}
.slotsTabs [type="checkbox"]:not(:checked) + label, .slotsTabs [type="checkbox"]:checked + label {position: relative;cursor: pointer;font-size: 14px;color: #474747;font-weight: normal;display: inline-block;width: 242px;vertical-align: top;padding:8px 0 0 35px;}
.slotsTabs [type="checkbox"]:not(:checked) + label::before {content: '';position: absolute;left: 0;top:12px;width:22px;height:22px;border: 1px solid #DCDCDC;background: #fff;border-radius:3px;}
.slotsTabs [type="checkbox"]:checked + label::before {content: '';position: absolute;left: 0;top:12px;width:22px;height:22px;border: 1px solid #0032A0;background: #0032A0;border-radius:3px;}
.slotsTabs [type="checkbox"]:not(:checked) + label::after {content: '';position: absolute;top:10.4px;left: 4.7px;width: 12px;height: 12px;background: #fff;border-radius: 3px;}
.slotsTabs [type="checkbox"]:checked + label::after {content: '\f00c';font-family: "Font Awesome 5 Free";font-weight: 900;position: absolute;top:15px;left: 4.7px;color: #fff;font-size: 12px;}
.slotsTabs [type="checkbox"]:not(:checked) + label:after { opacity: 0;transform: scale(0);}
.slotsTabs [type="checkbox"]:checked + label:after {opacity: 1; transform: scale(1);}
.slotsTabs [type="checkbox"]:disabled:not(:checked) + label:before, .slotsTabs [type="checkbox"]:disabled:checked + label:before {box-shadow: none; border-color: #bbb;background-color: #ddd;}
.slotsTabs [type="checkbox"]:disabled:checked + label:after {color: #999;}
.slotsTabs [type="checkbox"]:disabled + label {color: #aaa;}
.slotsTabs [type="checkbox"]:checked:focus + label:before, .slotsTabs [type="checkbox"]:not(:checked):focus + label:before {border: 1px solid #212121;}
.slotsTabs [type="checkbox"] + label + .inputBox, .slotsTabs [type="checkbox"] + label + .multipleslotCost {display:none;}
.slotsTabs [type="checkbox"] + label + br + label { width: 180px; display: inline-block; }
.slotsTabs [type="checkbox"] + label + br + label + .inputBox { display: inline-block; }
.slotsTabs [type="checkbox"] + label + br + label + .inputBox input {border:1px solid #ced4da;border-radius: .25rem;padding: 5px 10px;}
.slotsTabs [type="checkbox"]:checked + label + .inputBox { display: inline-block; }
.slotsTabs [type="checkbox"]:checked + label + .multipleslotCost { display: inline-block; }
.multipleslotCost label {display: inline-block;width: 180px;}
.multipleslotCost .inputBox.slotCost {display: inline-block; margin-bottom:10px;}
.multipleslotCost .inputBox.slotCost input {border:1px solid #ced4da;border-radius: .25rem;padding: 5px 10px;}
.memberRow input[type="text"] {border: 1px solid #ced4da;border-radius: .25rem;padding: 5px 10px;margin: 6px 0 0 0; width: 100%;}
.memberRow .profilePic {width: 53px;height: 53px;padding: 2px;border: solid 1px #ccc; margin: 0 10px 8px 0;text-align: center;display: inline-block;vertical-align: top;}
.form-group .memberRow img {padding: 0; border: 0;margin: 0;width: 100%;height: 100%;}
.memberRow .inputfile + label {padding: 0;margin:0; outline: none; display: inline-block; width: 65%;}
.memberRow .profilePic + input + .inputfile + label span {font-size: 15px;display: block;font-weight: normal; position: relative;margin-bottom: 2px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;max-width:100%;}
.memberRow .profilePic + .inputfile + label span {font-size: 15px;display: block;font-weight: normal; position: relative;margin-bottom: 2px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;max-width:100%;}
.memberRow .inputfile + label strong {background: #0032A0;color: #fff;font-weight: normal;font-size: 16px;padding: 5px 10px;display: inline-block;border-radius: .25rem;min-width: 100px;text-align: center;margin-top:8px;}
.memberRow input.addMoreMember {background: #0032A0;border: 0;color: #fff;font-size: 23px;padding: 0;height: 30px;width: 30px;display: inline-block;line-height: 0;margin: 9px 0 0 0;}
.memberRow input.removeMember  {background:#c82333;border: 0;color: #fff;font-size: 23px;padding: 0;height: 30px;width: 30px;display: inline-block;line-height: 0;margin: 9px 0 0 0;}
.aboutbox .box label {border: 1px solid #ced4da;border-radius: .25rem;padding: 5px 10px;max-width: 100%;width: 100%;    position: relative; height: 42px;}
.aboutbox .box label span {font-weight: normal;font-size: 16px;}
.aboutbox .box label strong {background: #0032A0;color: #fff;font-weight: normal;position: absolute;font-size: 16px;
    right: 0;top: 0;bottom: 0;padding: 6px 20px;}
body .btn.btn-info {background: #0032A0;border: 0;min-width: 100px;}
section.errorpage a.btn.btn-link {
    background: #0032A0;
    color: #fff;
    margin-top: 30px;
    text-transform: uppercase;
}
section.errorpage a.btn.btn-link:hover, section.errorpage a.btn.btn-link:focus { text-decoration: none; opacity: 0.8 }
div#locationsDiv .form-control { margin-bottom: 10px; }
div#locationsDiv [type=button] {height: 35px;width: 35px;font-size: 18px;padding: 0;line-height: 25px;background: #0032A0;border: 0;border-radius: .25rem;color: #fff;}
.pdfBox .fileSHj { display: inline-block; }
.pdfBox .dgAction {display: inline-block;vertical-align: top;margin: 30px 0 0 10px;}
.social-input img.application-socialimage {
    max-width: 46px;
    max-height: 46px;
    margin-right: 11px;
    border: 0;
}
.social-input {
    margin: 11px 25px;
}
.checksosial .col-md-6:nth-child(2n) .social-input {
    margin-right: 0;
}
.checksosial .col-md-6:nth-child(odd) .social-input {
    margin-left: 0;
}
ul.navbar-nav.ml-auto .top-right.links .dropdown-menu li {
    padding: 0;
}
table.table.campaing_select th {
    background: transparent !important;
    border: 0 !important;
    text-align: center;
}

table.table.campaing_select td {
    border: 0;
    text-align: center;
}

table.table.campaing_select th img {max-width: 60px;max-height: 60px;min-width: 60px;margin-bottom: 14px;}

.smc-toggle-input {
    position: relative;
    text-align: center;
    width: 34px;
    height: 20px;
    margin: 0 auto;
    cursor: pointer;
}

.smc-toggle-input input ~ label {width: 34px;height: 20px;border: solid 3px #000;border-radius: 12px;position: relative;margin: 0;transition: all 0.3s;}

.smc-toggle-input input ~ label:after {
    content: "";
    width: 12px;
    height: 12px;
    background: #000;
    position: absolute;
    border-radius: 15px;
    top: 0;
    right: 15px;
    transition: all 0.3s;
    bottom: 0;
    margin: auto;
}

.smc-toggle-input input:checked ~ label:after {
    right: 1px;
    background: #1ABC00;
}

.smc-toggle-input input:checked ~ label {
    border-color: #1ABC00;
    transition: all 0.3s;
}

.smc-toggle-input input[type="checkbox"] {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 1;
    opacity: 0;
    cursor: pointer;
}
label.posttype-like {
    filter: drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25));
    background: var(--Gradient-look, linear-gradient(96deg, rgba(253, 155, 141, 0.95) 31.88%, rgba(248, 105, 136, 0.95) 55.38%, rgba(255, 128, 216, 0.95) 93.1%));
    width: 140.252px;
    height: 64px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #FFF;
    text-align: center;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 59.023%; /* 9.444px */
    border-radius: 18px;
    margin: 8px auto 19px;
}

label.posttype-like img {
    width: 21px;
    margin-bottom: 5px;
}
table.campaing_select {
    border-radius: 20px;
    border: 1px solid #AD80FF;
    background: #FEFEFC;
    margin-bottom: 26px;
}

.connectPrising.inner {
    max-width: 100%;
    margin: 0 auto;
}
.connectPrising .accordion-item {
    margin: 20px 0;
}
.connectPrising.inner .accordion-item {
    background: #fff;
    border: 1px solid #EBEBFF;
    border-radius: 21px !important;
}
button.accordion-button {
    color: #191d48;
    font-size: 16px;
    padding: 20px 23px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    text-decoration: none;
    font-weight: 700;
    width:100%;
}
.accordion-item .accordion-header {
    background-color: #fff !important;
    border: 0;
    margin: 0;
}
.connectPrising.inner .accordion-item .accordion-header button.accordion-button {
    display: flex;
    padding: 13px 20px 15px;
    justify-content: space-between;
    background: rgba(253, 155, 141, 0.1);
    border: 1px solid #AD80FF;
    border-radius: 20px;
    cursor: default;
}
.accordion-item table {
    width: 100%;
}
.accordion-item table {
    width: 100%;
}
.connectPrising table td {
    vertical-align: middle;
    padding: 0 6px;
    font-style: normal;
    font-weight: 700;
    font-size: 14px;
    letter-spacing: 1px;
    color: #000000;
    width: 18%;
}
.connectPrising table td.taskName {
    font-style: normal;
    font-weight: 800;
    font-size: 18px;
    line-height: 8px;
    letter-spacing: 1px;
    color: #000000;
    padding-bottom: 11px;
    text-align: left;
}
.connectPrising .accordion-item table td {
    font-size: 14px;
    font-weight: 800;
}
.standard-icon {
    width: 54px;
}
.accordion-button img {
    height: 22px;
    width: auto;
}
.taskName img {
    display: none;
}
.table-btn {
    width: 130px;
    height: 44px;
    border-radius: 12px;
    border: 1px solid #AD80FF;
    font-style: normal;
    font-weight: 800;
    font-size: 13px;
    text-align: center;
    color: #FFFFFF;
    transition: all 0.3s;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    display: inline-flex;
    margin: 4px 0;
    letter-spacing: 0;
    white-space: normal;
    line-height: 34px;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}
.nobg-btn {
    color: rgba(0, 0, 0, 0.89);
    line-height: 33px;
    background: #fff;
}
span.timing {
    border: 1px solid #9A9A9A;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 15px;
    font-style: normal;
    font-weight: 800;
    font-size: 13px;
    line-height: 16px;
    text-align: center;
    color: #323232;
    width: 145px;
    display: inline-block;
    padding: 9px 0;
    background-color: #ffffff;
    max-height: 37px;
}
.vertical-bottom span.timing {
    margin-bottom: 5px;
}
input.table-btn.finish-camp {
    width: 145px;
    margin-left: 0 !important;
    margin-right: 0 !important;
    margin-top: 9px !important;
    margin-bottom: 0 !important;
}
.accordion-button img {
    height: 22px;
    width: auto;
}
.collapsed img {
    cursor: pointer;
}
.connectPrising .camp-button img {
    transform: rotate(0deg);
    transition: all 0.3s;
}
.connectPrising .camp-button.collapsed img {
    transform: rotate(180deg);
    transition: all 0.3s;
}
.connectPrising table td.camp-button.dropdown-button img {
    width: 40px;
    height: auto;
    margin-right: 15px;
}
.nobg-btn:hover {
    background: #000000;
    color: #fff;
}
span.handelpletform {
    display: flex;
    align-items: center;
}
.accordion-item .handelpletform img {
    width: 45px;
    height: auto;
    margin: 0 7px 0 0;
}
span.story-type {
    max-width: 66px;
    white-space: normal;
    text-align: center;
    line-height: normal;
}
.connectPrising table td.soclDetail .d-flex.align-items-center {
    position: relative;
}
.soclDetail span {
    text-align: left;
    padding-left: 8px;
}
span.sertp {
    white-space: nowrap;
    max-width: 156px;
    overflow: hidden;
    text-overflow: ellipsis;
}
.soclPrice {
    font-family: 'Mulish';
    text-align: center;
    display: inline-flex;
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 1px;
    color: #000000;
    height: 100%;
    width: 98px;
    margin-left: 7px;
    align-items: center;
}
input[readonly] {
    border-color: transparent !important;
    background: #e7e7e7 !important;
}
div.accordion-collapse {
    position: relative;
}
.connectPrising.inner div.accordion-collapse {
    width: 100%;
    border: 0;
}
.accordion-collapse .accordion-body {
    position: relative;
}
.data-table {
    width: 1086px;
    margin: 0 auto;
}
.data-table.cac {
    max-width: 900px;
    text-align: left;
}
.nav-tabs {
    border: 0;
}
ul.nav-tabs.filtertab {
    justify-content: center;
    margin: 18px 0 25px;
    border: 0;
}
.nav-tabs .nav-link {
    border: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    flex: 1;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 16px;
    letter-spacing: -0.015em;
    color: #000000;
    font-family: 'Mulish', sans-serif;
    padding: 16px;
    cursor: pointer;
}
.nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}
.nav-tabs.filtertab .nav-link {
    border-color: #9A9A9A;
    background: #FFFFFF;
    border: 1px solid gray;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 15px;
    display: inline-block;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 18px;
    color: #000000;
    text-align: center;
    width: 138.01px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 11px;
    transition: all 0.3s;
}
.nav-tabs.filtertab .nav-link.active, .nav-tabs.filtertab .nav-link:hover {
    background: #9A9A9A;
    color: #fff;
    border-color: #9A9A9A;
}
.nav-tabs.filtertab .nav-link.action {
    border-color: #FF0000;
}
.nav-tabs.filtertab .nav-link.action.active,
.nav-tabs.filtertab .nav-link.action:hover {
    background: #FF0000;
}
.nav-tabs.filtertab .nav-link.cancelled {
    border-color: #FF0000;
}
.nav-tabs.filtertab .nav-link.cancelled.active,
.nav-tabs.filtertab .nav-link.cancelled:hover {
    background: #FF0000;
}
.nav-tabs.filtertab .nav-link.complete {
    border-color: #63C063;
}
.nav-tabs.filtertab .nav-link.complete.active,
.nav-tabs.filtertab .nav-link.complete:hover {
    background: #63C063;
}
.nav-tabs.filtertab .nav-link.inprogress {
    border-color: #AD80FF;
}
.nav-tabs.filtertab .nav-link.inprogress.active,
.nav-tabs.filtertab .nav-link.inprogress:hover {
    background: #AD80FF;
}
.connectPrising .data-table td {
    font-size: 16px;
    font-weight: 800;
}
.data-table table .accordian-table td {
    border-top: solid 1px gray;
    border-bottom: solid 1px gray;
    padding: 14px 10px;
    width: auto;
}
.connectPrising .accordion-item table td {
    font-size: 14px;
    font-weight: 800;
}
.data-table table .accordian-table td:first-child {
    border-left: solid 1px gray;
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;
}
.data-table table .accordian-table.action td {
    border-color: rgba(254, 0, 0, 1);
}
.data-table table .accordian-table td {
    border-top: solid 1px gray;
    border-bottom: solid 1px gray;
    padding: 14px 10px;
    width: auto;
}
.data-table table .accordian-table td:first-child {
    border-left: solid 1px gray;
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;
}
.data-table table .accordian-table td:last-child {
    border-right: solid 1px gray;
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
}
.data-table table .accordian-table td img {
    margin-right: 7px;
}
.data-table table .accordian-table.action td {
    border-color: rgba(254, 0, 0, 1);
}
.data-table table .accordian-table.inprogress td {
    border-color: rgba(246, 166, 20, 1);
}
.data-table table .accordian-table.completed td {
    border-color: rgba(99, 192, 99, 1);
}
.data-table table .accordian-table.cancelled td {
    border-color: rgba(210, 43, 43, 1);
}
.connectPrising .data-table td {
    font-size: 16px;
    font-weight: 800;
}
.accordian-table span.timing {
    width: 130px;
}
.accordian-table .table-btn {
    margin: 0;
}
.darkgray-btn {
    background: #DB5555;
    border-color: #DB5555;
}
.darkgray-btn:hover {
    background: transparent;
    color: #DB5555;
}
.connectPrising [aria-expanded="false"] table td.camp-button.dropdown-button img {
    transform: rotate(180deg);
}
.connectPrising .accordion-item table td {
    font-size: 14px;
    font-weight: 800;
}
.this-button {
    width: 289px;
    text-align: right;
}
.star-div {
    text-align: center;
    margin-top: 0;
    display: flex;
    justify-content: center;
}
.star-div img {
    width: 36px;
    height: auto;
}
a.table-btn.closer {
    padding: 0;
    width: auto;
    padding: 0;
    height: auto;
    border: 0;
    box-shadow: none;
    width: 28px;
}
a.table-btn.closer img {
    width: 28px;
    height: auto;
}
.drat {
    width: 155px;
}
.connectPrising table td.timer.green .soclPrice {
    color: rgba(99, 192, 99, 1);
}
.connectPrising table td.green img {
    filter: invert(62%) sepia(62%) saturate(374%) hue-rotate(71deg) brightness(96%) contrast(86%);
}
.connectPrising table td.timer.gray .soclPrice {
    color: #A2A2A2;
}
.connectPrising table td.gray img {
    filter: invert(70%) sepia(0%) saturate(0%) hue-rotate(139deg) brightness(88%) contrast(89%);
}
.stars .star-div img {
    width: 27px;
    height: auto;
}
.campningDiv img.absolutimage {
    position: absolute;
    right: 17px;
    top: 16px;
}
.socialComp>div>div {
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: normal;
    color: #404040;
    padding: 6px 0;
    flex: 0 0 50%;
}
.socialComp>div i {
    width: 22px;
}
.socialComp {
    min-width: 126px;
    flex-wrap: wrap;
    width: auto;
}
input.blueBtn.smallBtn.redsmallBtn.ds,
input.blueBtn.smallBtn.redbigBtn.ds {
    width: auto;
    height: 33px;
    background: #FF0000;
    border-radius: 10px;
    border: solid 1px #FF0000;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    display: flex;
    align-items: center;
    text-align: center;
    padding: 3px 9px;
    color: #FFFFFF;
}
input.blueBtn.smallBtn.submittedReview.ds,
input.blueBtn.smallBtn.greensmallbtn.ds {
    width: auto;
    height: 33px;
    background: #63C063;
    border-radius: 10px;
    border: #63C063;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    display: flex;
    align-items: center;
    text-align: center;
    padding: 3px 9px;
    color: #FFFFFF;
}
body .form-control.textarea {
    padding: 12px 15px;
    height: 125px;
}
.socialInformatin {
    display: flex;
    align-items: center;
}
.soclIn {
    flex: 0 0 60px;
    margin-right: 17px;
}
.data-table table .accordian-table td {
    border-top: solid 1px gray;
    border-bottom: solid 1px gray;
    padding: 14px 10px;
    width: auto;
}
.data-table table {
    border-spacing: 0 8px !important;
    border-collapse: separate;
}
.data-table table .accordian-table td:first-child {
    border-left: solid 1px gray;
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;
}
.data-table table .accordian-table td:last-child {
    border-right: solid 1px gray;
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
}
.data-table table .accordian-table.action td {
    border-color: rgba(254, 0, 0, 1);
}
.data-table table .accordian-table.inprogress td {
    border-color: rgba(246, 166, 20, 1);
}
.data-table table .accordian-table.completed td {
    border-color: rgba(99, 192, 99, 1);
}
.data-table table .accordian-table.cancelled td {
    border-color: rgba(210, 43, 43, 1);
}

.data-table table .accordian-table.inprogress td select {
    border-radius: 7px;
    padding: 3px 7px;
}
.checkwidth .form-control {
    border-radius: 50px;
    width: 150px;
    text-align: center;
}

.checkwidth label {
    margin: 0;
}

.checkwidth {gap: 30px;}

.filtertop.d-flex {
    gap: 30px;
}
.connectPrising.inner .accordion-item .accordion-header button.accordion-button:after {
    display: none;
}
.request-popup .modal-body {
    padding: 0;
}
.request-popup .modal-dialog.default-width {
    max-width: 1000px;
}

.influencer .modal-content {
    background: #fff;
    border: 1px solid #EBEBFF;
    border-radius: 15px;
}
button.btn-close {
    color: #171936;
    opacity: 1;
    font-size: 10px;
    position: absolute;
    right: 8px;
    top: 8px;
    background: transparent;
    width: 30px;
    height: 30px;
    padding: 0;
}
button.btn-close img {
    width: 100%;
    height: 100%;
    position: static;
}
.popup-title {
    font-size: 20px;
    line-height: 24px;
    font-weight: 800;
    letter-spacing: 1px;
    padding: 22px 0 0;
    text-align: center;
}
.popup-title-id {
    letter-spacing: 1px;
    font-size: 14px;
    line-height: 17px;
    font-weight: 800;
    padding: 7px 0 17px;
    text-align: center;
}
.nav-tabs {
    border: 0;
}
ul.nav-tabs.ordertab {
    margin-top: 14px !important;
    margin-bottom: 25px !important;
}
ul.nav-tabs.ordertab li {
    flex: 0 0 50%;
}
.ordertab .nav-item button.nav-link {
    width: 100%;
    background: transparent;
    color: #353B5F;
    font-weight: 700;
    border: oldlace;
    border-radius: 0;
    border-bottom: solid 3px transparent;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 800;
    font-size: 18px;
    line-height: 24px;
    padding: 12px 17px;
}
.nav-tabs.ordertab .nav-link.active {
    border-radius: 0px;
    color: #AD80FF;
    opacity: 1;
    border-bottom: solid 3px #AD80FF;
}
.inside-table.request-content {
    display: flex;
    flex-wrap: wrap;
    gap: 44px 55px;
    padding: 50px 110px 78px;
}
.inside-table-row {
    display: flex;
}
.request-content .inside-table-row {
    flex: 0 0 153px;
    width: 153px;
    position: relative;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 20px;
    border: 1px solid var(--stepper-active, #AD80FF);
    gap: 10px;
    padding: 14px 0 19px;
    margin-top: 20px;
    min-height: 120px;
}
span.type-label {
    position: absolute;
    bottom: 102%;
    left: 0;
    right: 0;
    color: #000;
    text-align: center;
    font-family: 'Mulish';
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 1px;
}
span.type-image {
    width: 35px;
    height: 35px;
}
span.type-content {
    color: #000;
    text-align: center;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 1px;
}
.request-content-data.icon-before {
    display: flex;
    flex-wrap: wrap;
    gap: 34px 65px;
    padding: 29px 67px 46px;
}
.request-content-data .inside-table-row {
    display: block;
    border-radius: 20px;
    border: 1px solid var(--stepper-active, #AD80FF);
    width: 399px;
    padding: 12px;
}
.icon-before .order-titles {
    display: flex;
    justify-content: start;
    align-items: center;
    color: #000;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 1px;
    gap: 19px;
}
.request-content-data.icon-before .order-titles {
    margin: 0;
    text-align: left;
}
.icon-before .order-titles:before {
    content: "";
    width: 16.667px;
    height: 16.667px;
    display: inline-block;
    margin-right: 11px;
    background-image: url(../img/blk-check.svg);
}
.request-content-data.icon-before .order-titles:before {
    min-width: 40px;
    height: 40px;
    background-size: contain;
    background-repeat: no-repeat;
    margin: 0;
}
.request-content-data .order-content {
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 1px;
    color: #000000;
    padding: 0;
    align-items: center;
    text-overflow: ellipsis;
    overflow: hidden;
    display: flex;
    flex: 0 0 100%;
    width: 100%;
    gap: 9px;
    margin-top: 25px;
    margin-bottom: 14px;
}

.request-content-data .order-content:before {
    content: "";
    background: url(../img/icon-arrow-left-black.svg);
    width: 25px;
    height: 25px;
    flex: 0 0 25px;
    background-size: contain;
}

table.table td {
    font-family: sans-serif!important;
}