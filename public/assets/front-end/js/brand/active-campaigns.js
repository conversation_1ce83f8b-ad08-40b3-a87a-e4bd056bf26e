$(document).ready(function() {
    var complaint = complaint || 0;
    if (complaint == 1) {
        $('#complaintconfirm').modal('show');
    }
});

$(document).ready(function() {
    // $('#venueTable').DataTable();
    var review = review || 0;
    if (review == 1) {
        if (user) {
            $('#influencerName').text(user['first_name'] + ' ' + user['last_name']);
        }
        $('#reviewSuccess').modal('show');
    }
});

function openActive(id) {
    $.ajax({
        url: baseUrl + "/read-campaign/" + id,
        method: 'GET',
    }).done(function(data) {
        // What to do here?
    }).fail(function() {});
}

$(document).ready(function() {
    var hash = window.location.hash;
    if (hash != '') {
        $('#myTab button[data-bs-target="#active-campaigns"]').tab('show');
    }
});

function brandReviewSubmittedContent(id, advertising, media, post_type, post_content_type) {
    $.ajax({
        url: baseUrl + "/get-influencer-tasks",
        method: 'GET',
        data: {
            'media': media,
            'type': post_type,
            'advertising': advertising,
            'post_content_type': post_content_type,
            'influencer_request_id': id
        },
    }).done(function(data) {
        $('.tasklists' + id).html(data);
    }).fail(function() {});

    $("#pageLoader").show()
    $.ajax({
        url: baseUrl + "/review-influencer-submission/" + id,
        method: 'GET',
    }).done(function(data) {
        $('.campHistory').html(data);
        $('#reviewRating' + id).modal('show');
        $("#pageLoader").hide()
    }).fail(function() {
        $("#pageLoader").hide()
    });
}

function finishCampaign(id) {
    $("#pageLoader").show()
    $.ajax({
        url: baseUrl + "/finish-campaign/" + id,
        method: 'GET',
    }).done(function(data) {
        $('.campHistory').html(data);
        $('#reviewRating' + id).modal('show');
        $("#pageLoader").hide()
    }).fail(function() {
        $("#pageLoader").hide()
    });
}

$(document).on("click", ".openconfigure", function() {
    var popupid = $(this).attr("data-popup-id")
    $("#configure" + popupid).on('show.bs.modal', function() {});
    $("#configure" + popupid).on('shown.bs.modal', function() {
        $(document).on('change', '.Checkall', function() {
            var i_id = $(this).data('i_id');
            var id = $(this).attr('id').replace('selectUser', '');
            $("#selectInfluencer" + i_id + '_' + id).val(id);

            // current_price calculations removed - field completely deprecated 2025-01-29
            // Price calculations now handled by modern InfluencerPrice system
            if (this.checked) {
                $(this).closest(".checkboxAllCheck").hide();
                $(this).closest(".checkboxAllCheck").prev().show();
            }
        });
        $(document).on('click', '.remove', function() {
            var id = $(this).attr('id').replace('selectUser', '');

            // current_price calculations removed - field completely deprecated 2025-01-29
            // Price calculations now handled by modern InfluencerPrice system

            $("#selectInfluencer" + i_id + '_' + id).val(' ');
            $(this).closest("input[name='selectInfluencer']").val(' ');
            $(this).closest("span").hide();
            $(this).closest("span").next(".checkboxAllCheck").show();
            $(this).closest("span").next(".checkboxAllCheck").find("input").prop("checked", false);
        });
    });
});

// Stripe payment handling
var style = {
    base: {
        color: '#32325d',
        fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
        fontSmoothing: 'antialiased',
        fontSize: '16px',
        '::placeholder': {
            color: '#aab7c4'
        }
    },
    invalid: {
        color: '#fa755a',
        iconColor: '#fa755a'
    }
};

// Initialize Stripe elements when the DOM is fully loaded
$(document).ready(function() {
    if (typeof Stripe !== 'undefined') {
        // Create a Stripe client
        var stripe = Stripe(stripeKey);
        
        // Create an instance of Elements
        var elements = stripe.elements();
        
        // Create an instance of the card Element
        var card = elements.create('card', {
            style: style
        });
        
        // Add an instance of the card Element into the `card-element` <div>
        if (document.getElementById('card-element1')) {
            card.mount('#card-element1');
            
            // Handle real-time validation errors from the card Element
            card.addEventListener('change', function(event) {
                var displayError = document.getElementById('card-errors1');
                if (event.error) {
                    displayError.textContent = event.error.message;
                } else {
                    displayError.textContent = '';
                }
            });
        }
    }
});

// Handle card button click
$(document).on('click', '#card-button1', function() {
    $("#pageLoader").show();
    event.preventDefault();

    if ($('[name="card_id"]').is(':checked')) {
        document.getElementById('paymentForm1').submit();
    } else if ($('#paymentForm1').parsley().isValid()) {
        stripe.createToken(card).then(function(result) {
            var errorElement = document.getElementById('card-errors1');
            if (result.error) {
                // Inform the user if there was an error
                errorElement.textContent = result.error.message;
                $("#overlay").hide();
                $("#pageLoader").hide();
            } else {
                // Send the token to your server
                stripeTokenHandler(result.token);
                errorElement.textContent = '';
            }
        });
    } else {
        $('#paymentForm1').parsley().validate();
    }
});

$('input[name="card_id"]').change(function() {
    if ($(this).is(':checked'))
        $('input[name="card_id"]').not(this).prop('checked', false);
});

// Submit the form with the token ID
function stripeTokenHandler(token) {
    // Insert the token ID into the form so it gets submitted to the server
    var form = document.getElementById('paymentForm1');
    var hiddenInput = document.createElement('input');
    hiddenInput.setAttribute('type', 'hidden');
    hiddenInput.setAttribute('name', 'stripeToken');
    hiddenInput.setAttribute('value', token.id);
    form.appendChild(hiddenInput);

    // Submit the form
    form.submit();
}

function reviewTask(slug, id) {
    var review_count = 0;

    var task1 = $('input[name="task1"]:checked').val();
    var task2 = $('input[name="task2"]:checked').val();
    var task3 = $('input[name="task3"]:checked').val();

    if (task1 != undefined) {
        var review_count = parseInt(review_count) + 1;
    }
    if (task2 != undefined) {
        var review_count = parseInt(review_count) + 1;
    }
    if (task3 != undefined) {
        var review_count = parseInt(review_count) + 1;
    }

    var task = parseInt(task1) + parseInt(task2) + parseInt(task3);
    if (task == 3) {
        $('#review' + id).prop("disabled", false);
        $('#complaint' + id).hide();
        $('#complaint' + id).prop("disabled", true);
    } else {
        if (review_count == 3) {
            $('#review' + id).prop("disabled", false);
            $('#complaint' + id).show();
            $('#complaint' + id).prop("disabled", false);
            // $('#review'+id).prop("disabled", true);
        }
    }
}

function acceptRequest(id) {
    $.ajax({
        url: baseUrl + "/accept-request/" + id,
        method: 'GET',
    }).done(function(data) {
        $(document).ready(function() {
            $('button[data-bs-toggle="tab"]').on('show.bs.tab', function(e) {
                localStorage.setItem('activeTab', $(e.target).attr('data-bs-target'));
            });
            var activeTab = localStorage.getItem('activeTab');
            if (activeTab) {
                $('#myTab button[data-bs-target="' + activeTab + '"]').tab('show');
            }
        });
        window.location.reload();
    }).fail(function() {});
}

function openPayment(name, amt) {
    // var name = $(this).attr("data-name");
    $('#payName').val(name);
    // var amt = $(this).attr("data-amount");
    $('#payAmount').val(amt);
    $('#amtPay').html(amt);
}

function showComplaint(id) {
    $('#complaintPopupConfirm' + id).modal('show');
}

$(document).ready(function() {
    $('.Checkall').change(function() {
        $('.Checkall').val(this.checked);
    });
});

function getFileExtension(imageUrl) {
    return imageUrl.split('.').pop().split(/\#|\?/)[0];
}

function downloadImage(imageUrl) {
    const link = document.createElement("a");
    link.href = imageUrl;
    let extension = getFileExtension(imageUrl);
    link.download = `survey_results.${extension}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function closeComplaintModal(id) {
    $('#complaintPopupConfirm' + id).modal('toggle');
}