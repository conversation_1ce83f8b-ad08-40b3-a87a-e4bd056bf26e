@import url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css");
@import url('https://fonts.googleapis.com/css2?family=Mulish:wght@200;300;400;500;600;700;800;900;1000&family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@font-face {
    font-family: Outfit;
    src: url('../fonts/Outfit-ExtraBold.eot');
    src: url('../fonts/Outfit-ExtraBold.eot?#iefix') format('embedded-opentype'),
        url('../fonts/Outfit-ExtraBold.woff2') format('woff2'),
        url('../fonts/Outfit-ExtraBold.woff') format('woff'),
        url('../fonts/Outfit-ExtraBold.ttf') format('truetype'),
        url('../fonts/Outfit-ExtraBold.svg#Outfit-ExtraBold') format('svg');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: Outfit;
    src: url('../fonts/Outfit-Black.eot');
    src: url('../fonts/Outfit-Black.eot?#iefix') format('embedded-opentype'),
        url('../fonts/Outfit-Black.woff2') format('woff2'),
        url('../fonts/Outfit-Black.woff') format('woff'),
        url('../fonts/Outfit-Black.ttf') format('truetype'),
        url('../fonts/Outfit-Black.svg#Outfit-Black') format('svg');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: Outfit;
    src: url('../fonts/Outfit-Bold.eot');
    src: url('../fonts/Outfit-Bold.eot?#iefix') format('embedded-opentype'),
        url('../fonts/Outfit-Bold.woff2') format('woff2'),
        url('../fonts/Outfit-Bold.woff') format('woff'),
        url('../fonts/Outfit-Bold.ttf') format('truetype'),
        url('../fonts/Outfit-Bold.svg#Outfit-Bold') format('svg');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: Outfit;
    src: url('../fonts/Outfit-SemiBold.eot');
    src: url('../fonts/Outfit-SemiBold.eot?#iefix') format('embedded-opentype'),
        url('../fonts/Outfit-SemiBold.woff2') format('woff2'),
        url('../fonts/Outfit-SemiBold.woff') format('woff'),
        url('../fonts/Outfit-SemiBold.ttf') format('truetype'),
        url('../fonts/Outfit-SemiBold.svg#Outfit-SemiBold') format('svg');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: Outfit;
    src: url('../fonts/Outfit-Thin.eot');
    src: url('../fonts/Outfit-Thin.eot?#iefix') format('embedded-opentype'),
        url('../fonts/Outfit-Thin.woff2') format('woff2'),
        url('../fonts/Outfit-Thin.woff') format('woff'),
        url('../fonts/Outfit-Thin.ttf') format('truetype'),
        url('../fonts/Outfit-Thin.svg#Outfit-Thin') format('svg');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: Outfit;
    src: url('../fonts/Outfit-Light.eot');
    src: url('../fonts/Outfit-Light.eot?#iefix') format('embedded-opentype'),
        url('../fonts/Outfit-Light.woff2') format('woff2'),
        url('../fonts/Outfit-Light.woff') format('woff'),
        url('../fonts/Outfit-Light.ttf') format('truetype'),
        url('../fonts/Outfit-Light.svg#Outfit-Light') format('svg');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: Outfit;
    src: url('../fonts/Outfit-ExtraLight.eot');
    src: url('../fonts/Outfit-ExtraLight.eot?#iefix') format('embedded-opentype'),
        url('../fonts/Outfit-ExtraLight.woff2') format('woff2'),
        url('../fonts/Outfit-ExtraLight.woff') format('woff'),
        url('../fonts/Outfit-ExtraLight.ttf') format('truetype'),
        url('../fonts/Outfit-ExtraLight.svg#Outfit-ExtraLight') format('svg');
    font-weight: 200;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: Outfit;
    src: url('../fonts/Outfit-Regular.eot');
    src: url('../fonts/Outfit-Regular.eot?#iefix') format('embedded-opentype'),
        url('../fonts/Outfit-Regular.woff2') format('woff2'),
        url('../fonts/Outfit-Regular.woff') format('woff'),
        url('../fonts/Outfit-Regular.ttf') format('truetype'),
        url('../fonts/Outfit-Regular.svg#Outfit-Regular') format('svg');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: Outfit;
    src: url('../fonts/Outfit-Medium.eot');
    src: url('../fonts/Outfit-Medium.eot?#iefix') format('embedded-opentype'),
        url('../fonts/Outfit-Medium.woff2') format('woff2'),
        url('../fonts/Outfit-Medium.woff') format('woff'),
        url('../fonts/Outfit-Medium.ttf') format('truetype'),
        url('../fonts/Outfit-Medium.svg#Outfit-Medium') format('svg');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}
a {
    text-decoration: none;
}
body {
    font-family: 'Mulish', sans-serif;
    background: #F2F2FC !important;
}
.bgLightBlue {
    background-color: #B6EBFB;
}
.header-logo span {
    font-size: 32px;
    text-decoration: none;
}
.header-logo a {
    text-decoration: none;
    color: #171936;
    font-weight: 600;
}
ul.h-menu {
    padding: 0;
    margin: 0;
    list-style: none;
    text-decoration: none;
    display: flex;
    align-items: center;
}
ul.h-menu a {
    text-decoration: none;
    color: #877F7F;
    position: relative;
    font-weight: 400;
    padding: 5px 9px;
    font-family: Outfit;
    font-size: 14px;
}
header.site-header {
    padding: 17px 0;
    width: 100%;
    position: relative;
    z-index: 1;
    background-color: #fff;
}
.color-white {
    color: #fff;
}
.color-black {
    color: #232323;
}
.color-red {
    color: #FF0000;
}
.color-blue {
    color: #00BFFF;
}
.color-green {
    color: #63C063;
}
.bg-lightBlue {
    background: #D4E7FF;
    border-color: #D4E7FF !important;
}
.bg-lightBlue:hover {
    color: #D4E7FF !important;
    background: transparent !important;
}
.bg-lightBlue.color-black:hover {
    color: #232323;
}
.bg-red {
    background: #FF0000;
    border-color: #FF0000;
}
.bg-red:hover {
    color: #FF0000 !important;
    background: transparent !important;
}
.bg-green {
    color: #FFFFFF;
    background: #63C063;
    border-color: solid 1px #63C063;
}
.bg-green:hover {
    color: #63C063 !important;
    background: transparent !important;
}
.bg-blue {
    background: #00BFFF;
    border-color: #00BFFF;
}
.bg-blue:hover {
    color: #00BFFF !important;
    background: transparent !important;
}
.popupbtn {
    padding: 13px 27px;
    border-radius: 10px;
    border: solid 1px;
    cursor: pointer;
    text-decoration: none;
    transition: all 0.3s;
}
.bgDarkBlue {
    background: #AD80FF;
}
ul.f-menus {
    padding: 0;
    margin: 0;
    list-style: none;
    display: flex;
    align-items: center;
    justify-content: center;
}
ul.f-menus li a {
    color: #171936;
    text-decoration: none;
    margin: 12px 16px;
    display: inline-block;
    font-size: 14px;
    color: #000000;
    text-align: center;
    font-weight: 700;
}
.copyright {
    color: #000;
    font-family: Outfit;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 142.857% */
    letter-spacing: 0.7px;
    max-width: 1254px;
    margin: 0 auto;
}
.site-footer {
    padding: 33px 0 31px;
    margin-top: 0;
    border-top: solid 6px #ED0000 !important;
}
.active .dropdown-item:focus,
.active .dropdown-item:hover {
    color: #1e2125 !important;
    background-color: #e9ecef !important;
}
.floating-input:focus~label.floatLabel,
.floating-input:not(:placeholder-shown)~label.floatLabel {
    color: #171936;
    font-weight: 600;
}
.floating-select:focus~label.floatLabel,
.floating-select:not([value=""]):valid~label.floatLabel {
    top: -18px;
    font-size: 14px;
    color: #5264AE;
}
.floating-input,
.floating-select {
    font-size: 14px;
    padding: 4px 4px;
    display: block;
    width: 100%;
    height: 30px;
    background-color: transparent;
    border: none;
    border-bottom: 1px solid #757575;
}
.floating-input:focus,
.floating-select:focus {
    outline: none;
    border-bottom: 2px solid #5264AE;
}
label.floatLabel {
    color: #171936;
    font-size: 15px;
    font-weight: normal;
    left: 0;
    top: -25px;
    transition: 0.2s ease all;
    -moz-transition: 0.2s ease all;
    -webkit-transition: 0.2s ease all;
}
.floating-label {
    display: inline-block;
    width: 100%;
    position:
        relative;
    margin-bottom: 20px;
    margin-top: 0;
}
body .form-control {
    background: #dcdada;
    border-radius: 10px;
    border: 0;
    height: 38px;
    font-size: 14px;
    padding: 0 12px 0 15px;
    color: #1c2053;
    appearance: auto;
    background: #F9F9FC;
    border: 1px solid #EBEBFF;
}
.form-group {
    margin-bottom: 16px;
}
body i~.form-control {
    padding-left: 38px;
}
.floating-label i {
    position: absolute;
    font-size: 20px;
    left: 10px;
    top: 12px;
}
.passView.toggle-password {
    position: absolute;
    right: 13px;
    top: 16px;
    background: transparent;
    padding: 0;
    margin: 0;
    border: 0;
    font-size: 8px;
    cursor: pointer;
}
.passView.toggle-password i {
    position: static;
    font-size: 16px;
    color: deepskyblue;
    cursor: pointer;
}
.logForm {
    max-width: unset;
    margin: 0 auto;
    padding: 0;
    border-radius: 15px;
    background: transparent;
    position: relative;
}
h1.pageHeading {
    text-align: center;
    font-size: 31px;
    font-weight: 700;
    margin-bottom: 23px;
    color: #171936;
}
.errorMessage {
    font-weight: 500;
    color: #ffffff;
    text-align: center;
    position: fixed;
    top: 8px;
    margin: 0 0 6px;
    padding: 15px 15px 15px 50px;
    width: auto;
    right: 8px;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAHOSURBVEhLrZa/SgNBEMZzh0WKCClSCKaIYOED+AAKeQQLG8HWztLCImBrYadgIdY+gIKNYkBFSwu7CAoqCgkkoGBI/E28PdbLZmeDLgzZzcx83/zZ2SSXC1j9fr+I1Hq93g2yxH4iwM1vkoBWAdxCmpzTxfkN2RcyZNaHFIkSo10+8kgxkXIURV5HGxTmFuc75B2RfQkpxHG8aAgaAFa0tAHqYFfQ7Iwe2yhODk8+J4C7yAoRTWI3w/4klGRgR4lO7Rpn9+gvMyWp+uxFh8+H+ARlgN1nJuJuQAYvNkEnwGFck18Er4q3egEc/oO+mhLdKgRyhdNFiacC0rlOCbhNVz4H9FnAYgDBvU3QIioZlJFLJtsoHYRDfiZoUyIxqCtRpVlANq0EU4dApjrtgezPFad5S19Wgjkc0hNVnuF4HjVA6C7QrSIbylB+oZe3aHgBsqlNqKYH48jXyJKMuAbiyVJ8KzaB3eRc0pg9VwQ4niFryI68qiOi3AbjwdsfnAtk0bCjTLJKr6mrD9g8iq/S/B81hguOMlQTnVyG40wAcjnmgsCNESDrjme7wfftP4P7SP4N3CJZdvzoNyGq2c/HWOXJGsvVg+RA/k2MC/wN6I2YA2Pt8GkAAAAASUVORK5CYII=) !important;
    height: 50px;
    background-color: #BD362F;
    border-radius: 3px 3px 3px 3px;
    background-position: 15px center;
    background-repeat: no-repeat;
    -moz-box-shadow: 0 0 12px #999999;
    -webkit-box-shadow: 0 0 12px #999999;
    box-shadow: 0 0 12px #999999;
    z-index: 1;
}
.forGotLink {
    display: block;
    color: #212427;
    text-align: center;
    font-family: Mulish;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px; /* 171.429% */
    width: auto;
}
.form-group.form-check.custom-check {
    width: 100%;
    display: inline-block;
    padding: 0;
    position: relative;
    margin-bottom: 0;
    flex: 1;
}
.forGotLink a {
    text-decoration: none;
    display: inline-block;
    color: #1C4532;
    font-family: Inter;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 150%; /* 24px */
    text-decoration-line: underline;
}
.form-group.form-check.custom-check label.form-check-label {
    font-size: 15px;
    font-weight: 600;
    position: relative;
    padding: 0 0 0 24px;
    left: auto;
    top: auto;
    right: auto;
    bottom: auto;
    width: auto;
    color: #718096;
    font-family: Inter;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    margin-bottom: 0;
}
.form-group.form-check.custom-check input#exampleCheck1 {
    position: absolute;
    left: 24px;
    top: 1px;
    opacity: 0;
    cursor: pointer;
}
.form-group.form-check.custom-check input:checked~label:before {
    background-image: url(../images/icons/icon-check-white.svg);
    position: absolute;
    background-repeat: no-repeat;
    background-color: #AD80FF;
    background-position: center;
    border-color: #AD80FF;
}
.orText {
    text-align: center;
    font-size: 13px;
    color: #fff;
    font-weight: 800;
    width: 420px;
    margin: 4vh auto;
    position: relative;
}
.orText:before {
    content: "";
    position: absolute;
    top: 1px;
    margin: auto;
    bottom: 0;
    background: #A0AEC0;
    border-radius: 50%;
    z-index: 0;
    width: calc(50% - 20px);
    height: 1px;
    left: 0;
}
.orText:after {
    content: "";
    position: absolute;
    top: 1px;
    margin: auto;
    bottom: 0;
    background: #A0AEC0;
    border-radius: 50%;
    z-index: 0;
    width: calc(50% - 20px);
    height: 1px;
    right: 0;
}
.socialConect {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
}
.socialConect a {
    display: flex;
    width: 49%;
    flex: 0 0 420px;
    text-align: center;
    background: lightgray;
    padding: 8px 0;
    margin: 6px auto;
    border-radius: 5px;
    color: #fff;
    text-decoration: none;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s;
}
.socialConect a:hover {
    color: #fff;
}
.socialConect a.youtube {
    background: #ff000094;
}
.socialConect a i {
    width: 30px;
    height: 30px;
    font-size: 16px;
    background: #ffffffb0;
    display: flex;
    color: #FF0000;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    line-height: 26px;
    margin-left: 6px;
}
.socialConect a.facebook {
    background: #1877f2a1;
}
.socialConect a.facebook i {
    color: #1877f2;
}
.socialConect a.twitter {
    background: #1a8cd8a3;
}
.socialConect a.twitter i {
    color: #1a8cd8;
}
.socialConect a.youtube:hover {
    background: #ff0000;
}
.socialConect a.facebook:hover {
    background: #1877f2;
}
.socialConect a.twitter:hover {
    background: #1a8cd8;
}
.lbBtmTxt {
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    margin: 13px 0 0;
}
.lbBtmTxt a {
    color: rgb(173, 128, 255);
}
div.userType {
    display: inline-block;
    width: 100%;
    border: solid 1px #f0f0f0;
    border-radius: 15px;
    background: #dcdada;
    position: relative;
    margin-bottom: 19px;
}
div.userType .customRadio {
    float: left;
    display: inline-block;
    width: 50%;
    text-align: center;
    cursor: pointer;
    position: relative;
}
.customRadio input~label {
    position: relative;
    left: 0;
    margin: 0;
    right: 0;
    bottom: 0;
    top: 0;
    width: 100%;
    padding: 13px 7px;
    color: #171936;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
}
.customRadio input {
    position: absolute;
    width: 100%;
    height: 100%;
    cursor: pointer;
    opacity: 0;
}
.customRadio input:checked~label,
.customRadio label:hover {
    background: deepskyblue;
    color: #fff;
}
header button.user-btn.dropdown-toggle {
    display: flex;
    align-items: center;
    border: 0;
    background: transparent;
    padding: 0;
    font-weight: 600;
    text-transform: capitalize;
    font-size: 14px;
}
.steps {
    border-radius: 20px;
    border-radius: 10px;
    background-color: #fdfefe;
    padding: 12px 14px;
    margin: 70px 0;
    background: #fff;
}
.dashboardsidebar ul {
    padding: 0;
    margin: 0;
    list-style: none;
    margin-top: 12px;
    max-height: calc(100vh - 181px);
}
.dashboardsidebar ul a {
    font-size: 16px;
    padding: 15px 27px;
    list-style: none !important;
    text-decoration: none;
    color: #000;
    margin: 0 10px;
    display: flex;
    align-items: center;
    width: calc(100% - 20px);
    position: relative;
    /* font-weight: 600; */
    line-height: 25px;
    transition: all 0.3s;
    border-radius: 25px;
}
.dashboardsidebar ul a.active,
.dashboardsidebar ul li a:hover {
    color: #AD80FF;
    /* background: #AD80FF; */
    /* box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25); */
}
body .smalSpace .form-control {
    padding-left: 15px;
    background: #F9F9FC;
}
.imageOuter {
    width: 150px;
    height: 150px;
    overflow: hidden;
    border-radius: 50%;
    margin: auto;
    background-image: url(../images/userImage.jpg);
    background-size: cover;
    border: solid 1px #80808069;
    position: relative;
}
.imageOuter img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.op-img label.button {
    position: static;
    pointer-events: all;
    width: 128px;
    font-size: 16px;
    color: #fff;
    font-weight: normal;
    background: #AD80FF;
    border: solid 2px #AD80FF;
    transition: all 0.3s;
    text-align: center;
    line-height: normal;
    height: auto;
    float: none;
    border-radius: 5px;
    display: inline-block;
    left: auto;
    right: auto;
    padding: 5px 0 8px;
    margin: 16px auto 0;
    cursor: pointer;
}
.op-img label.button:hover,
.socialBtnConnect .soclBtn:hover,
input.blueBtn.newpost:hover {
    background-color: transparent;
    color: deepskyblue;
    cursor: pointer;
}
.op-img {
    text-align: center;
    display: inline-block;
    width: auto;
    margin-bottom: 20px;
    background: #fff;
    border: 1px solid #aaa;
    border-radius: 5px;
    padding: 10px 15px;
}
.op-img input#profile_pic {
    position: absolute;
    left: -9999px;
}
.img-usr {
    overflow: hidden;
    margin: 0 8px 0 0;
    border-radius: 50%;
    position: static;
    width: 40px;
    height: 40px;
}
.img-usr img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    float: right;
}
.dropdown-menu.show {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 3px 6px rgba(0, 0, 0, .1607843137254902) !important;
    width: auto;
    padding: 0;
    min-width: 199px;
}
#hsj-menu li .dropdown-menu.show a {
    font-size: 20px;
    padding: 12px 22px;
    font-weight: 600;
    line-height: normal;
    height: auto;
    color: #171936 !important;
    text-decoration: none;
}
.header-logo {
    height: 45px;
    transition: all 0.3s;
}
.copyright a {
    color: #fff;
}
h1.dashHeading {
    font-weight: 800;
    font-size: 32px;
    line-height: 45px;
    margin-bottom: 20px;
    color: #000000;
    background: #FFFFFF;
    border-radius: 15px;
    letter-spacing: 5px;
    text-align: center;
    text-transform: uppercase;
    padding: 18px 17px;
    position: relative;
    overflow: hidden;
}
h1.dashHeading:before {
    content: "";
    /* background: linear-gradient(93.63deg, #AD80FF 2.7%, #F86988 48.81%, #AD80FF 100%); */
    background: #AD80FF;
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 0;
}
h1.dashHeading:after {
    content: "";
    position: absolute;
    left: 1px;
    top: 2px;
    width: calc(100% - 2px);
    height: calc(100% - 4px);
    background: #fff;
    border-radius: 14px;
    z-index: 0;
}
h1.dashHeading span {
    position: relative;
    z-index: 1;
}
ul.parsley-errors-list {
    padding: 0;
    margin: 0;
    font-size: 12px;
    color: red;
    list-style: none;
    position: absolute;
    bottom: -18px;
    line-height: 14px;
}
input.hidden-textbox.form-control.text-start {
    padding-left: 11px;
}
ul.f-menus li a.active {
    color: #fff;
}
button.close {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    background: transparent;
    border: 0;
    width: 38px;
}
.wizardPopup {
    position: relative;
    top: 0;
    left: 0;
    width: 900px;
    margin: 30px auto;
    ;
    right: 0;
    bottom: 0;
    background: #fff;
    border-radius: 15px;
    padding: 30px 30px;
    height: auto;
}
.wizardPopupOuter:before {
    content: "";
    ;
    background: rgb(23 25 54 / 67%);
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    backdrop-filter: saturate(180%) blur(3px);
    background: rgba(black, .1);
}
.wizardPopupOuter {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    overflow: auto;
    z-index: 999;
}
.wizardForm {
    max-width: 700px;
    margin: 0 auto;
}
body.hidescroll {
    overflow: hidden;
}
.disconnectedToSocialimg.iconBlack {
    display: block;
}
.disconnectedToSocial img.iconColored {
    display: none;
}
.socialCnt {
    width: 60px;
    height: 60px;
    border-radius: 0;
    margin-right: 53px;
    padding: 0;
    background: #ffffff85;
}
.socialCnt img {
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 0;

}
.blur .socialCnt img{
    filter: grayscale(133%) blur(2px);
    pointer-events: none;
    opacity: 0.3;
}
.socialUserImage {
    width: 60px;
    height: 60px;
    overflow: hidden;
    flex: 0 0 60px;
    border-radius: 15px;
    margin-right: auto;
}
.socialUserImage img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.socialBtnConnect {
    width: auto;
    text-align: center;
    margin-right: 0;
}
.socialUserDetailName {
    font-size: 19px;
    font-weight: 600;
    line-height: normal;
}
.socialUserDetailName a{
    color: #000;
}
.socialUserDetailNumber {
    font-size: 13px;
    line-height: normal;
    color: #000;
    padding-top: 5px;
}
.socialUserDetail {
    padding: 0 0 0 14px;
}
.connectWithInner.d-flex.align-items-center.disconnectedToSocial.blur, .connectWithInner {
    padding: 23px 26px;
    margin: 20px 0;
    position: relative;
    border-radius: 15px;
    border: 1px solid #f7f7f7;
    background: linear-gradient(88deg, #FBFBFC 0%, rgba(219, 221, 232, 0.00) 100%);
}
.connectWithInner:last-child {
    border: 0;
}
.cusomRadio {
    display: flex;
    align-items: center;
}
.cusomRadio .form-check {
    padding-right: 15px;
}
label {
    font-size: 13px;
    color: #171936;
    font-weight: 600;
    margin: 0 0 7px;
    width: 100%;
    text-align: left;
}
hr {
    color: #eaeaea;
    opacity: 1;
    margin: 19px 0 !important;
    display: inline-block;
}
.cusomRadio label {
    font-size: 16px;
    margin: 0;
    line-height: normal;
    display: flex;
}
.iconsTable span {
    width: 40px;
    height: 40px;
    display: inline-block;
    margin: 0 5px;
    position: relative;
    border-radius: 50%;
    transition: all 0.3s;
}
.iconsTable span img {
    width: 100%;
    height: 100%;
}
.iconsTable {
    display: flex;
}
.connectedActive img.iconColored {
    display: block;
}
.connectedActive img.iconBlack {
    display: none;
}
.connectedNotActiveimg.iconColored {
    display: block;
}
.connectedNotActive img.iconBlack {
    display: none;
}
.NotconnectedActive img.iconColored {
    display: none;
}
.NotconnectedActive img.iconBlack {
    display: block;
}
.iconsTable span.connectedActive input:checked~span {
    position: absolute;
    background-image: url(../images/checkmark-png-5.png);
    font-size: 34px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}
.iconsTable span input {
    position: absolute;
    width: 100%;
    height: 100%;
    cursor: pointer;
    opacity: 0;
}
.iconsTable span>span {
    position: absolute;
    left: -3px;
    top: 17px;
    pointer-events: none;
    margin: 0;
    width: 27px;
    height: 27px;
}
.connectPrising .form-check label.form-check-label {
    /* white-space: nowrap; */
    /* margin: 0; */
    /* display: flex; */
    /* align-items: center; */
    /* padding-left: 0; */
    /* margin-bottom: 16px; */
}
.connectPrising table td {
    vertical-align: middle;
    padding: 0 6px;
    font-style: normal;
    font-weight: 700;
    font-size: 14px;
    letter-spacing: 1px;
    color: #000000;
    width: 18%;
}
.borderTd {
    border-bottom: solid 1px #eaeaea;
    width: 100%;
    display: inline-block;
}
.newpost {
    border: solid 2px;
    border-radius: 5px;
    width: 144px;
    line-height: 37px;
    font-weight: 700;
    float: left;
    display: inline-block;
    height: 46px;
    margin-top: 16px;
    text-align: center;
}
.nhu {
    display: flex;
    width: 100%;
    padding: 26px 0 12px;
    justify-content: center;
}
.blueBtn.newpost {
    background: #1DF3F1;
    border: solid 2px #1DF3F1;
    color: #fff;
    margin: 0 18px 0 0;
}
.blueBtn.newpost[disabled] {
    background-color: #d8d8d8;
    border-color: #d8d8d8;
}
button.btn-close {
    color: #171936;
    opacity: 1;
    font-size: 10px;
    position: absolute;
    right: 8px;
    top: 8px;
    background: transparent;
    width: 30px;
    height: 30px;
    padding: 0;
}
button.btn-close img {
    width: 100%;
    height: 100%;
    position: static;
}
.modal-body .customRadio input~label:before {
    content: "";
    width: 20px;
    font-weight: 900;
    height: 20px;
    border: solid 2px #000;
    display: inline-block;
    border-radius: 3px;
    position: absolute;
    left: 20px;
    top: 0;
    bottom: 0;
    margin: auto;
    text-align: center;
}
.modal-body .customRadio input~label {
    padding-left: 60px;
    width: 100%;
    border: solid 1px #f0f0f0;
    text-align: left;
    border-radius: 7px;
    background: #dfdfe3;
}
.modal div.userType {
    border-radius: 0;
    background: transparent;
    margin: 0 -8px;
}
.modal-body div.userType .customRadio {
    margin: 8px;
    width: calc(50% - 16px);
}
.modal-body .customRadio input:checked~label,
.modal-body .customRadio input~label:hover {
    background: deepskyblue;
}
.modal-body .customRadio input:checked~label:before {
    border-color: #fff;
    content: "\f00c";
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    font-size: 15px;
    line-height: 19px;
}
.modal.fade.show {
    background: rgb(23 25 54 / 40%);
}
.connectWith.editProfile {
    border: 0;
    display: flex;
    flex-direction: column;
    background: #dcdada;
    border-radius: 5px;
    margin-top: -14px;
}
.connectWith.editProfile .connectWithInner {
    padding: 11px 0;
    clear: both;
    align-items: center;
    justify-content: center;
}
.connectWith.editProfile .connectWithInner .socialCnt {
    width: 60px;
    height: 60px;
    flex: 0 0 60px;
}
.connectWith.editProfile .connectWithInner .socialUserImage {
    width: 60px;
    height: 60px;
}
a.verifyAcNotification {
    display: inline-block;
    width: 134px;
    text-align: center;
    background: #ffba00;
    padding: 7px;
    color: #fff;
    text-decoration: none;
    border-radius: 5px;
    font-size: 14px;
    padding: 4px 7px;
    margin-left: auto;
    margin-right: 0;
}
a.verifyAcNotification:hover {
    background: #bd8d0c;
    color: #fff;
}
.blogBox {
    box-shadow: 0 10px 38px rgb(24 28 60 / 5%);
    border-radius: 30px;
    width: 100%;
    height: auto;
    float: left;
    display: inline-block;
    margin: 0 0 30px;
    padding: 10px;
    box-shadow: 0.6em 0.6em 1.2em #d2dce9, -0.5em -0.5em 1em #ffffff;
}
.blogBoxImg {
    height: 258px;
    background: #d8d8d8;
    border-radius: 20px;
    overflow: hidden;
}
.blogBoxContent {
    padding: 8px 25px;
}
.blogBoxContent h3 a {
    color: #191d48;
    font-size: 18px;
    padding: 9px 0 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    text-decoration: none;
    font-weight: 700;
}
.blogBoxBottom {
    display: flex;
    font-size: 12px;
    color: #705ac4;
    padding: 14px 0 0;
}
.blogDescription {
    color: #171936;
    font-size: 13px;
    padding: 9px 0 0;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 87px;
}
.blogBoxImg img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.custom-pagination {
    text-align: center;
}
.custom-pagination ul {
    display: flex;
    align-items: center;
    justify-content: center;
}
.custom-pagination ul li {
    min-width: 44px;
    text-align: center;
    box-shadow: 0 10px 38px rgb(24 28 60 / 5%);
    height: 44px;
    line-height: 34px;
    background: #fff;
    box-shadow: 0 10px 38px rgb(21 20 51 / 9%);
    margin: 0 6px;
}
.custom-pagination ul li a {
    text-decoration: none;
    color: #000;
    font-size: 18px;
    font-weight: 400;
    width: 100%;
    height: 100%;
    display: inline-block;
    padding: 5px;
    border-radius: 8px;
}
.custom-pagination ul li.active a,
.custom-pagination ul li a:hover {
    background: #1DF3F1;
    color: #fff;
}
.blogview a {
    color: #1DF3F1;
    font-weight: 500;
    font-size: 16px;
    text-decoration: none;
}
.blog-detail-layout .blog-image {
    height: 469px;
    border-radius: 22px;
    overflow: hidden;
    margin-top: 60px;
    clear: both;
}
.blog-detail-layout .blog-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.blog-individual h1 {
    color: #191d48;
    font-size: 28px;
    padding: 22px 0 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    text-decoration: none;
    font-weight: 700;
}
.blog-individual .blogBoxBottom {
    font-size: 16px;
    margin-bottom: 16px;
}
.blog-individual .blog-text.blogBox {
    box-shadow: none;
}
.steps .accordion-body {
    color: #191d48;
    font-size: 16px;
    padding: 9px 0 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    text-decoration: none;
    padding: 24px 13px;
    height: auto;
    display: inline-block;
    width: 100%;
    float: left;
}
button.accordion-button {
    color: #191d48;
    font-size: 16px;
    padding: 20px 23px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    text-decoration: none;
    font-weight: 700;
}
.accordion-item .accordion-header {
    background-color: #fff !important;
    border: 0;
}
.wrapper {
    position: static;
    top: 50%;
    left: 50%;
    display: flex;
    flex-direction: row-reverse;
}
.wrapper input {
    display: none;
}
.wrapper label {
    display: block;
    cursor: pointer;
    width: 150px;
    margin: -11px 0 7px;
}
.wrapper label:before {
    content: '\f005';
    position: relative;
    display: block;
    font-size: 21px;
    color: black;
    font-family: "Font Awesome 6 Free";
    font-weight: 400;
}
.wrapper label:after {
    content: '\f005';
    position: absolute;
    display: block;
    font-size: 21px;
    color: #1DF3F1;
    top: -11px;
    opacity: 0;
    transition: .6s;
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}
.wrapper label:hover:after,
.wrapper label:hover~label:after,
.wrapper input:checked~label:after {
    opacity: 1;
}
.steps.filter {
    padding: 12px 8px;
}
.filterHeading {
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 9px;
}
.steps.filter select.form-control {
    padding-left: 9px;
}
.slider {
    -webkit-appearance: none;
    width: 100%;
    height: 6px;
    background: #e3e3e3;
    outline: none;
    opacity: 1;
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
}
.slider:hover {
    opacity: 1;
}
.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 12px;
    height: 12px;
    background: #1DF3F1;
    cursor: pointer;
    border-radius: 50%;
}
.slider::-moz-range-thumb {
    width: 12px;
    height: 12px;
    background: #1DF3F1;
    cursor: pointer;
    border-radius: 50%;
}
button.applyFilter {
    width: calc(50% - 5px);
    margin-left: 0;
    margin-right: 0;
    padding: 0;
    font-size: 12px !important;
    margin-top: 20px;
}
button.applyFilter:hover {
    background-color: transparent;
    color: #1DF3F1;
}
.userDetails {
    border-radius: 10px;
    padding: 10px;
    box-shadow: 0 10px 38px 0 rgb(0 17 81 / 20%);
    background-color: #fff;
    margin: 15px 0;
    overflow: hidden;
    position: relative;
    transition: all 0.2s;
    box-shadow: 0.6em 0.6em 1.2em #d2dce9, -0.5em -0.5em 1em #ffffff;
}
.small-icon {
    width: 45px;
    height: 45px;
    display: inline-block;
}
.small-icon img {
    width: 100%;
    height: 100%;
}
.orcCont span.handelpletform {
    margin-left: auto;
    margin-top: -9px;
}
.userDetails2 {
    display: flex;
    align-items: center;
    margin: 9px 0 23px;
}
.userDetailImage {
    width: 40px;
    height: 40px;
    margin-right: 10px;
    flex: 0 0 40px;
    border-radius: 50%;
    overflow: hidden;
    border: solid 1px #80808054;
}
span.infoName {
    display: inline-block;
    width: 100%;
    font-size: 14px;
    font-weight: 600;
}
.userDetailImage img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.targetHeading {
    font-weight: 600;
    font-size: 13px;
    flex: 0 0 100%;
}
.userDetails3 {
    display: flex;
    flex-wrap: wrap;
}
.targetgroup {
    flex: 0 0 50%;
    padding: 3px 0;
    font-size: 12px;
}
.userDetails4 {
    margin-top: 10px;
}
a.sselectBtn {
    border: solid 2px #1DF3F1;
    background: #1DF3F1;
    text-decoration: none;
    color: #fff;
    width: 86px;
    text-align: center;
    border-radius: 5px;
    padding: 5px 10px;
    margin-left: 15px0;
    font-size: 15px;
    display: inline-block;
    transition: all 0.3s;
}
a.sselectBtn:hover {
    border: solid 2px #1DF3F1;
    color: #1DF3F1;
    background: transparent;
}
.selectedUserOuter .userDetails4 .btn-group {
    margin: 0 0 0 0;
    line-height: 23px;
    align-items: center;
    color: green;
    font-weight: 600;
    text-transform: capitalize;
}
.selectedUserOuter .userDetails4 .btn-group i {
    margin-right: 7px;
}
.selectedUserOuter span.handelpletform {
    margin-left: auto;
    flex: 0 0 30px;
}
.selectedUserOuter a.btn.btn-danger {
    padding: 4px 8px;
    font-size: 14px;
    line-height: normal;
    font-weight: 500;
    height: auto;
    margin-left: auto;
    color: #fff;
    border-radius: 10px;
    font-size: 18px;
    line-height: normal;
    padding: 7px 18px;
    background: rgb(237 0 0 / 80%);
}
.selectedUserOuter a.btn.btn-danger:hover {
    background-color: transparent;
    color: rgb(237 0 0 / 80%);
    border-color: rgb(237 0 0 / 80%);
}
.selectedUserOuter .userDetails {
    margin-bottom: 0;
    box-shadow: 0 5px 10px 0 rgb(0 8 37 / 42%);
    margin: 15px;
}
.influncerUpdate {
    padding: 21px 15px 15px;
    font-weight: 600;
}
.markeplaceRequest .targetgroup.forTag {
    height: auto;
}
.markeplaceRequest {
    border-radius: 25px;
    overflow: hidden;
}
input.btn.btn-primary.reqBtn {
    text-transform: capitalize;
    background: #3CC23C;
    padding: 0 10px;
    width: 130px;
    height: 37px;
    border-radius: 15px;
    border: 1px solid #3CC23C;
    font-style: normal;
    font-weight: 800;
    font-size: 13px;
    text-align: center;
    color: #FFFFFF;
    transition: all 0.3s;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    display: inline-flex;
    margin: 0;
    letter-spacing: 0;
    white-space: normal;
    line-height: 34px;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    width: 100%;
}
.selectedUser {
    padding-bottom: 134px;
    max-height: calc(100vh - 188px);
    overflow: auto;
}
.wizardForm ul {
    margin: 0;
    padding: 0;
    list-style: none;
    font-weight: 700;
}
div.userType .customRadio:first-child {
    border-right: solid 1px #fff;
}
.image img {
    width: 90%;
    height: 100%;
    object-fit: contain;
}
.image {
    text-align: center;
    height: calc(100vh - 60px);
}
section.home-banner {
    padding: 30px 0;
    background: #aa1485;
    background: rgb(29, 228, 243);
    background: linear-gradient(0deg, rgba(29, 228, 243, 1) 0%, rgba(170, 20, 133, 1) 100%);
}
.banner-title {
    color: #fff;
    padding-right: 160px;
}
.banner-title h3.title {
    font-weight: bold;
    font-size: 37px;
}
.banner-title p {
    font-size: 16px;
    line-height: 24px;
    margin: 0;
    padding: 19px 0 0;
}
span.showUser br {
    display: none;
}
span.serIcon {
    font-size: 3rem;
    color: #1DF3F1;
    padding: 0;
    box-shadow: 0.6em 0.6em 1.2em #d2dce9, -0.5em -0.5em 1em #ffffff;
    border-radius: 50%;
    transition: all 0.3s;
    width: 96px;
    height: 96px;
    display: inline-block;
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 0 0 96px;
}
span.serName {
    display: flex;
    align-items: center;
    font-size: 19px;
    padding: 0 0 0 31px;
    width: 166px;
}
.servicesSection {
    padding: 70px 23px;
    width: 1000px;
    margin: 0 auto;
}
.aboutSection {
    background: #fff;
    padding: 70px 0;
}
.subTitle {
    font-size: 12px;
    color: #aa1485;
    font-weight: 600;
    letter-spacing: 1px;
}
.sectionTitle {
    font-size: 39px;
    font-weight: 800;
    padding: 0 0 5px;
}
.sectiontext {
    line-height: 28px;
    padding-right: 46px;
}
.influncers {
    padding: 70px 0;
}
.card-wrapper {
    overflow: visible;
    padding: 3rem 2rem;
    border-radius: 3rem;
    box-shadow: 0.6em 0.6em 1.2em #d2dce9, -0.5em -0.5em 1em #ffffff;
    margin-bottom: 4rem;
    text-align: center;
    margin-top: 3rem;
}
.card-wrapper img {
    width: 154px;
    height: 154px;
    border-radius: 50%;
    object-fit: cover;
    margin: auto;
    margin-bottom: 2rem;
}
.card-box h4.card-title strong {
    font-size: 25px;
    font-weight: 700;
}
.card-box h5.card-text strong {
    font-size: 15px;
    font-weight: normal;
}
.targetgroups {
    width: 100%;
}
.-layout .blogBox,
.-layout .userDetails {
    margin-top: 4rem;
}
.counterSection {
    padding: 70px 0;
    background-image: url(../images/bg-image.jpg);
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    position: relative;
}
.counterSection::before {
    content: "";
    width: 100%;
    height: 100%;
    background-color: #1df3f1db;
    position: absolute;
    left: 0;
    top: 0;
}
.counterSection .sectiontext {
    padding: 0;
    max-width: 68%;
    margin: 0 auto;
}
div#counter {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #aa1485cc;
    max-width: 841px;
    margin: 68px auto 0;
    border-radius: 15px;
}
div#counter .conterAll {
    flex: 0 0 210px;
    padding: 0 13px;
    text-align: center;
    padding: 10px;
    border-radius: 15px;
    margin: 0;
    padding: 22px 0;
    position: relative;
}
.counter-value {
    font-weight: 900;
    font-size: 39px;
    color: #fff;
    line-height: normal;
}
span.ttlCntr {
    color: #fff;
    font-weight: 500;
}
div#counter .conterAll:after {
    content: "";
    height: 60px;
    width: 3px;
    background: #fff;
    position: absolute;
    top: 0;
    bottom: 0;
    margin: auto;
    right: 0;
    border-radius: 5px;
}
div#counter .conterAll:nth-last-child(1):after {
    background: transparent;
}
main.all-content {
    overflow-x: hidden;
}
.socialConect a.google.socialConectLink {
    position: relative;
    padding: 11px 24px;
    cursor: pointer;
    width: 420px;
    height: 44px;
    border-radius: 30px;
    border: 1px solid #CBD5E0;
    background:  #FFF;
    color: #67728A;
    font-family: Outfit;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px; /* 155.556% */
    margin-top: 0;
    margin-bottom: 0;
}
.socialConect a.google.socialConectLink img {
    width: 29px;
    position: absolute;
    left: 24px;
    height: 29px;
    object-fit: contain;
    /* margin:  0; */
    top: 0;
    bottom: 0;
    margin: auto;
}
.orText span {
    position: relative;
    cursor: default;
    color: #718096;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: Inter;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px; /* 166.667% */
    letter-spacing: -0.154px;
}
.-layout .fixed-header button.user-btn.dropdown-toggle.show {
    color: #171936;
}
.-layout header.fixed-header button.user-btn.dropdown-toggle {
    color: #171936;
}
label.priceSpan {
    display: block;
    color: #fff;
}
.outVarification {
    text-align: center;
    color: #fff;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 13px 22px;
    margin-bottom: 20px;
}
body .select2-container--default .select2-selection--single .select2-selection__rendered {
    padding: 3px 10px;
    line-height: normal;
    line-height: 37px;
}
body .select2-container--default .select2-selection {
    background: #F9F9FC;
    border-radius: 10px;
    height: 46px;
    font-size: 14px;
    padding: 0 12px 0 0;
    /* line-height: 46px; */
    border-color: #EBEBFF;
}
body .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 46px;
}
body .select2-container--default .select2-selection--multiple .select2-selection__choice {
    line-height: normal;
    height: auto;
    vertical-align: middle;
    /* margin-top: -9px; */
    font-size: 12px;
    margin-top: 0;
}
body .select2-container .select2-selection--multiple .select2-selection__rendered {
    line-height: normal;
    height: 44px;
    line-height: 29px;
    margin: 0 !important;
    width: 100% !important;
    /* display: inline-block; */
}
textarea.select2-search__field {
    vertical-align: unset !important;
}
span.larg {
    display: inline-block;
    clear: both;
    width: 100%;
    margin-top: 12px;
}
.select2-container--default .select2-selection--multiple {
    overflow-x: hidden;
    overflow-y: auto;
}
input#amount-price {
    width: 100%;
    background: #fff;
    border-radius: 5px;
    height: 46px;
    font-size: 14px;
    padding: 3px 10px;
    line-height: 37px;
    border: 1px solid #aaa;
}
.wizardForm input~span img {
    width: 31px;
    position: static;
    left: 10px;
    top: 0;
    bottom: 0;
    margin: 0 9px 0 0;
}
.wizardForm input~span {
    flex-direction: row-reverse;
    justify-content: start;
    border-radius: 30px;
    padding: 11px 23px;
    background: linear-gradient(180deg, rgba(90, 90, 90, 0.92) 0%, rgba(129, 129, 129, 0.51) 100%);
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 700;
    font-size: 15px;
    line-height: 23px;
    display: flex;
    align-items: center;
    color: #FFFFFF;
    transition: all 0.3s;
}
tags.tagify.inputtags {
    border-bottom-right-radius: 5px !important;
    border-top-right-radius: 5px !important;
    margin: 0;
    width: calc(100% - 34px);
    padding: 5px;
}
.wizardForm ul li label {
    width: calc(220px - 10px);
    float: left;
    display: inline-block;
    border-radius: 13px;
    position: relative;
}
.wizardForm ul li label input {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 23px;
    display: flex;
    align-items: center;
    color: #FFFFFF;
}
.wizardForm ul li label.facebook input:checked~span,
.wizardForm ul li label.facebook input:hover~span {
    background: #0f92e8d1;
}
.wizardForm ul li label.instagram input:checked~span,
.wizardForm ul li label.instagram input:hover~span {
    background: #C82C83d1;
}
.wizardForm ul li label.youtube input:checked~span,
.wizardForm ul li label.youtube input:hover~span {
    background: #f20000d1;
}
.wizardForm ul li label.twitch input:checked~span,
.wizardForm ul li label.twitch input:hover~span {
    background: #8A43F2d1;
}
.wizardForm ul li label.twitter input:checked~span,
.wizardForm ul li label.twitter input:hover~span {
    background: #1BAEDFd1;
}
.wizardForm ul li label.tiktok input:checked~span,
.wizardForm ul li label.tiktok input:hover~span {
    background: #000000d1;
}
.wizardForm ul li label.autowidth {
    width: auto;
}
.wizardForm ul li label.autowidth span {
    width: auto;
    border-radius: 10px;
    background: rgb(255 245 243);
    border-radius: 32px;
    margin: 0;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 700;
    font-size: 15px;
    color: #111111;
    padding: 0 16px;
    height: 40px;
    transition: all 0.3s;
    font-weight: 700;
    font-size: 16px;
    line-height: 20px;
}
.wizardForm input:checked~span,
.wizardForm input:hover~span {
    background: #1DF3F1;
}
.menus.ms-auto {
    display: flex;
    list-style: none;
    align-items: center;
}
span.showUser {
    color: #171936;
    line-height: 48px;
    text-align: center;
    font-weight: 700;
    margin-right: auto;
}
.menus.ms-auto a.icon {
    color: #171936;
    font-size: 25px;
    position: relative;
    line-height: 46px;
}
.-layout .menus.ms-auto a.icon {
    color: #fff;
}
span.checkRd.round {
    height: 31px;
    width: 100%;
    background: #dcdada;
    display: inline-block;
    border-radius: 15px;
    position: relative;
    transition: all 0.3s;
    float: left;
}
.alsi .floating-label.smalSpace {
    margin: 0;
    padding: 0;
}
.alsi .floating-label.smalSpace label.switch {
    margin: 0 !important;
    float: left;
    display: inline-block;
    cursor: pointer;
}
span.checkRd.round:after {
    content: "";
    width: 24px;
    height: 24px;
    background: #fff;
    position: absolute;
    left: 33px;
    top: 3px;
    border-radius: 50%;
}
input:checked~span.checkRd.round:after {
    left: 3px;
}
input:checked~span.checkRd.round {
    background: #1DF3F1
}
.menus.ms-auto a.icon span.count {
    position: absolute;
    right: -10px;
    top: -3px;
    background: red;
    min-width: 18px;
    padding: 2px 3px;
    font-style: normal;
    font-weight: 900;
    font-size: 12px;
    line-height: 15px;
    text-align: center;
    color: #FFFFFF;
    width: auto;
    height: 18px;
    border-radius: 18px;
}
.fixed-header span.showUser {
    color: #171936;
}
.fixed-header .menus.ms-auto a.icon {
    color: #171936;
}
li.drop-state {
    padding: 7px 0;
}
li.ntfMenu {
    padding: 0 30px 0 0;
}
.-layout .fixed-header span.showUser {
    color: #171936;
}
.form-group .ui-slider {
    -webkit-appearance: none;
    width: 100%;
    height: 6px;
    background: #e3e3e3;
    outline: none;
    opacity: 1;
    -webkit-transition: .2s;
    transition: opacity .2s;
    border: 0 !important;
}
.imageOuter img.loaderImage {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    width: 37px;
    height: 37px;
}
span.ui-slider-handle.ui-corner-all.ui-state-default {
    -webkit-appearance: none;
    appearance: none;
    width: 12px;
    height: 12px;
    background: #1DF3F1;
    cursor: pointer;
    border-radius: 50%;
    margin: 0 0 0px;
    bottom: -24px;
    border: 0;
    top: -3px;
    margin: 0 0 0px -7px;
    pointer-events: all;
}
span#priceTwoprice,
span#priceTwofollowers {
    float: right;
    font-size: 12px;
    margin: 8px 0 0;
    font-weight: 500;
}
span#priceOneprice,
span#priceOnefollowers {
    font-size: 12px;
    margin: 8px 0 0;
    font-weight: 500;
}
label.connectLable {
    margin-top: -18px;
    display: inline-block;
    position: relative;
    top: -7px;
}
.userDetails .checkedNameBox span.selcc {
    display: block;
}
label.checkboxAllCheck span.selcc {
    background: #1DF3F1;
    border: solid 2px #1DF3F1;
    color: #fff;
    border-radius: 5px;
    font-size: 18px;
    line-height: normal;
    padding: 7px 18px;
    display: block;
}
label.checkboxAllCheck input {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}
label.checkboxAllCheck input:hover~a {
    background: transparent;
    color: #1DF3F1
}
.selectedUserOuter .markeplaceRequest .userDetails .checkboxAllCheck span.seledcc {
    display: inline-block;
}
.selectedUserOuter .markeplaceRequest .userDetails .checkboxAllCheck span {
    display: block;
}
.selectedUserOuter .markeplaceRequest .userDetails .checkboxAllCheck span span.selcc {
    display: none;
}
.selectedUserOuter .userDetails .checkboxAllCheck span {
    display: none;
    line-height: 23px;
    align-items: center;
    color: green;
    font-weight: 600;
    text-transform: capitalize;
    font-size: 14px;
}
body .markeplaceSidebar .select2-container--default .select2-selection {
    line-height: normal;
}
body .markeplaceSidebar .select2-container .select2-selection--multiple .select2-selection__rendered {
    line-height: normal;
}
body .markeplaceSidebar .select2-container--default span.select2-selection.select2-selection--multiple {
    line-height: normal;
    padding: 6px;
}
.selectedUserOuter .userDetails .checkboxAllCheck a.sselectBtn {
    display: none;
}
.selectedUserOuter .userDetails .checkboxAllCheck:hover .btn-danger {
    background: transparent;
    color: rgb(237 0 0 / 80%);
}
.removeButton {
    text-align: right;
}
.input-group span.select2.select2-container.select2-container--default {
    width: calc(100% - 35px) !important;
}
.userDetails.selectBox {
    display: none;
}
.flexUser {
    flex: 0 0 calc(33.33% - 18px);
    max-width: calc(33.33% - 18px);
    display: none;
}
.flexUserOuter {
    column-gap: 25px;
}
.steps.selectedUserOuter {
    padding: 0;
    box-shadow: 0 0 32px 0 rgb(27 58 146 / 52%);
}
.steps.selectedUserOuter div#selectedUser {
    padding: 0;
}
#selectedUser .checkedNameBox>label.checkboxAllCheck {
    display: none;
}
span.select2.select2-container.select2-container--default {
    width: 100% !important;
}
.SelectedUserOpen {
    display: none;
}
.contentAllWz {
    width: 100%;
    display: flex;
}
.vizrdSelection {
    display: flex;
    width: auto;
    float: left;
    align-items: center;
    font-weight: 600;
    font-size: 15px;
    margin: 7px 8px 0 0;
}
.targetgroup.forTag {
    width: 100%;
    display: inline-block;
    flex-direction: column;
    min-width: 100%;
    height: 69px;
    overflow: hidden;
}
.targetgroup.forTag span {
    white-space: normal;
    width: 100%;
    overflow: hidden;
    display: inline-block;
    text-overflow: ellipsis;
    font-weight: bold;
    flex-direction: column;
}
.input-group-prepend .input-group-text {
    height: 46px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
.userDetails1 a {
    max-width: calc(100% - 45px);
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    text-decoration: none;
    font-size: 15px;
    line-height: 31px;
    transition: all 0.3s;
}
span.handelname {
    color: #212529;
    text-decoration: none;
}
.userDetails1 a:hover {
    opacity: 0.7;
}
.loader {
    position: fixed;
    top: 0;
    left: 0;
    background: #171936de;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}
.iconsTable span:hover {
    box-shadow: 3px 3px 3px rgb(0 0 0 / 50%);
}
.iconsTable span:active {
    transform: translateY(8px);
}
.closeErrorNow {
    cursor: pointer;
    margin-left: 29px;
}
.closeErrorNow:hover {
    color: #000000;
    text-shadow: 0 1px 0 #fff;
    opacity: .4;
}
input.typeahead.tm-input.form-control.tm-input-info {
    padding-left: 8px;
    margin-bottom: 0;
}
span.tm-tag.tm-tag-info {
    line-height: 36px;
    margin-bottom: 0;
}
div#custom-handle,
div#custom-handle2 {
    background: #1DF3F1;
    border: 0;
    padding: 1px 4px 18px !important;
    margin-top: 16px;
    font-size: 12px;
    border-radius: 3px;
    width: auto;
    color: #fff;
}
div#custom-handle:before,
div#custom-handle2:before {
    content: "";
    position: absolute;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #1DF3F1;
    left: 0;
    right: 0;
    margin: auto;
    top: -16px;
}
div#seeMore {
    width: auto;
    border-radius: 5px;
    border: 0;
    height: 43px;
    font-size: 16px;
    padding: 0 21px;
    color: #fff;
    font-weight: 700;
    background: #1DF3F1;
    border: solid 2px #1DF3F1;
    transition: all 0.3s;
    text-align: center;
    line-height: 39px;
    cursor: pointer;
    width: 248px;
    display: block;
    margin: 43px auto 0;
}
div#seeMore:hover {
    color: #1DF3F1;
    background: #fff;
}
.iconsTable span.NotconnectedActive {
    cursor: default;
    pointer-events: none;
}
.informationDiv table input[type="text"][readonly] {
    pointer-events: none;
    opacity: 0.5;
}
input.resetFilter {
    width: calc(50% - 5px);
    margin-left: 0;
    margin-right: 0;
    font-size: 12px;
    margin-top: 20px;
}
.newBtn {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
/* New header css */
.new_header {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    background-color: white;
    box-shadow: 0 0 10px #000;

}

.greeting {
    text-align: center;
    width: 100%;
    font-size: 16px;
    color: #2c2d60;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.greeting .name,
.greeting .status {
    width: 100%;
    max-width: 600px;
    text-align: left;
}
.greeting .status {
    font-size: 32px;
    font-weight: bold;
}

.user_profile_pic{
    display: flex;
    align-content: space-around;
    height: 96px;
    justify-content: left;
    margin-left: 40px;
    flex-wrap: wrap;
    flex-direction: row;
}
/* Dashboard css */
.dashboard-left {
    position: absolute;
    left: 0;
    height: 100%;
    border-radius: 0;
    box-shadow: 0 2px 1px rgba(0, 0, 0, 0.7);
    transition: all 0.3s;
    z-index: 999;
    padding: 0 0 23px;
    width: 286px;
    background: #F2F2FC;
    border-right: 2px solid #F2F2FC;
    box-shadow: 0 0 10px #000;
    clip-path: inset(0 -10px 0 0);
}
.dashboard_outer {
    position: relative;
    display: inline-block;
    width: 100%;
}
.dashboard-right {
    margin: 0;
    margin-left: 286px;
    margin-top: 0;
    transition: all 0.3s;
}
.toggleLink i.fi.fi-rr-menu-burger {
    font-size: 23px;
    line-height: 23px;
    height: 22px;
    display: inline-block;
    margin-right: 7px;
    cursor: pointer;

}
.toggleLink {
    align-items: center;
    justify-content: start;
    font-size: 18px;
    text-transform: capitalize;
    font-weight: 600;
    padding: 0;
    white-space: nowrap;
    color: #1c2053;
    cursor: pointer;
    text-align: right;
    margin-right: -16px !important;
    margin-top: -35px !important;
    margin-bottom: 26px !important;
}
.ativeLink .form-check {
    position: relative;
    width: 183px;
    height: auto;
    min-height: unset;
    margin: 0;
    padding: 0;
}
.ativeLink .form-check input.form-check-input {
    position: absolute;
    left: -99999px;
    top: 0;
    margin: 0;
    pointer-events: none;
}
.ativeLink .form-check input.form-check-input~label {
    height: auto;
    content: "";
    display: inline-block;
    color: #fff;
    font-size: 15px;
    line-height: 20px;
    margin: 0;
    padding: 15px 8px;
    text-align: center;
    font-weight: 500;
    letter-spacing: 1.5px;
    cursor: pointer;
    overflow: hidden;
    width: 183px;
    box-shadow: 0px 10px 20px rgba(35, 170, 35, 0.1);
    border-radius: 15px;
}
.colOffline .ativeLink .form-check input.form-check-input~label {
    background: red;
}
.colOnline .ativeLink .form-check input.form-check-input~label {
    background: #63C063;
}
input:focus,
button:focus,
input:active,
button:active {
    outline: none !important;
    box-shadow: none !important;
}
.dashboardsidebar ul a i {
    width: 50px;
    display: inline-block;
    text-align: center;
    height: 50px;
    font-size: 24px;
    line-height: normal;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 9px;
    min-width: 50px;
    flex: 0 0 50px;
    transition: all 0.3s;
}
.ramt header.site-header.rink-header {
    position: fixed;
    top: 0;
    left: 0;
    background: #fff;
    padding: 17px 0 !important;
}
.ramt .operator-profile {
    margin-top: 0;
    margin-bottom: 0;
}
.ramt .content-area {
    padding: 20px;
}
/* My profile CSS */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}
.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}
.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
}
input:checked+.slider {
    background-color: green;
}
input:focus+.slider {
    box-shadow: 0 0 1px #2196F3;
}
input:checked+.slider:before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(26px);
}
.slider.round {
    border-radius: 34px;
}
.slider.round:before {
    border-radius: 50%;
}
span.fildCount {
    width: 20px;
    height: 20px;
    border-radius: 15px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    transition: all 0.3s;
    position: absolute;
    top: 0;
    bottom: 0;
    margin: auto;
    right: 36px;
    cursor: pointer;
}
span.fildCount.reqFld {
    margin-left: 0;
    margin-right: 0;
    background: red;
    right: 13px;
}
span.fildCount.onlFld {
    margin-left: auto;
    background: orange;
}
input[readonly] {
    border-color: transparent !important;
    background: #e7e7e7 !important;
}
span.onClkOk {
    position: absolute;
    right: 14px;
    font-size: 13px;
    background: red;
    color: #fff;
    padding: 3px 10px 6px 30px;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    top: 46px;
    cursor: pointer;
    display: none;
    z-index: 0;
}
.nextLinkOnl.onClkOk {
    background: orange;
}
.nextLinkReq.onClkOk {
    background: red;
}
.step_form_link {
    position: relative;
    width: 100%;
    display: flex;
    align-items: top;
    justify-content: space-between;
}
.input-group span.select2-selection.select2-selection--multiple {
    display: flex;
    align-items: baseline;
}
.input-group span.select2-selection.select2-selection--multiple ul {
    align-items: center;
    display: flex !important;
}
.input-group span.select2-selection.select2-selection--multiple ul li {
    margin: 0 !important;
}
.step_form {
    margin-top: 35px;
}
.bin_links {
    width: auto;
    border-radius: 5px;
    height: auto;
    font-size: 15px;
    color: #fff;
    font-weight: 700;
    background: #1DF3F1;
    border: solid 2px #1DF3F1;
    transition: all 0.3s;
    padding: 10px 29px;
    margin-top: 14px;
    cursor: pointer;
}
body .input-group-prepend~.select2-container--default span.select2-selection.select2-selection--multiple {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
.steps_con {
    display: none;
    position: relative;
    margin: 0 20px 40px;
    background: #FFFFFF;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 20px;
    padding: 22px 40px 30px;
}
.ser_op:first-child:after {
    content: "";
}
.newpost.draft {
    background: gray;
    border-color: gray;
}
.newpost.draft:hover {
    color: gray;
}
.ads_methods {
    max-width: 605px;
    margin: 0 auto;
}
.ads_method_links {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 22px;
}
.ads_link .ads_type {
    background: gray;
    color: #fff;
    padding: 5px 13px 5px;
    border-radius: 5px;
    line-height: 17px;
    display: inline-block;
    font-size: 13px;
    cursor: pointer;
    border: solid 1px gray;
    font-weight: 600;
}
span.ads_link_info {
    font-size: 19px;
    color: #147E6D;
    display: inline-block;
    padding: 5px 4px 0 0;
    float: left;
}
span.ads_check {
    font-size: 19px;
    margin: 6px 0 0 2px;
    display: inline-block;
    float: right;
    color: darkgray;
}
.ads_link .ads_type:hover {
    color: gray;
    background: transparent;
}
.priceSpan i.fa-solid.fa-circle-info {
    font-size: 19px;
    color: #fff;
    float: left;
    margin: 2px 4px 0 0;
    cursor: pointer;
}
.pricing_top small {
    font-size: 13px;
    line-height: normal;
    text-align: left;
    padding: 5px 0 0;
    display: inline-block;
    width: 100%;
    color: #fff;
}
span.suggest_price {
    display: block;
    text-align: center;
    background: #ffffff36;
    border: solid 1px #ffffff;
    width: 130px;
    border-radius: 4px !important;
    margin: 9px auto 0;
    font-weight: 600;
    color: #fff;
}
.input-group-text {
    border: 0 !important;
    background: lightgray !important;
}
.checkone .socialCnt {
    margin: 24px 10px 0;
}
.inputs {
    padding: 0 10px;
    flex: 0 0 calc(50% - 30px);
}
.checkone.d-flex {
    margin: 0 -10px 0;
    width: 100%;
}
.main {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    border: solid 1px #1DF3F1;
    border-radius: 0;
    overflow: hidden;
    margin-bottom: 20px;
}
.myDiv {
    border-bottom: solid 2px #e7e7e7;
    padding: 20px 0;
    background: #008000b3;
    padding: 18px 26px 29px;
    width: 100%;
    margin: 14px 0;
    border-radius: 24px;
}
div#slider {
    width: 100%;
}
.sl-progress {
    width: 100%;
}
.main button.btn-range {
    display: inline-block;
    padding: 11px 11px;
    width: 39px;
    background: #1df3f136;
    border: 0;
    float: left;
    border-radius: 0;
}
.place_slider span {
    float: left;
}
.placeSlidee_title {
    background-color: #212529;
    text-align: center;
    color: #fff;
    margin-bottom: 7px;
    font-size: 13px;
    padding: 6px 10px;
}
.main span.ui-slider-handle.ui-corner-all.ui-state-default {
    width: 85px;
    font-size: 11px;
    text-align: center;
    border-radius: 12px;
    line-height: normal;
    height: 25px;
    display: inline-block;
    line-height: 25px;
    color: #000;
    margin-top: 9px;
    margin-left: 0;
    pointer-events: none;
    cursor: default;
}
.main div.ui-slider {
    width: 100%;
    height: 37px;
    border: 0;
    margin: 0 85px 0 0;
    pointer-events: none;
}
.main .ui-slider-range.ui-corner-all.ui-widget-header.ui-slider-range-max {
    background: transparent;
}
.checkoneOn .socialCnt {
    margin: 0;
    position: relative;
    overflow: visible;
}
.checkoneOn .inputs {
    padding: 14px;
    border: solid 1px #a8a8a8;
    margin: 0 0 0 9px;
    flex: 1;
    height: 46px;
    padding: 0;
    line-height: 44px;
    text-align: center;
}
div#mediayoutube {
    background: #c5c5c5;
}
.checkoneOn {
    align-items: center;
    padding: 0;
    margin-bottom: 20px;
}
.checkoneOn .inputs.act {
    background: #1df3f136;
    border: solid 1px #1DF3F1;
}
.dyn_pric {
    text-align: center;
    border: solid 1px #ffba00;
    background: #ffba0040;
    font-size: 12px;
    float: right;
    width: calc(100% - 25px);
    padding: 5px 5px;
}
.asdd {
    display: flex;
    align-items: center;
    margin: 20px 0;
}
.asdd span.ads_link_info {
    padding-top: 0;
}
.asdd .custom-control.custom-checkbox {
    line-height: 31px;
    position: relative;
}
.asdd label {
    position: relative;
    display: inline-block;
    line-height: normal;
    padding: 0 0 0 22px;
    margin: 0;
}
.asdd label:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    border: solid 1px #9b9b9b;
    width: 16px;
    height: 16px;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    border-radius: 4px;
}
.asdd input.custom-control-input.ds {
    position: absolute;
    left: 0;
    top: 0;
    width: 16px;
    height: 16px;
    z-index: 1;
    opacity: 0;
}
.asdd input.custom-control-input.ds:checked~label:after {
    content: "";
    width: 12px;
    height: 12px;
    background: url(../images/checkmark-png-5.png);
    position: absolute;
    background-size: contain;
    left: 2px;
    top: 3px;
}
.ads_link.active .ads_type {
    background: #ffba00;
    border: solid 1px #ffba00;
    color: #fff;
}
.active span.ads_check {
    color: darkturquoise;
}
.ads_content {
    display: none;
}
.ads_content.current {
    display: block;
}
.informationDiv .nav-tabs .nav-link {
    background: gray;
    color: #fff;
    padding: 5px 13px 5px;
    border-radius: 5px;
    line-height: 17px;
    display: inline-block;
    font-size: 13px;
    cursor: pointer;
    border: solid 1px gray;
    font-weight: 600;
    margin: 0 8px;
}
.informationDiv .nav-tabs .nav-link.active {
    background: #ffba00;
    border: solid 1px #ffba00;
    color: #fff;
}
.informationDiv .nav-tabs {
    justify-content: center;
    border: 0;
}
.tabsram {
    display: flex;
    width: 100%;
    min-width: 100%;
}
.tabsram-in {
    flex: 0 0 50%;
    max-width: 50%;
    position: relative;
    padding: 0;
}
.orcTitle {
    text-align: center;
    font-size: 25px;
    font-weight: 500;
    margin: 28px 0 26px;
}
.orcCont {
    display: flex;
    overflow: auto;
}
.orcCont .flexUser {
    flex: 0 0 253px;
    min-width: 231px;
    margin: 12px;
}
.tabsram-in:first-child:before {
    content: "";
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    width: 3px;
    height: 70%;
    background: #9d9d9d;
}
.tabsram-in:first-child {
    padding-right: 30px;
}
.tabsram-in:last-child {
    padding-left: 30px;
}
body.marketplace-layout.logedin.noScroll {
    overflow: hidden;
}
.selectmedia ul.parsley-errors-list {
    color: #fff;
    font-weight: bold;
}
input.parsley-error,
.input-group.eror tags.tagify.form-control.inputtags.ccc.ds.tagify--noTags.tagify--empty,
body .parsley-error~.select2-container--default .select2-selection {
    border-color: red !important;
}
.modal-content {
    border-radius: 20px;
    border: 0;
}
.influencer .modal-content {
    background: #fff;
    border: 1px solid #EBEBFF;
    border-radius: 15px;
}
.wizardHeading {
    font-family: 'Mulish';
    text-align: center;
    margin: 25px 0 31px;
    font-style: normal;
    font-weight: 800;
    font-size: 32px;
    line-height: 40px;
    letter-spacing: 3px;
    text-transform: uppercase;
    color: #353B5F;
    max-width: 70%;
    margin-left: auto;
    margin-right: auto;
}
.confirm-link:hover {
    background-color: transparent;
    color: #1DF3F1;
}
.wizardForm .text-center {
    margin-top: 15px;
}
.confirmpopup .modal-content {
    border-radius: 17px;
    border: 0;
    border-radius: 15px;
    padding: 30px 30px;
}
.confirmpopup .modal-header {
    border: 0;
    padding: 0;
    text-align: center;
    align-items: center;
    justify-content: center;
}
.confirmpopup .modal-header h5 {
    text-align: center;
    font-size: 31px;
    font-weight: 700;
    margin-bottom: 23px;
    color: #171936;
}
.confirmpopup .modal-footer {
    border: 0;
    padding: 0;
    justify-content: center;
}
.confirmpopup .modal-footer button.btn.btn-primary.confirm {
    width: auto;
    border-radius: 5px;
    border: 0;
    height: 46px;
    font-size: 20px;
    padding: 0 19px;
    color: #fff;
    font-weight: 700;
    background: #1DF3F1;
    border: solid 2px #1DF3F1;
    transition: all 0.3s;
}
.confirmpopup .modal-footer button.btn.btn-secondary.cancel {
    width: auto;
    width: auto;
    border-radius: 5px;
    border: 0;
    height: 46px;
    font-size: 20px;
    padding: 0 19px;
    color: #fff;
    font-weight: 700;
    border: solid 1px #6c757d;
}
a.confirm-link.cancleBtn:hover {
    background: transparent;
    color: gray;
}
.confirmpopup .modal-footer button.btn.btn-primary.confirm:hover {
    color: #1DF3F1;
    background: transparent;
}
.confirmpopup .modal-footer button.btn.btn-secondary.cancel:hover {
    color: #6c757d;
    background: transparent;
}
.worning_point {
    background: #AD80FF;
    text-align: center;
    color: #fff;
    font-size: 13px;
    padding: 6px;
    margin-bottom: 15px;
}
.dashboardsidebar ul li ul.subMenu {
    margin: 0;
    left: 100%;
    top: 0;
    padding: 0;
}
.dashboardsidebar ul li ul.subMenu a {
    padding: 9px 26px 9px 21px;
    border: 0;
    justify-content: start;
    width: calc(100% - 20px);
}
.active~.menusal {
    display: block;
}
.menusal.opwnMenu {
    display: block;
    width: 100%;
}
.dashboard-left.open {
    width: 50px;
}
.dashboard-left.open .colOnline .toggleLink img {
    transform: rotate(180deg);
}
.dashboardsidebar {
    white-space: nowrap;
}
.open .ativeLink {
    padding: 0;
}
.open .ativeLink .form-check input.form-check-input~label {
    padding: 11px 5px;
    border-radius: 0;
    width: 50px;
}
.open span.nameInspan {
    width: 0;
    overflow: hidden;
}
.colOnline .toggleLink {
    background: transparent;
}
.dashboard-right.open {
    margin-left: 50px;
}
.ativeLink .form-check input.form-check-input~label i {
    font-size: 21px;
    background: #FFFFFF;
    color: #63C063;
    border-radius: 7px;
    font-size: 14px;
    line-height: normal;
    padding: 0px 2px;
    margin-right: 7px;
}
.open .ativeLink .form-check input.form-check-input~label i {
    margin-right: 0;
    font-size: 20px;
    display: inline-block;
    padding: 1px 5px;
}
body .mCSB_container,
body .mCustomScrollBox {
    overflow: visible !important;
}
.menudiv {
    overflow-y: visible;
    overflow-x: visible;
}
#mCSB_1_dragger_vertical{
  height: 0px !important;
}
.dashboardsidebar ul li {
    position: relative;
    align-items: center;
    width: 100%;
    margin: 12px auto;
}
.dashboardsidebar ul li.home-link {
    display: flex;
}
.menusal {
    position: static;
    left: 50px;
    top: 0;
    padding-left: 0;
    z-index: 999;
    background: #efefef;
    padding: 10px 8px 10px;
    display: none;
    width: 100%;
}
.open .menusal {
    position: absolute;
    width: 250px;
}
span.lki {
    width: 100%;
}
.open span.lki {
    width: 0;
    overflow: hidden;
    display: none !important;
}
.popup2btns {
    justify-content: center;
    justify-items: unset;
}
a.confirm-link.cancleBtn {
    margin-left: auto;
}
.popup2btns a.confirm-link.cancleBtn {
    background: gray;
    border-color: gray;
}
.popup2btns a.confirm-link.cancleBtn:hover {
    background: transparent;
}
.hashTagitle .input-group span.select2-selection.select2-selection--multiple ul li {
    padding: 2px 9px;
    margin: 0 0 0 13px !important;
    padding-left: 24px;
}
.hashTagitle button.select2-selection__choice__remove {
    height: 22px;
    border-top: 0 !important;
    bor-t: 4;
}
.hashTagitle .input-group-prepend~.select2-container--default span.select2-selection.select2-selection--multiple {
    align-items: center;
}
.hashTagitle .input-group-prepend~.select2-container--default span.select2-selection.select2-selection--multiple textarea.select2-search__field {
    margin: 0 0 0 13px;
}
.ativeLink {
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 7px 15px;
}
.dashboardsidebar ul li.desabled {
    cursor: default;
    pointer-events: none;
    opacity: 0.5;
}
.datatip:after {
    content: attr(data-tip);
    position: absolute;
    bottom: 100%;
    left: 2px;
    font-size: 12px;
    background: #fff;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
    padding: 7px 11px;
    border-radius: 5px;
    display: none;
}
.datatip:hover:after {
    display: block;
    content: attr(data-tip);
    color: #1c2053 !important;
}
.datatip {
    position: relative;
}
.informationDiv .datatip:after {
    width: 250px;
    right: -999px;
    margin: auto;
    left: -999px;
    text-align: center;
}
.dashboardsidebar ul a i.bi-exclamation-triangle-fill {
    color: red;
}
.dashboardsidebar ul a i.bi-heart-fill {
    color: green;
}
.custom-checkbox input {
    position: absolute;
    width: 16px;
    height: 16px;
    top: 1px;
    left: 0;
    z-index: 1;
    opacity: 0;
}
.custom-checkbox input:checked~label:after {
    content: "\f00c";
    font-family: "Font Awesome 6 Free";
    font-weight: bold;
    position: absolute;
    left: 2px;
    top: 3px;
}
.socialCnt.nmt {
    position: relative;
    overflow: visible;
}
span.onClkOk>span {
    background: #ffffff94;
    width: 20px;
    height: 20px;
    display: inline-block;
    position: absolute;
    left: 5px;
    border-radius: 50%;
    text-align: center;
    line-height: 20px;
}
.disable {
    pointer-events: none !important;
    cursor: default !important;
}
.resdf span.nextLinkReq.onClkOk.rqrIn1 {
    top: 45px;
}
.notempty {
    border-color: #aaa !important;
}
.notempty~span.onClkOk {
    display: none !important;
}
.reqr .borderHide .select2-container--open span.select2-selection {
    border-color: #aaa !important;
}
.borderHide span.onClkOk {
    display: none !important;
}
.paymentOuter {
    width: 100%;
    margin: 42px auto;
    display: flex;
    box-shadow: 0 1px 53px rgb(0 0 0 / 24%);
    padding: 30px 14px;
    border-radius: 15px;
}
.paymentOuterLeft {
    flex: 0 0 243px;
    margin: 0 15px;
}
.paymentOuterLeft input {
    position: absolute;
    left: -99999px;
}
.paymentOuterLeft input~span {
    width: 100%;
    display: inline-block;
    text-align: center;
    height: 100%;
    height: 52px;
    line-height: 48px;
    background: #8f8f8f;
    color: #fff;
    font-size: 20px;
    cursor: pointer;
    border: solid 1px #8f8f8f;
    border-radius: 5px;
    transition: all 0.3s;
    font-weight: 700;
}
label.list-group-item {
    padding: 0;
    margin: 0;
}
.paymentOuterRight {
    margin: 0 15px;
}
.paymentOuterLeft input~span:hover {
    background: transparent;
    color: #8f8f8f;
}
.paymentOuterLeft input:checked~span {
    background: #1DF3F1;
    border-color: #1DF3F1;
}
.paymentOuterLeft input:checked~span:hover {
    background: transparent;
    color: #1DF3F1;
}
.content_input {
    display: flex;
}
.socialMediaSection .list-group {
    flex-direction: revert;
}
.socialMediaSection .list-group input {
    position: absolute;
    left: -9999pc;
}
.socialMediaSection .list-group img.icon {
    width: 63px;
    display: inline-block;
}
.socialMediaSection .list-group label.list-group-item {
    margin: 14px 35px 38px 0;
    border: 0;
}
.socialMediaSection .list-group input~span:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 20px;
    height: 20px;
    border: solid 1px black;
    border-radius: 4px;
    bottom: 0;
    margin: auto;
}
.socialMediaSection .list-group input[disabled]~span:before {
    background-color: #e3e3e3;
    border-color: #e3e3e3;
}
.socialMediaSection .list-group input~span {
    position: relative;
    padding-left: 30px;
    display: inline-block;
    cursor: pointer;
}
.content_descriptin {
    font-size: 15px;
    flex: 0 0 478px;
    padding-right: 19px;
}
.socialMediaSection .list-group input:checked~span:after {
    content: "\f00c";
    position: absolute;
    left: 2px;
    top: 0;
    bottom: 0;
    font-size: 17px;
    font-family: "Font Awesome 6 Free";
    color: darkgreen;
    line-height: 22px;
    height: 20px;
    margin: auto;
}
.likeCheck label.list-group-item input~span:before {
    content: "";
    position: relative;
    width: 50px;
    height: 50px;
    border: solid 1px;
    border-radius: 10px;
    display: inline-block;
}
.likeCheck label.list-group-item input {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    opacity: 0;
}
.likeCheck label.list-group-item input:checked~span:after {
    content: "";
    content: "\f00c";
    position: absolute;
    left: 11px;
    top: 0;
    bottom: 0;
    font-size: 28px;
    font-family: "Font Awesome 6 Free";
    color: darkgreen;
    line-height: 22px;
    height: 20px;
    margin: auto;
}
.selectFormsInput {
    margin-top: -36px;
    margin-bottom: -36px;
}
.paymentOuterLeft input:checked~span:before {
    content: "\f00c";
    position: static;
    left: 11px;
    top: -3px;
    bottom: auto;
    font-size: 38px;
    font-family: "Font Awesome 6 Free";
    color: darkgreen;
    line-height: 22px;
    height: 20px;
    margin: auto;
    margin-left: -38px;
    margin-top: -20px;
    display: inline-block;
    position: relative;
}
tags.tagify.form-control.inputtags {
    padding: 3px 0;
    height: 46px;
}
.open span.fildCount {
    position: absolute;
    right: 0;
    top: 9px;
    width: 14px;
    font-size: 10px;
    height: 14px;
    z-index: 999;
    bottom: auto;
}
.open span.fildCount.reqFld {
    bottom: 9px;
    margin: 0;
    top: auto;
}
.open .dashboardsidebar ul a i {
    font-size: 18px;
}
.select_media {
    background: gray;
    margin: 0 10px 10px;
    flex: 0 0 calc(33.33% - 20px);
    border-radius: 24px;
    padding-bottom: 67px;
    position: relative;
}
.checkcontentIn ul li {
    display: flex;
    align-items: center;
    justify-content: start;
    color: #fff;
    margin: 25px 0;
}
.checkcontentIn ul li:nth-child(even) {
    flex-direction: row-reverse;
    width: 100%;
    justify-content: flex-start;
    text-align: right;
}
.checkcontentIn ul li img {
    width: 47px;
    margin-right: 19px;
}
.checkcontentIn ul {
    margin: 0;
    padding: 16px 21px;
    width: 100%;
}
.checkcontentIn ul li:nth-child(even) img {
    margin: 0 0 0 19px;
}
.mediabox {
    justify-content: center;
}
span.whatname {
    background: #fff;
    width: 156px;
    display: inline-block;
    text-align: center;
    border-radius: 4px;
    padding: 3px 4px;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    pointer-events: none;
}
.mdst {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 18px 0 0;
}
label.custom-control.custom-checkbox {
    display: flex;
    margin: 0 7px 0 0;
    align-items: center;
    position: relative;
    cursor: pointer;
    width: auto;
}
label.custom-control.custom-checkbox span.custom-control-indicator {
    width: 52px;
    height: 26px;
    display: inline-block;
    background: #cdcbcb;
    border-radius: 22px;
    position: relative;
    flex: 0 0 52px;
}
label.custom-control.custom-checkbox span.custom-control-indicator:after {
    content: "";
    width: 20px;
    height: 20px;
    position: absolute;
    left: 3px;
    top: 3px;
    border-radius: 30px;
    background: #fff;
    box-shadow: 0 3px 12px rgb(0 0 0 / 57%);
    transition: all 0.3s;
}
label.custom-control.custom-checkbox input:checked~span.custom-control-indicator:after {
    left: 29px;
}
.select2-selection__rendered img.img-flag,
.select2-results__option img {
    width: 24px;
    margin-right: 14px;
}
.select2-selection__rendered span,
.select2-results__option span {
    display: flex;
    align-items: center;
}
.select2-results__option:hover,
.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable {
    background: #b9fab9 !important;
    color: black !important;
}
.sahrePrice {
    width: 824px;
    margin: 0 auto;
}
.sahrePriceHeading {
    background: #fff;
    display: block;
    text-align: center;
    border-radius: 4px;
    padding: 3px 4px;
    margin: 0 auto 25px;
    color: #000;
    width: 158px;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor: pointer;
    pointer-events: none;
}
.market_place_slider img.iconBlack {
    width: 32px;
    height: 32px;
    margin-right: 7px;
}
.market_place_slider .placeSlidee_title {
    flex: 1;
}
.market_place_slider {
    margin-top: 20px;
}
.pops {
    display: block;
    text-align: center;
    background: #1df3f136;
    border: solid 1px #1DF3F1;
    width: 130px;
    border-radius: 4px !important;
    margin: 9px auto 0;
    font-weight: 600;
    width: 100%;
}
.iconbox.alrtn {
    display: flex;
    align-items: center;
    margin-bottom: 21px;
    margin-top: 20px;
}
.alrtn i.fa-solid.fa-circle-info {
    font-size: 19px;
    color: #147E6D;
    float: left;
    margin: 0 12px 0 0;
}
.text-center.mt-mjh {
    margin: 0;
    color: #fff;
}
div.ser_op.disabled {
    pointer-events: none;
}
.selectmedia {
    pointer-events: none;
    cursor: default;
    position: absolute;
    width: 100%;
    padding: 0 22px;
    bottom: 21px;
    display: none;
}
.checkedmedia .selectmedia {
    pointer-events: all;
    cursor: pointer;
    display: block;
}
.vizrdSelection label.custom-control.custom-checkbox {
    margin-left: 12px;
}
.vizrdSelection label.custom-control.custom-checkbox:first-child {
    margin-left: 0;
}
label.custom-control.custom-checkbox input:checked~span.custom-control-indicator {
    background: linear-gradient(90deg, rgba(29, 243, 241, 1) 68.23%, rgba(7, 175, 174, 1) 100%);
}
.desabled i.bi.bi-exclamation-triangle-fill.datatip {
    opacity: 0;
}
a.dropdown-item.desabled {
    pointer-events: none;
    cursor: default;
    opacity: 0.7;
}
.oii {
    text-align: center;
    background: green;
    border-radius: 6px;
    font-size: 17px;
    padding: 4px 0;
    color: #fff;
    font-weight: 600;
}
.side-barlogo {
    width: 224px;
    margin: 0 auto;
    margin-left: 24px;
    /* padding: 25px 0; */
    transition: all 0.3s;
}
.side-barlogo img {
    width: 100%;
}
.dashboardsidebar ul a img {
    /*width: 18px;
    height: 18px;*/
    margin-right: 12px;
    filter: brightness(0) invert(0);
}
.dashboardsidebar ul a:hover img,
.dashboardsidebar ul a.active img {
    /* filter: brightness(0.6) saturate(1.5) hue-rotate(260deg); */
    filter: none !important;
}
.dashboard-right header.site-header {
    box-shadow: 0 9px 22px -16px rgb(27 58 146 / 45%) !important;
    width: calc(100% - 40px);
    position: relative;
    z-index: 1;
    margin: 36px 20px;
    background: #fff;
    border-radius: 19px;
    padding: 13px 14px;
    z-index: 111;
}
.dashboard-right header.site-header .header-logo {
    display: none;
}
.dashboard-right header.site-header .menus.ms-auto {
    margin: 0 !important;
    width: 100%;
}
.dashboard-right header.site-header span.showUser {
    margin-right: auto;
}
.step_form_custom {
    margin: 0 20px 40px;
}
.page_tab {
    margin: 0 20px 40px;
    background: #FFFFFF;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 20px;
    padding: 50px 30px;
    margin-top: 4%;
}
.page_tab .steps {
    margin: 0;
}
.side-barlogo img.iconLogo {
    height: 46px;
    width: auto;
    display: none;
}
.open .side-barlogo img.iconLogo {
    display: block;
    height: 37px;
    margin: auto;
}
.open .side-barlogo img.fullLogo {
    display: none;
}
.open .side-barlogo {
    width: auto;
    padding: 12px 0;
}
.open .profile_svg{
    height: 23px !important;
}
.open .dashboardsidebar ul a {
    padding: 5px;
    margin-top: 10px;
    margin-bottom: 10px;
    /*margin: 7px auto;*/
    /*padding: 0;*/
    /*width: 40px;
    height: 40px;*/
    border-radius: 5px;
}

.open .ativeLink .form-check input.form-check-input~label span {
    display: none;
}
.open .ativeLink .form-check {
    width: 50px;
    flex: auto;
    overflow: hidden;
}
.open .dashboardsidebar .ativeLink {
    display: flex;
    width: 50px;
    flex-direction: column-reverse;
}
.open .toggleLink {
    /* padding: 9px 14px; */
    margin: 0 0 8px;
}
.open .toggleLink img{
    transform: rotate(180deg);
}
.button-ccg img {
    display: none;
}
.open .button-ccg img {
    display: block;
    width: 100%;
    height: 100%;
}
.open .button-ccg span.hide-clp {
    display: none;
}
.open a.button-ccg {
    padding: 0;
    background: transparent;
    width: 40px;
    border-radius: 12px;
    height: 44px;
    margin: 0 auto;
}
.open .dashboardsidebar ul a img {
    /*height: 31px;
    width: auto;*/
    min-widthn: 20px !important;
    width: 20px !important;
    max-width: 20px !important;
    margin: 0 auto;
}
.open .dashboardsidebar ul li.home-link {
    flex-direction: column-reverse;
}
.open .dashboardsidebar ul li.home-link .toggleLink {
    margin: 7px auto;
    width: 40px;
    height: 40px;
}
.colOffline .ativeLink .form-check input.form-check-input~label i {
    color: red;
}
.bin_links:hover {
    background: transparent;
    color: #1DF3F1;
}
.mCS-autoHide>.mCustomScrollBox>.mCSB_scrollTools,
.mCS-autoHide>.mCustomScrollBox~.mCSB_scrollTools {
    opacity: 1 !important;
}
.mCS-minimal.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
    background: #b2b2b2 !important;
    border-radius: 0;
    width: 6px !important;
    border-radius: 5px !important;
}
.mCSB_scrollTools {
    width: 7px !important;
}
div#showError {
    width: 100%;
    text-align: center;
    position: absolute;
    left: 0;
    top: 8px;
    z-index: 1;
    color: red;
    font-size: 12px;
}
.text-center.injj {
    color: red;
    font-size: 35px;
}
span.custom-control-indicator.rdk {
    margin-right: 15px;
}
.conttr {
    display: flex;
    align-items: center;
    margin-right: auto;
}
.select_media .custom-checkbox input {
    width: 219px;
    height: 30px;
    margin-top: -3px;
    cursor: pointer;
}
.hashtagerror {
    font-size: 13px;
    color: red;
    margin: 0px 0 0;
}
.reqr .parsley-error~span.select2-container span.select2-selection {
    border-color: red !important;
}
.firDaat {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 30px;
    white-space: nowrap;
    flex: 0 0 calc(100% - 268px);
    overflow: hidden;
    text-overflow: ellipsis;
}
.custDetail {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 25px;
    white-space: nowrap;
}
.soclPrice {
    font-family: 'Mulish';
    text-align: center;
    display: inline-flex;
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 1px;
    color: #000000;
    height: 100%;
    width: 98px;
    margin-left: 7px;
    align-items: center;
}
.soclDetail {
    text-align: center;
}
.soclDetail span.handelpletform {
    width: 34px;
    height: 34px;
}
.soclDetail span.handelpletform img {
    width: 45px;
    height: 45px;
}
.influncerCol {
    max-width: 139px;
    margin-left: auto;
}
.wewPopup span.small-icon {
    height: 37px;
}
.timer {
    text-align: center;
    align-items: center;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 30px;
    text-align: center;
    color: #404040;
    white-space: nowrap;
    margin-right: 11px;
}
h2.gnrlInformation {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 800;
    font-size: 21px;
    line-height: 24px;
    color: #353B5F;
    margin-bottom: 16px;
}
.sortNN {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 25px;
    text-align: center;
    color: #000000;
    width: 571px;
    margin: auto;
}
.wewPopup .custDetail {
    text-align: center;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 15px;
    line-height: 26px;
    text-align: center;
    margin: 18px 0;
}
.wewPopup .popup2btns {
    margin: 36px 0 21px;
    justify-content: center;
}
input.blueBtn.smallBtn.ds.accept {
    width: 160px;
    height: 35px;
    background: #00BFFF;
    border-radius: 10px;
    border-color: #00BFFF;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 15px;
    line-height: 20px;
    color: #FFFFFF;
    margin: 0 10px;
}
input.blueBtn.smallBtn.ds.reject {
    width: 160px;
    height: 35px;
    background: #FF0000;
    border-radius: 10px;
    border-color: #FF0000;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 15px;
    line-height: 20px;
    color: #FFFFFF;
    margin: 0 10px;
}
.wewPopup .modal-dialog {
    max-width: 601px;
}
input.blueBtn.smallBtn.ds.reject:hover {
    background: transparent;
    color: #FF0000;
}
input.blueBtn.smallBtn.ds.accept:hover {
    background: transparent;
    color: #00BFFF;
}
.authForm .timer .blueBtn.smallBtn {
    width: 146px;
    height: 27px;
    background: rgba(255, 184, 0, 0.38);
    border-radius: 10px;
    border-color: rgba(255, 184, 0, 0.38);
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 13px;
    color: #000000;
    line-height: 23px;
}
input.sbt.blueBtn.smallBtn.ds {
    background: #63C063;
    border-color: #63C063;
}
.requestCamp .timer {
    flex: 0 0 103px;
}
.activeCamp .campningDiv {
    padding: 9px 21px 9px 18px;
}
input.sbt.blueBtn.smallBtn.ds:hover {
    color: #63C063;
    background: transparent;
}
.instr span.select2.select2-container.select2-container--default {
    width: 147px !important;
}
.instr label {
    margin: 0;
    margin-right: 24px;
    font-size: 16px;
}
.instr {
    margin: 24px 0 24px;
}
table#venueTable tr th:first-child {
    border-left: 1px solid #96E5FF !important;
}
table#venueTable tr th:last-child {
    border-right: 1px solid #96E5FF !important;
}
table#venueTable tr th {
    background: #E2F8FF;
    border: 1px solid #96E5FF !important;
    border-left: 0 !important;
    border-right: 0 !important;
    font-weight: 500;
    font-size: 16px;
    line-height: 27px;
    font-family: 'Poppins';
    padding: 10px 19px;
    white-space: nowrap;
}
table#venueTable {
    width: 100%;
}
.SSH img {
    width: 23px;
    height: 23px;
    margin-right: 4px;
}
.SSH {
    margin: 4px 0 5px;
    align-items: center;
}
.clntrr {
    margin-left: auto;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 30px;
    text-align: center;
    color: #404040;
    white-space: nowrap;
    width: 115px;
}
.customerSer .firDaat {
    flex: 0 0 144px;
}
.customerSer .custDetail {
    flex: 0 0 129px;
}
.ordertab .nav-item button.nav-link {
    width: 100%;
    background: transparent;
    color: #353B5F;
    font-weight: 700;
    border: oldlace;
    border-radius: 0;
    border-bottom: solid 3px transparent;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 800;
    font-size: 18px;
    line-height: 24px;
    padding: 12px 17px;
}
ul.nav-tabs.ordertab {
    margin-top: 14px !important;
    margin-bottom: 25px !important;
}
table.connectPrising tr.campningDiv>td {
    padding: 17px 18px 20px;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid #AD80FF;
    border-radius: 80px;
    transform: rotate(0deg);
    border-left: 0;
    border-radius: 0;
    border-right: 0;
    box-shadow: 2px 4px 3px #AD80FF;
    background: #fff;
}
table.connectPrising {
    border-spacing: 0 25px !important;
    border-collapse: separate;
    border-spacing: 0 1em;
    margin: 0 auto;
}
table.connectPrising tr.campningDiv>td:first-child {
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;
    border-left: 1px solid #AD80FF;
    box-shadow: 3px 4px 3px #AD80FF;
}
table.connectPrising tr.campningDiv>td:last-child {
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
    border-right: 1px solid #AD80FF;
}
.custDetail span {
    width: 100%;
    display: block;
    white-space: pre-wrap;
    line-height: 23px;
    min-width: 176px;
}
.connectPrising.inner {
    max-width: 100%;
    margin: 0 auto;
}
.sorting_1 a {
    color: #00BFFF;
    font-weight: 700;
}
.connectPrising.inner .accordion-item .accordion-header button.accordion-button {
    display: flex;
    padding: 13px 20px 15px;
    justify-content: space-between;
    background: #d1c5e8a3;
    border: 1px solid #AD80FF;
    border-radius: 20px;
    cursor: default;
}
.connectPrising.inner .accordion-item {
    background: #fff;
    border: 1px solid #EBEBFF;
    border-radius: 21px !important;
}
.acrtsdes {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 30px;
    color: #000000;
    padding: 19px 21px;
    flex: 0 0 21%;
}
.acrimage {
    width: 36px;
    height: 36px;
    margin: 19px 21px;
    display: inline-block;
    flex: 0 0 36px;
}
.acrimage img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.acrinfCat {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 30px;
    line-height: normal;
    color: #404040;
    flex: 0 0 26%;
}
.acrinfluncerCount {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 30px;
    text-align: center;
    color: #404040;
}
span.roundCr {
    width: 15px;
    height: 15px;
    left: 1836px;
    top: 308px;
    background: #FF0000;
    border-radius: 25px;
    display: inline-block;
    margin-right: 51px;
}
span.roundCr.nol {
    background: transparent;
}
.connectPrising.inner .accordion-item .accordion-header button.accordion-button:after {
    display: none;
}
.connectPrising.inner div.accordion-collapse {
    width: 100%;
    border: 0;
}
.acrinternalData {
    background: #F7F7FF;
    border: 1px solid #EBEBFF;
    border-radius: 10px;
    padding: 11px 25px;
    display: flex;
    align-items: center;
    margin-bottom: 14px;
    justify-content: start;
}
.userPrice {
    flex: 0 0 12%;
}
.userTime img {
    width: 20px;
    height: 20px;
    margin-right: 7px;
}
.userTime {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 30px;
    color: #404040;
    flex: 0 0 34%;
}
.userUserr {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 30px;
    color: #404040;
    flex: 0 0 29%;
}
.userUserr a {
    color: #00BFFF;
    text-decoration: none;
}
.orderFormDiv .selectedUser {
    display: flex;
    flex-wrap: nowrap;
    overflow: auto;
    width: 100%;
    height: auto;
    max-height: unset;
}
.orderFormDiv .steps.selectedUserOuter {
    margin: 29px 0 15px;
}
.orderFormDiv .selectedUserOuter .userDetails {
    max-width: 273px;
    flex: 0 0 273px;
}
table.customTable tbody td {
    background: #F9F9FC !important;
    border: 0;
    vertical-align: middle;
    padding: 10px 23px;
    white-space: nowrap;
}
table.customTable {
    border-collapse: separate;
    border-spacing: 0 15px !important;
}
table.customTable tbody td:first-child {
    border-top-left-radius: 15px;
    border-bottom-left-radius: 15px;
}
table.customTable tbody td:last-child {
    border-top-right-radius: 15px;
    border-bottom-right-radius: 15px;
}
.socialComp>div>div {
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: normal;
    color: #404040;
    padding: 6px 0;
    flex: 0 0 50%;
}
.socialComp>div i {
    width: 22px;
}
.socialComp {
    min-width: 126px;
    flex-wrap: wrap;
    width: auto;
}
input.blueBtn.smallBtn.redsmallBtn.ds,
input.blueBtn.smallBtn.redbigBtn.ds {
    width: auto;
    height: 33px;
    background: #FF0000;
    border-radius: 10px;
    border: solid 1px #FF0000;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    display: flex;
    align-items: center;
    text-align: center;
    padding: 3px 9px;
    color: #FFFFFF;
}
input.blueBtn.smallBtn.submittedReview.ds,
input.blueBtn.smallBtn.greensmallbtn.ds {
    width: auto;
    height: 33px;
    background: #63C063;
    border-radius: 10px;
    border: #63C063;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    display: flex;
    align-items: center;
    text-align: center;
    padding: 3px 9px;
    color: #FFFFFF;
}
body .form-control.textarea {
    padding: 12px 15px;
    height: 125px;
}
.socialInformatin {
    display: flex;
    align-items: center;
}
.soclIn {
    flex: 0 0 60px;
    margin-right: 17px;
}
.soclIn img {
    width: 100%;
}
.socialInformatin .socialComp {
    width: auto;
    margin-left: auto;
}
.connectType {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 30px;
    display: flex;
    align-items: center;
    text-align: center;
    color: #404040;
}
.socialInformatin .socialComp>div {
    width: auto;
    display: flex;
    flex: 0 0 auto;
    margin-left: 18px;
}
.socialInformatin .socialComp>div i {
    font-size: 17px;
    color: #404040;
    width: auto;
    padding-right: 5px;
    display: flex;
    align-items: center;
}
iframe.iframe {
    width: 393px !important;
    height: 273px;
    clear: both;
}
.bold-text {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 30px;
    color: #000000;
    padding: 10px 0 19px;
    text-align: left;
}
.tasklist {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 30px;
    color: #000000;
    margin-bottom: 9px;
    display: flex;
}
.trmtext {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 30px;
    color: #383838;
}
.tasklistinput {
    display: flex;
    align-items: center;
    justify-content: center;
}
.taslListCustomRatio label {
    margin: 0;
    padding: 0 0 0 22px;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 16px;
    color: #1F2937;
}
.taslListCustomRatio label:before {
    content: "";
    position: absolute;
    width: 13px;
    height: 13px;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    border-radius: 50%;
    border: 1px solid #000;
}
.taslListCustomRatio {
    position: relative;
    padding: 0;
    margin: 0 20px 0 0;
    text-transform: capitalize;
}
.taslListCustomRatio input:checked~label:after {
    content: "";
    position: absolute;
    left: 2px;
    top: 0;
    bottom: 0;
    margin: auto;
    width: 9px;
    height: 9px;
    border-radius: 50%;
    background: #000;
}
.taslListCustomRatio input {
    position: absolute;
    left: -999px;
    top: 1px;
    bottom: 0;
    width: 100%;
    z-index: 1;
    opacity: 0;
}
.taslListCustomRatio input:checked~label:before {
    border-color: #000;
}
.tasklistContent {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 30px;
    color: #383838;
}
input#review {
    width: 220px;
    height: 60px;
    left: 735px;
    top: 854.7px;
    background: #63C063;
    border: solid 1px #63C063;
    border-radius: 10px;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 20px;
    align-items: center;
    text-align: center;
    color: #FFFFFF;
    white-space: pre-wrap;
}
#complaint {
    width: 220px;
    height: 60px;
    left: 965px;
    top: 854.7px;
    background: #FF0000;
    border: solid 1px #FF0000;
    border-radius: 10px;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 20px;
    display: flex;
    align-items: center;
    text-align: center;
    color: #FFFFFF;
    margin-left: auto;
}
button,
input[type=button],
input[type=submit] {
    cursor: pointer;
}
.socialComp>div.oneWidth {
    flex: 0 0 55px;
    margin: 0 39px 0 0;
}
table.campHistory {
    border-spacing: 0 15px !important;
    border-collapse: separate;
    border-spacing: 0 1em;
    margin: 0 auto;
    width: 100%;
}
table.campHistory tr td:first-child {
    border-top-left-radius: 17px;
    border-bottom-left-radius: 17px;
}
table.campHistory td {
    padding: 12px 27px;
}
table.campHistory tr td:last-child {
    border-top-right-radius: 17px;
    border-bottom-right-radius: 17px;
}
span.productImage {
    width: 50px;
    height: 50px;
    overflow: hidden;
    display: inline-block;
}
table.campHistory .soclDetail span.handelpletform img {
    width: 68px;
    height: 68px;
}
.campHistory td {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 30px;
    color: #404040;
}
.cunformBtn {
    font-family: 'Mulish';
    text-decoration: none;
    border: solid 1px #63C063;
    width: 100px;
    height: 35px;
    background: #63C063;
    border-radius: 10px;
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    color: #FFFFFF;
    transition: all 0.3s;
}
.padeextra p {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 700;
    font-size: 20px;
    line-height: 28px;
    color: #3E3E3E;
    margin-top: 31px;
}
.wizardForm .tasklistinput {
    justify-content: start;
    margin-left: auto;
}
div.StripeElement {
    border-radius: 10px;
    height: 46px;
    font-size: 14px;
    padding: 12px 12px;
    color: #1c2053;
    appearance: auto;
    background: #F9F9FC;
    border: 1px solid #EBEBFF;
    margin-bottom: 0;
    margin-top: 42px;
}
.padeextra a.cunformBtn {
    margin: 17px auto 0;
    display: inline-block;
    line-height: 34px;
    width: auto;
    padding: 0 31px;
    margin-top: 10px;
}
.padeextra {
    margin-top: 46px;
}
#confirmpopup .socialInformatin {
    margin: 21px 0 0;
}
.connectType>span {
    margin-left: 41px;
}
#confirmpopup input#review {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 20px;
    align-items: center;
    color: #FFFFFF;
    width: 130px;
    height: 35px;
    margin: 0;
}
#confirmpopup button#complaint {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 20px;
    display: flex;
    align-items: center;
    color: #FFFFFF;
    justify-content: center;
    width: 130px;
    height: 35px;
    margin: 0 0 0 10px;
}
.widthBtnPopup {
    width: auto;
    justify-content: center;
    margin-bottom: 30px;
}
#confirmpopup .soclIn {
    width: 455px;
    margin: 0 auto;
}
#confirmpopup .socialInformatin .soclIn {
    width: auto;
    margin: 0 14px 0 0;
}
div#confirmpopup .modal-dialog {
    max-width: 730px;
}
#confirmpopup .wizardForm {
    max-width: 493px;
}
.rate {
    height: 46px;
    padding: 0;
    display: inline-block;
}
.rate:not(:checked)>input {
    position: absolute;
    top: -9999px;
}
.rate:not(:checked)>label {
    float: right;
    width: 40px;
    overflow: hidden;
    white-space: nowrap;
    cursor: pointer;
    color: #ccc;
    height: 40px;
    margin-right: 8px;
    margin-top: 4px;
}
.rate>label:before {
    content: "";
    background: url(../images/star-blank.png);
    width: 41px;
    height: 40px;
    display: inline-block;
    background-size: contain;
    background-repeat: no-repeat;
}
.rate>input:checked~label:before {
    content: "";
    background: url(../images/star-fill.png);
    width: 41px;
    height: 40px;
    display: inline-block;
    background-size: contain;
    background-repeat: no-repeat;
}
.rate>input:checked~label {
    color: #ffc700;
}
.rate:not(:checked)>label:hover~label:before {
    content: "";
    background: url(../images/star-fill.png);
}
.rate>input:checked+label:hover,
.rate>input:checked+label:hover~label,
.rate>input:checked~label:hover,
.rate>input:checked~label:hover~label,
.rate>label:hover~input:checked~label {
    color: #c59b08;
}
.rate:not(:checked)>label:hover:before {
    content: "";
    background: url(../images/star-fill.png);
    width: 41px;
    height: 40px;
    display: inline-block;
    background-size: contain;
    background-repeat: no-repeat;
}
.ratingPopup label {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    line-height: 30px;
    display: block;
    align-items: center;
    color: #404040;
    margin: 0;
}
.ratingPopup textarea#review {
    width: 100%;
    height: 187px;
    background: #E2EFFF;
    border: 1px solid #EBEBFF;
    border-radius: 15px;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 30px;
    color: #AAAAAA;
    padding: 17px 26px;
    margin-top: 33px;
}
.socialComp>div {
    display: flex;
    width: 166px;
    flex-wrap: wrap;
}
div#venueTable_length {
    display: none;
}
div#venueTable_filter {
    display: none;
}
a.reviewEdit {
    color: #00BFFF;
}
#campaignId{
    font-size: 18px;
    color: #AD80FF;
}
a.campaignId {
    color: #00BFFF;
    font-weight: 700;
}
.standard-icon {
    width: 54px;
}
.userDetails span.handelpletform {
    margin-left: auto;
    margin-top: -8px;
    width: 50px;
}
#requestDialogue label.checkboxAllCheck span.selcc {
    border: solid 2px #1DF3F1;
    color: #fff !important;
    font-size: 18px !important;
    line-height: normal !important;
}
#requestDialogue .modal-dialog.modal-lg {
    max-width: 900px;
}
.customCheck input {
    position: absolute;
    width: 25px;
    height: 26px;
    margin: auto;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 1;
    opacity: 0;
}
.customCheck input~label {
    font-size: 14px;
    padding: 0 12px 0 30px;
    color: #1c2053;
    margin: 0;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 30px;
    color: #383838;
}
.customCheck input~label:before {
    width: 25px;
    height: 25px;
    left: 735px;
    top: 711px;
    border: 1px solid #868686;
    border-radius: 2px;
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto;
}
.customCheck {
    position: relative;
}
.customCheck input:checked~label:after {
    content: "\f00c";
    font-family: "Font Awesome 6 Pro";
    font-weight: 900;
    position: absolute;
    left: 4px;
    top: 1px;
}
#requestDialogue .customCheck {
    margin: 0;
}
#requestDialogue .tasklistinput {
    justify-content: start;
}
.campningDiv.indata {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    background: #FFFFFF;
    border: 1px solid #AD80FF;
    border-radius: 15px;
    margin: 16px 50px;
    padding: 20px 30px;
    justify-content: space-between;
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 30px;
    color: #404040;
    position: relative;
}
.campningDiv.indata>div:nth-child(4) {
    min-width: 180px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    min-width: 100%;
    padding: 0 10px;
}
.campningDiv.indata>div:nth-child(5) {
    margin-left: auto;
    min-width: 106px;
}
.campningDiv.indata>div:nth-child(5) a {
    text-decoration: none;
    color: #00BFFF;
}
.publishfirDaat~.firDaat {
    padding-left: 0px !important;
}
input#request_time {
    width: 60px;
    height: 52px;
    background: #F9F9FC;
    border: 1px solid #EBEBFF;
    border-radius: 15px;
    text-align: center;
    margin-right: 12px;
}
.wewPopup label.m-0.me-4 {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 23px;
    display: flex;
    align-items: center;
    color: #515151;
}
.popupbtn.complant-btn {
    max-width: 205px;
    padding: 0;
    margin-left: 10px;
}
label.checkboxAllCheck input~span.span41.d-block {
    background: #1DF3F1;
    border: solid 2px #1DF3F1;
    color: #fff;
    border-radius: 5px;
    font-size: 18px;
    line-height: normal;
    padding: 7px 18px;
}
.modal .selectedUserOuter .userDetails {
    flex: 0 0 262px;
    max-width: 262px;
    min-width: 262px;
}
.modal .flexUser {
    flex: 0 0 262px;
    max-width: 262px;
    min-width: 262px;
    margin: 15px;
}
.modal .flexUser .userDetails {
    width: 100%;
    max-width: unset;
    min-width: unset;
    margin: 0;
    max-height: unset;
    padding-bottom: 0;
    padding: 10px;
}
.modal .flexUserOuter {
    flex-wrap: nowrap !important;
    overflow-x: scroll;
    column-gap: 0;
}
[disabled] {
    opacity: 0.5;
    pointer-events: none;
}
.paymentPagenumber {
    max-width: calc(50% - 30px);
    flex: 50%;
    width: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 17px 38px;
    margin: 67px 0 0;
    background: #FFFFFF;
    border: 1px solid #AD80FF;
    box-shadow: 0px 4px 10px #AD80FF;
    border-radius: 20px;
    position: relative;
}
.paymentPagenumber h2 {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 700;
    font-size: 24px;
    line-height: 30px;
    color: #000000;
    margin-bottom: 19px;
    height: auto;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 25px 0 4px;
}
.ortContent {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 30px;
    text-align: center;
    color: #000000;
    display: flex;
    align-items: center;
    justify-content: center;
    letter-spacing: 1px;
    height: auto;
    max-width: 645px;
    margin: auto;
    padding: 38px 0 30px;
}
span.paymentLikeBtn {
    padding: 11px 20px;
    min-width: 95px;
    text-align: center;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 20px;
    color: #000000;
    background: #FFFFFF;
    border: 1px solid #AD80FF;
    border-radius: 20px;
    letter-spacing: 1px;
    width: 155px;
}
span.paymentLikeBtn.color-green {
    border: 1px solid #05E005;
}
.btnpor {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 800;
    font-size: 16px;
    line-height: 20px;
    align-items: center;
    margin-top: 20px;
    transition: all 0.3s;
    width: 220px;
    height: 46px;
    border-radius: 20px;
    letter-spacing: 1px;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-style: solid;
    border-width: 1px;
}
.bg-error {
    background-color: #AD80FF !important;
    border-color: #AD80FF !important;
}
.bg-error:hover {
    background-color: transparent !important;
    color: #AD80FF !important;
}
.rate.table>label:before,
.rate.table>input:checked~label:before {
    width: 35px;
    height: 33px;
}
.rate.table>label {
    border: 0;
    width: 35px;
}
.table.rate {
    width: 223px;
    margin: 0 auto;
    float: none;
    display: inline-block;
}
.displaylog {
    background: #F9F9FC;
    border: 1px solid #EBEBFF;
    border-radius: 15px;
    display: flex;
    padding: 13px 14px;
    margin-bottom: 18px;
}
span.priceon {
    margin-left: auto;
    font-weight: bold;
}
.mefia video,
.mefia img {
    width: 100%;
    max-height: 455px;
    max-width: 100%;
}
.acrinfCat.widthlo {
    flex: 0 0 54px;
}
.select_media.instagram,
.myDiv.instagram,
.instagram .oii {
    background: -webkit-radial-gradient(30% 107%, circle, #fdf497 0%, #fdf497 5%, #fd5949 45%, #d6249f 60%, #285AEB 90%);
    background: -o-radial-gradient(30% 107%, circle, #fdf497 0%, #fdf497 5%, #fd5949 45%, #d6249f 60%, #285AEB 90%);
    background: radial-gradient(circle at 30% 107%, #fdf497 0%, #fdf497 5%, #fd5949 45%, #d6249f 60%, #285AEB 90%);
    background: -webkit-radial-gradient(circle at 30% 107%, #fdf497 0%, #fdf497 5%, #fd5949 45%, #d6249f 60%, #285AEB 90%);
}
.select_media.twitter,
.myDiv.twitter,
.twitter .oii {
    background: #1DA1F2;
}
.select_media.facebook,
.myDiv.facebook,
.facebook .oii {
    background: #4267B2;
}
.select_media.tiktok,
.myDiv.tiktok,
.tiktok .oii {
    background: #010101;
}
.table>:not(:first-child) {
    border: 0 !important;
}
table .rate img {
    width: 34px;
}
.select_media.youtube,
.myDiv.youtube,
.youtube .oii {
    background: #ff0000;
}
.select_media.twitch,
.myDiv.twitch,
.twitch .oii {
    background: #6441a5;
}
.socialComp>div>div[data-tooltip]:hover:after {
    content: attr(data-tooltip);
    position: absolute;
    background: #000000cc;
    color: #fff;
    padding: 3px 8px;
    left: -31px;
    margin: auto;
    top: -19px;
    width: 77px;
    text-align: center;
    border-radius: 6px;
    text-transform: capitalize;
    font-size: 13px;
}
.socialComp>div>div[data-tooltip] {
    position: relative;
}
.tabsram .userDetails,
.tabsram .userDetails1 a span.handelname {
    color: #383838;
}
.socialComp>div>div[data-tooltip]:hover:before {
    content: "";
    position: absolute;
    top: 3px;
    border-style: solid;
    border-width: 4px 5px 0 5px;
    border-color: #000000cc transparent transparent;
    left: 4px;
}
.wizardForm .text-center img,
.wizardForm .text-center video {
    max-width: 100%;
    max-height: 500px;
}
.listshow {
    display: flex;
    background: #F9F9FC;
    border: 1px solid #EBEBFF;
    border-radius: 15px;
    padding: 0;
    margin-bottom: 35px;
    overflow: hidden;
}
.listshow>div {
    flex: 0 0 33.33%;
    flex-direction: column;
    display: flex;
    border-right: solid 1px #dee2e6;
}
.listshow>div>.listTitle {
    background: #AEE8FC80;
    width: 100%;
    border: oldlace;
    border-radius: 0;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 800;
    font-size: 16px;
    line-height: 24px;
    color: rgba(0, 191, 255, 1);
    border-bottom: solid 2px #00BFFF;
    padding: 7px 13px;
}
.userNameol {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 15px;
    white-space: nowrap;
    padding: 4px 13px 5px;
}
.listshow>div:last-child {
    border: 0;
}
.influncerList .wizardForm {
    max-width: 91%;
}
div[target="popup"] {
    text-align: center;
    font-weight: bold;
    color: #00BFFF;
    cursor: pointer;
}
.confirmBox {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    z-index: 1;
    width: 462px;
    height: 327px;
    display: none;
}
.influencer .modal-content .confirmBox .wizardHeading {
    padding: 0;
    margin: 0 0 24px;
    font-size: 23px;
    line-height: normal;
}
.influencer .modal-content .confirmBox .confirmText {
    text-align: center;
    color: #000000;
    font-family: 'Mulish', sans-serif;
}
.close-confirmation {
    color: #fff;
    background-color: rgb(237 0 0 / 80%);
    border-color: rgb(237 0 0 / 80%);
    font-weight: 500;
    height: auto;
    margin-left: auto;
    color: #fff;
    border-radius: 5px;
    font-size: 18px;
    line-height: normal;
    padding: 7px 18px;
    margin: 0 12px;
    transition: all 0.3s;
    text-decoration: none;
}
.close-confirmation:hover {
    background-color: transparent;
    color: rgb(237 0 0 / 80%);
}
.confirmationButton {
    display: flex;
    justify-content: center;
    margin: 32px 0 0;
}
.selectedUserOuter .confirmationButton a.btn.btn-danger {
    margin: 0 12px;
}
.confirmText:before {
    content: "\f129";
    position: relative;
    font-family: "Font Awesome 6 Free";
    font-weight: 800;
    width: 76px;
    height: 76px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: solid 1px;
    border-radius: 50px;
    font-size: 42px;
    color: rgb(237 0 0 / 80%);
    margin: 0 auto 25px;
}
.confirmOverlay {
    background: rgb(23 25 54 / 40%);
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}
.confirmInner {
    background: #fff;
    border: 1px solid #EBEBFF;
    border-radius: 15px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    z-index: 1;
    width: 462px;
    height: 327px;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}
.confirmationButton a.btn.btn-danger.remove {
    font-size: 18px;
    line-height: normal !important;
    background: #1DF3F1;
    border: solid 1px #1DF3F1;
    color: #fff;
    border-radius: 5px;
    padding: 7px 18px;
    display: block;
}
.confirmationButton a.btn.btn-danger.remove:hover {
    color: #1DF3F1;
    background: transparent;
}
.flexUserOuter a.removeButtonStyle {
    display: none;
}
.flexUserOuter .confirmBox {
    display: none !important;
}
.selectedUser label.checkboxAllCheck.d-block {
    display: none !important;
}
a.removeButtonStyle {
    font-weight: 500;
    height: auto;
    margin-left: auto;
    color: #fff;
    border-radius: 10px;
    font-size: 18px;
    line-height: normal;
    padding: 7px 18px;
    background: rgb(237 0 0 / 80%);
}
a.removeButtonStyle:hover {
    background: transparent;
    color: rgb(237 0 0 / 80%);
    border-color: rgb(237 0 0 / 80%);
}
.modal .selectedUser .removeButton {
    display: none;
}
span.acce,
span.reje,
span.pend {
    display: inline-block;
    width: 139px;
    text-align: center;
    margin-bottom: 6px;
    margin-top: -14px;
}
span.acce {
    color: #63C063;
}
span.reje {
    color: #FF0000;
}
span.pend {
    color: #00BFFF;
}
.wewPopup .timer {
    margin: 0 0 0 17px;
}
.campningDiv .timer img {
    margin: 0;
}
.authForm .timer .blueBtn.smallBtn.dactivet {
    background: #c5c5c5;
    border-color: #c5c5c5;
    color: #fff;
    cursor: default;
    pointer-events: none;
}
.userTime.ms-auto.w-auto .socialComp {
    grid-row-end: auto;
    display: flex;
    float: right;
}
.userTime.ms-auto.w-auto .socialComp>div {
    display: inline-block !important;
    width: auto;
}
.userTime.ms-auto.w-auto .socialComp>div>div {
    flex: 0 0 150px;
    float: left;
    width: 57px;
}
.page-item.active .page-link {
    background: #00BFFF;
    border-color: #00BFFF;
}
.wizardForm ul li>label.autowidth span {
    background: #E2F8FF;
    border-radius: 10px;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: normal;
    display: flex;
    align-items: center;
    color: #111111;
    padding: 20px 20px;
    margin: 0 !important;
}
body .wizardPopupOuter .select2-container--default .select2-selection {
    background: #F9F9FC;
    border: 1px solid #D1D1D1;
    border-radius: 10px;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 400;
    line-height: 23px;
}
.wizardPopup span.larg {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 800;
    font-size: 22px;
    line-height: 28px;
    display: flex;
    align-items: center;
    color: #353B5F;
    margin-bottom: 13px;
}
label.customInputRadio {
    width: 206px;
}
.wizardForm ul li label.autowidth input:checked~span,
.wizardForm ul li label.autowidth:hover span {
    background: #FF9180;
    color: #fff;
}
.lockText {
    text-align: center;
    font-size: 18px;
    color: #000;
    font-weight: 500;
    margin-bottom: 20px;
    margin-top: 33px;
    width: 678px;
    margin: 13px auto 17px;
}
.row.markeplaceUserList {
    margin: 0 -25px;
}
body .wizardPopupOuter span.select2 {
    max-width: 549px;
}
body .wizardPopupOuter span.select2 span.select2-selection {
    height: 52px;
    line-height: 50px;
    padding-top: 0;
    padding-bottom: 0;
}
.group-Data-left {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: normal;
    color: #424141;
    width: 100%;
    display: inline-block;
}
.group-Data-right {
    width: 100%;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 700;
    font-size: 12px;
    line-height: normal;
    color: #AD80FF;
    height: 16px;
}
.group-Data-right.drp {
    min-height: 27px;
    height: auto;
}
.multiData {
    display: inline-block;
    width: 100%;
}
.group-Data-right span {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 600;
    font-size: 12px;
    line-height: 15px;
    color: #000000;
    background: #E8EFF6;
    border-radius: 8px;
    padding: 4px 10px;
    margin: 3px 3px 0 0;
    display: inline-block;
}
span.infoPrice {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 700;
    font-size: 18px;
    line-height: 23px;
    align-items: center;
    color: #252525;
    float: right;
    padding: 7px 0 0;
}
label.checkboxAllCheck input~span .selcc {
    width: 130px;
    height: 37px;
    border-radius: 15px;
    border: 1px solid #3CC23C;
    font-style: normal;
    font-weight: 800;
    font-size: 13px;
    text-align: center;
    color: #FFFFFF;
    transition: all 0.3s;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    display: inline-flex;
    letter-spacing: 0;
    white-space: normal;
    line-height: 22px !important;
    cursor: pointer;
    background: #3CC23C;
}
label.checkboxAllCheck input:hover~span .selcc {
    color: #3CC23C !important;
    background: transparent;
}
.markeplaceUserSidebar {
    width: 190px;
}
.markeplaceUserList {
    display: flex;
    width: calc(100% + 20px);
    gap: 0 20px;
    margin: 0 -10px;
}
.markeplaceUserCheck {
    width: calc(100% - 190px);
    display: flex;
}
.divOnet {
    flex: 0 0 calc(72% - 10px);
    margin-right: auto;
}
.divSeco {
    flex: 0 0 calc(28% - 10px);
}
span.infoStar .fa-solid.fa-star {
    color: #FFB800;
}
label.checkboxAllCheck input~span .selcc:hover {
    background: transparent;
    color: #1DF3F1 !important;
}
.influncerUpdate .newInfluncer {
    font-family: 'Mulish';
    border-bottom: solid 1px #DBDBDB;
    padding: 0 0 6px;
    margin: 0 0 8px;
    font-style: normal;
    font-weight: 800;
    font-size: 18px;
    line-height: 25px;
    letter-spacing: 1px;
    color: #000000;
    padding-bottom: 11px;
}
.influncerUpdate>div {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 700;
    font-size: 15px;
    line-height: 28px;
    color: #424141;
}
span.allk {
    display: inline-block;
    width: 131px;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 15px;
    line-height: 28px;
    color: #424141;
}
.influncerUpdate a {
    display: block;
    width: 181px;
    margin-top: 16px;
    text-transform: uppercase;
    margin: 14px auto 7px;
}
div#pageLoader {
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    z-index: 111111;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    background: #ffffff1f;
    display: none;
}
div#pageLoader img {
    width: 54px;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    margin: auto;
}
.active~.menusal ul.subMenu {
    display: block;
}
.dashboardsidebar ul li ul.subMenu li:first-child a {
    margin-top: 0;
}
.dashboardsidebar ul li ul.subMenu li:last-child a {
    margin-bottom: 0;
}
.dashboardsidebar ul a.havesubmenu:after {
    content: "\f054";
    font-family: "Font Awesome 6 Pro";
    font-weight: 900;
    position: absolute;
    right: 16px;
    top: 18px;
    transition: all 0.3s;
    display: block;
}
.open .dashboardsidebar ul a.havesubmenu:after {
    display: none;
}
.dashboardsidebar ul a.havesubmenu.toggleMenu:after,
.dashboardsidebar ul a.havesubmenu.active:after {
    transform: rotate(90deg);
}
label.heading {
    font-family: 'Mulish';
    font-style: normal;
    line-height: 17px;
    display: flex;
    align-items: center;
    color: #353B5F;
    margin-bottom: 0;
}
.media-div {
    justify-content: start;
    border-radius: 10px;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 23px;
    display: flex;
    align-items: center;
    transition: all 0.3s;
    text-transform: capitalize;
}
.media-div img {
    width: 60px;
    position: static;
    left: 10px;
    top: 0;
    bottom: 0;
    margin: 0;
}
.admethod>span {
    width: auto;
    margin: 0;
    font-family: 'Mulish';
    padding: 0;
    transition: all 0.3s;
    display: inline-block;
    font-style: normal;
    font-weight: 600;
    font-size: 32px;
    line-height: 30px;
    color: #000000;
}
.admethod {
    position: relative;
    margin-left: 37px;
}
.media-div.facebook {
    background: #0f92e8d1;
}
.media-div.instagram {
    background: #1BAEDFd1;
}
.media-div.twitch {
    background: #8A43F2d1;
}
.media-div.youtube {
    background: #f20000d1;
}
.media-div.tiktok {
    background: #000000d1;
}
.media-div.snapchat {
    background: #FFFC00d1;
}
.hhsu {
    margin-top: 39px;
    margin-bottom: 27px;
    justify-content: center;
    align-items: center;
}
a.view-link {
    margin-left: auto;
    margin: 0 18px 16px 0;
    color: #00BFFF;
    font-weight: bold;
    text-decoration: none;
    display: inline-block;
}
.required_message {
    display: none;
}
.dd span.select2.select2-container .select2-selection {
    border-color: red;
}
div.required_message {
    padding: 0;
    margin: 0;
    font-size: 12px;
    color: red;
    list-style: none;
    position: absolute;
    bottom: -18px;
    white-space: nowrap;
}
.dd tags.tagify.form-control.required.inputtags.tagify--noTags.tagify--empty {
    border-color: red !important;
}
.sdee {
    display: inline-block;
    width: 100%;
    position: relative;
    padding: 0 15px;
}
.sdee label.autowidth {
    margin: 9px 8px;
}
.imageNvideo {
    width: 100%;
    flex: 100%;
    margin: 11px 0 !important;
    text-align: center;
    height: auto;
    max-height: unset;
    overflow: hidden;
    padding: 0 !important;
    max-width: 410px;
    margin: 0 auto !important;
}
.campningDiv i.fi.fi-sr-arrow-right {
    padding: 0 7px 0 0px;
    display: inline-block;
    position: relative;
    top: 2px;
    font-size: 15px;
    line-height: normal;
    height: 16px;
}
.sst {
    display: flex;
    align-items: center;
    margin-right: 14px;
}
span.input-group-text {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 700;
}
.selectedUserOuter a.btn.btn-danger.Rejected {
    background: transparent;
    color: #FF0000;
    border: 0;
    padding-left: 0;
    padding-right: 0;
    margin-bottom: 7px;
    pointer-events: none;
    cursor: default;
}
.selectedUserOuter a.btn.btn-danger.Selected {
    background: transparent;
    color: #63C063;
    border: 0;
    padding-left: 0;
    padding-right: 0;
    margin-bottom: 7px;
    pointer-events: none;
    cursor: default;
}
.paypal a,
.stripe a.calender-btn {
    background: rgb(39, 52, 105);
    background: linear-gradient(90deg, rgba(39, 52, 105, 1) 0%, rgba(38, 144, 194, 0.7) 100%);
    width: auto;
    border-radius: 5px;
    height: auto;
    font-size: 15px;
    color: #fff;
    font-weight: 700;
    transition: all 0.3s;
    padding: 10px 29px;
    margin-top: 14px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}
.stripe a.calender-btn {
    background: #635bff;
}
.order-history-layout .connectPrising.inner .accordion-item .accordion-header button.accordion-button {
    padding-right: 41px;
}
.authForm .campningDiv .camp-button input.redbigBtn {
    color: #fff;
    background: #FF0000;
    border-color: #FF0000;
    transition: all 0.3s;
}
.authForm .campningDiv .camp-button input.redbigBtn:hover {
    color: #FF0000;
    background: transparent;
}
.previous.disabled,
.next.disabled {
    display: none;
}
div.dataTables_wrapper div.dataTables_paginate ul.pagination {
    margin: 2px 0;
    white-space: nowrap;
    justify-content: flex-end;
}
.order-history-layout .acrtsdes {
    flex: 0 0 24%;
}
.imageNvideo img,
.imageNvideo video {
    max-width: 100%;
    height: 100%;
    object-fit: contain;
    max-height: 350px;
    max-width: 100%;
}
.linkDiv label {
    width: auto;
    margin-right: 11px;
    margin-bottom: 0;
    width: 100%;
}
.linkDiv {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 30px;
    color: #383838;
    display: flex;
    align-items: center;
    margin: 17px 0;
    flex-wrap: wrap;
    border: solid 1px rgba(0, 0, 0, .04);
    border-radius: 12px;
    padding: 7px 17px;
    background: rgba(0, 0, 0, .02);
}
.showlink {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 30px;
    color: #000000;
    padding: 0 10px 0 0;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}
.linkDiv a {
    margin-bottom: 0;
    display: inline-block;
    text-decoration: none;
    color: #00BFFF;
    margin-left: auto;
}
.rate.infl {
    pointer-events: none;
}
.select-media-long {
    margin: 0 -5px;
}
.select-media-long label {
    margin: 9px 8px;
}
.widauto.socialComp>div {
    width: auto;
    margin-right: 18px;
}
.customerSer {
    background: #FFFFFF;
    border: 1px solid #AD80FF;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 20px;
    padding: 38px 41px;
}
.paymentPagenumber.greenBox h2 {
    color: #05E005;
    letter-spacing: 5px;
    text-transform: uppercase;
}
.paymentPagenumber.greenBox {
    border: 1px solid #05E005;
    box-shadow: 0px 4px 10px #05E005;
}
.paymentPagenumber img {
    position: absolute;
    left: -22px;
    top: -29px;
    height: 76px;
}
.wizardHeadingOrt {
    text-align: center;
    font-size: 32px;
    line-height: 40px;
    font-weight: 700;
    padding-top: 11px;
}
.wizardHeadingOrt img {
    position: static;
    height: 57px;
}
.modal-body {
    padding: 0;
}
.modal-lg,
.modal-xl {
    max-width: 933px;
}
.chkImage img {
    position: static;
    width: 130px;
    height: auto;
    margin-top: 70px;
}
.ortContentntr {
    font-size: 28px;
    line-height: 35px;
    font-weight: 600;
    letter-spacing: 1px;
    color: #000000;
    padding: 40px 0 33px;
}
.payment-setup-div {
    background: #FFFFFF;
    border: 1px solid #AD80FF;
    border-radius: 20px;
    padding: 25px 35px;
    display: inline-block;
    min-width: 76%;
    height: auto;
    width: 921px;
}
.ps-title {
    font-style: normal;
    font-weight: 800;
    font-size: 31px;
    line-height: 40px;
    letter-spacing: 1px;
    margin-right: 22px;
}
.ps-subTitle {
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 1px;
    margin-top: 16px;
    display: inline-block;
    width: 100%;
}
a.anp {
    background: #FFFFFF;
    border: 1px solid #B9B9B9;
    border-radius: 15px;
    display: inline-block;
    font-style: normal;
    font-weight: 800;
    font-size: 13px;
    line-height: 16px;
    color: #8D8D8D;
    padding: 9px 15px;
    width: 157px;
    text-align: center;
    transition: all 0.3s;
}
a.anp.active {
    color: #00A800;
    border-color: #00A800;
}
a.anp.active:hover {
    background: #00A800;
    color: #fff;
}
.secTitleOne {
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 1px;
    font-weight: 800;
}
.secTitleTwo {
    font-size: 12px;
    line-height: 15px;
    font-weight: 500;
    letter-spacing: 1px;
}
.secTitle {
    margin-top: 51px;
}
.noaccountDetail {
    font-style: italic;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 1px;
    color: #676767;
    width: 533px;
    margin-top: 50px;
}
.accImg {
    display: flex;
    flex-direction: column;
}
span.accusername {
    background: #FFFFFF;
    border: 1px solid #AD80FF;
    border-radius: 15px;
    text-align: center;
    width: auto;
    height: 33px;
    font-style: normal;
    font-weight: 600;
    font-size: 12px;
    line-height: 15px;
    color: #AD80FF;
    line-height: 30px;
    margin-top: 8px;
}
.accountdetail {
    margin-top: 15px;
    align-items: center;
    width: 311px;
}
.removeacc {
    margin-left: auto;
}
tr.campningDiv table {
    width: 100%;
}
.connectPrising table td.taskName {
    font-style: normal;
    font-weight: 800;
    font-size: 18px;
    line-height: 8px;
    letter-spacing: 1px;
    color: #000000;
    padding-bottom: 11px;
}
td.taskName div {
    color: #000000;
}
.campningDiv .handelpletform img {
    width: 45px;
    height: auto;
    margin-right: 5px;
}
.campningDiv img {
    height: 22px;
    width: auto;
}
span.timing {
    font-style: normal;
    font-weight: 400;
    font-size: 2em;
    line-height: 16px;
    text-align: center;
    display: inline-block;
    background-color: #ffffff;
    max-height: 37px;
}
td.soclDetail {
    width: 150px;
}
td.firDaat {
    width: 150px;
}
.connectPrising table td.taskName div {
    display: inline-block;
}
span.story-type {
    max-width: 66px;
    white-space: normal;
    text-align: center;
    line-height: normal;
}
span.handelpletform {
    display: flex;
    align-items: center;
}
.custDetail .usernamewidth {
    width: 119px;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-width: unset;
    display: inline-block;
    margin-left: 11px;
    position: relative;
}
.soclDetail span {
    text-align: left;
    padding-left: 8px;
}
.connectPrising table td.soclDetail {
    width: 25%;
}
span.smalltext {
    font-style: normal;
    font-weight: 600;
    font-size: 10px;
    line-height: 13px;
    text-align: center;
    color: #474747;
    white-space: nowrap;
    letter-spacing: 0;
    line-height: normal;
    margin: -4px 0 0;
    display: inline-block;
    position: absolute;
    left: 0;
    bottom: -18px;
    height: 14px;
    width: 100%;
}
a.go-page-link {
    font-style: normal;
    font-weight: 800;
    font-size: 13px;
    line-height: 16px;
    text-align: center;
    color: #000000;
    filter: drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25));
    display: inline-block;
    border: 1px solid #9A9A9A;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 15px;
    padding: 0 9px;
    width: 168.42px;
    height: 37px;
    line-height: 35px;
    transition: all 0.3s;
    margin-top: 11px;
}
a.go-page-link:hover {
    color: #fff;
    background: #000000;
}
.no-data-div {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 7px 20px 30px;
}
.no-data-div img {
    width: 121.24px;
    margin-bottom: 54px;
    margin-top: 48px;
}
.no-data-contant {
    font-style: normal;
    font-weight: 600;
    font-size: 24px;
    line-height: 30px;
    letter-spacing: 1px;
    color: #000000;
    text-align: center;
}
.button-ccg {
    /* background: linear-gradient(97.12deg, #AD80FF 19.01%, #F86988 48.05%, #AD80FF 82.21%); */
    background: #AD80FF;
    border-radius: 12px;
    padding: 0 19px;
    font-style: normal;
    font-weight: 700;
    font-size: 15px;
    color: #FFFFFF;
    height: 44px;
    display: inline-block;
    line-height: 42px;
    transition: all 0.9s;
    margin: 0 auto;
}
a.button-ccg:hover {
    background: linear-gradient(97.12deg, #AD80FF 100%, #F86988 0%, #AD80FF 0%);
    color: #fff;
}
.no-data-div .button-ccg {
    margin-top: 63px;
    margin-bottom: 50px;
}
.no-data-div img.image2 {
    width: 84px;
    margin-top: 62px;
}
.table-btn {
    width: 130px;
    height: 44px;
    border-radius: 12px;
    border: 1px solid #AD80FF;
    font-style: normal;
    font-weight: 800;
    font-size: 13px;
    text-align: center;
    color: #FFFFFF;
    transition: all 0.3s;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    display: inline-flex;
    margin: 4px 0;
    letter-spacing: 0;
    white-space: normal;
    line-height: 34px;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}
.green-btn {
    background: #3CC23C !important;
    border-color: #3CC23C !important;
    color: white !important;
}
.green-btn:hover {
    background: transparent !important;
    color: #3CC23C !important;
}
.light-red-btn {
    background: #FF9180 !important;
    border-color: #FF9180 !important;
}
.light-red-btn:hover {
    background: transparent !important;
    color: #FF9180 !important;
}
.nobg-btn {
    color: rgba(0, 0, 0, 0.89);
    line-height: 33px;
    background: #fff;
}
.nobg-btn:hover {
    background: #000000;
    color: #fff;
}
.btn-div {
    display: flex;
    width: 324px;
    flex-wrap: wrap;
    margin: 0 -6px;
    flex-direction: row-reverse;
}
.red-btn {
    background: rgb(237 0 0 / 80%) !important;
    border-color: rgb(237 0 0 / 80%) !important;
}
.red-btn:hover {
    color: #ED0000 !important;
    background: transparent !important;
}
.ord {
    width: 155px;
    text-align: center;
    position: relative;
}
.nobg-btn.dactivet {
    font-weight: 900;
    background: #9A9A9A;
    align-items: center;
    white-space: normal;
    line-height: normal;
}
.nobg-btn.dactivet:hover {
    background: transparent;
    color: #9A9A9A;
}
.custDetail span.ctrb {
    border-radius: 5px;
    width: 35px !important;
    height: 34.92px !important;
    min-width: unset !important;
    display: flex !important;
    align-items: center;
    justify-content: center;
    font-style: normal;
    font-weight: 800;
    font-size: 14px;
    line-height: 18px;
    display: flex;
    align-items: center;
    text-align: center;
    color: #FFFFFF;
    margin: 0 7px 0 0;
}
span.ctrb.green-user {
    background: #26C826;
}
span.ctrb.red-user {
    background: rgba(255, 0, 0, 1);
    border-color: rgba(255, 0, 0, 1);
}
.custDetail div.influncercount {
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 20px;
    display: flex;
    align-items: center;
    letter-spacing: 1px;
    color: #000000;
    margin: 0 12px 0 0;
}
.accordion-item table {
    width: 100%;
}
.accordion-item .handelpletform img {
    width: 45px;
    height: auto;
    margin: 0 7px 0 0;
}
.accordion-button img {
    height: 22px;
    width: auto;
}
.connectPrising table td.camp-button.dropdown-button img {
    width: 40px;
    height: auto;
    margin-right: 15px;
}
.data-top-button ul {
    padding: 0;
    margin: 0;
    text-align: center;
    list-style: none;
    display: flex;
    justify-content: center;
    align-items: center;
}
.data-top-button ul a {
    background: #FFFFFF;
    border: 1px solid gray;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 15px;
    display: inline-block;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 18px;
    color: #000000;
    text-align: center;
    width: 138.01px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 11px;
    transition: all 0.3s;
}
.data-top-button ul a.all {
    background: #9A9A9A;
    color: #fff;
    border-color: #9A9A9A;
}
.data-top-button ul a.all:hover {
    color: #9A9A9A;
    background: transparent;
}
.data-top-button ul a.action {
    border: 1px solid #FF0000;
}
.data-top-button ul a.inprogress {
    border: 1px solid #AD80FF;
}
.data-top-button ul a.completed {
    border: 1px solid #63C063;
}
.data-top-button ul a.cancelled {
    border: 1px solid #FF0000;
}
.data-top-button ul a.inprogress:hover {
    background: #AD80FF;
}
.data-top-button ul a.completed:hover {
    background: #63C063;
}
.data-top-button ul a.cancelled:hover {
    background: #FF0000;
}
.data-top-button ul a.action:hover {
    background: #FF0000;
}
.data-top-button ul a:hover {
    color: #ffffff;
}
.data-top-button {
    margin: 18px 0 25px;
}
.data-table {
    width: 1086px;
    margin: 0 auto;
}
.data-table table {
    border-spacing: 0 8px !important;
    border-collapse: separate;
}
.data-table table .accordian-table td {
    border-top: solid 1px gray;
    border-bottom: solid 1px gray;
    padding: 14px 10px;
    width: auto;
}
.data-table table .accordian-table td:first-child {
    border-left: solid 1px gray;
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;
}
.data-table table .accordian-table td:last-child {
    border-right: solid 1px gray;
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
}
.data-table table .accordian-table td img {
    margin-right: 7px;
}
.data-table table .accordian-table.action td {
    border-color: rgba(254, 0, 0, 1);
}
.data-table table .accordian-table.inprogress td {
    border-color: rgba(246, 166, 20, 1);
}
.data-table table .accordian-table.completed td {
    border-color: rgba(99, 192, 99, 1);
}
.data-table table .accordian-table.cancelled td {
    border-color: rgba(210, 43, 43, 1);
}
.connectPrising .data-table td {
    font-size: 16px;
    font-weight: 800;
}
.accordian-table span.timing {
    width: 130px;
}
.accordian-table .table-btn {
    margin: 0;
}
.darkgray-btn {
    background: #DB5555;
    border-color: #DB5555;
}
.darkgray-btn:hover {
    background: transparent;
    color: #DB5555;
}
.connectPrising [aria-expanded="false"] table td.camp-button.dropdown-button img {
    transform: rotate(180deg);
}
.connectPrising .accordion-item table td {
    font-size: 14px;
    font-weight: 800;
}
.this-button {
    width: 289px;
    text-align: right;
}
.star-div {
    text-align: center;
    margin-top: 0;
    display: flex;
    justify-content: center;
}
.star-div img {
    width: 36px;
    height: auto;
}
a.table-btn.closer {
    padding: 0;
    width: auto;
    padding: 0;
    height: auto;
    border: 0;
    box-shadow: none;
    width: 28px;
}
a.table-btn.closer img {
    width: 28px;
    height: auto;
}
.drat {
    width: 155px;
}
.connectPrising table td.timer.green .soclPrice {
    color: rgba(99, 192, 99, 1);
}
.connectPrising table td.green img {
    filter: invert(62%) sepia(62%) saturate(374%) hue-rotate(71deg) brightness(96%) contrast(86%);
}
.connectPrising table td.timer.gray .soclPrice {
    color: #A2A2A2;
}
.connectPrising table td.gray img {
    filter: invert(70%) sepia(0%) saturate(0%) hue-rotate(139deg) brightness(88%) contrast(89%);
}
.stars .star-div img {
    width: 27px;
    height: auto;
}
.campningDiv img.absolutimage {
    position: absolute;
    right: 17px;
    top: 16px;
}
.modal-dialog.default-width {
    max-width: 817px;
}
.popup-title {
    font-size: 20px;
    line-height: 24px;
    font-weight: 800;
    letter-spacing: 1px;
    padding: 22px 0 0;
    text-align: center;
}
.popup-title-id {
    letter-spacing: 1px;
    font-size: 14px;
    line-height: 17px;
    font-weight: 800;
    padding: 7px 0 17px;
    text-align: center;
}
.nav-tabs .nav-link {
    border: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    flex: 1;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 16px;
    letter-spacing: -0.015em;
    color: #000000;
    font-family: 'Inter', sans-serif;
    font-family: 'Mulish', sans-serif;
    padding: 16px;
    cursor: pointer;
}
.nav-tabs {
    border: 0;
}
.nav-tabs.ordertab .nav-link span {
    font-family: 'Inter';
    font-family: 'Mulish', sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 150%;
    letter-spacing: -0.015em;
    color: #9A9A9A;
    padding-top: 7px;
}
.nav-tabs.ordertab .nav-link.active {
    border-radius: 0px;
    color: #AD80FF;
    opacity: 1;
    border-bottom: solid 3px #AD80FF;
}
.detail-table {
    border: 1px solid #AD80FF;
    border-radius: 20px;
    max-width: 590px;
    margin: 36px auto;
    padding: 18px 16px;
}
.detail-table .inside-table {
    width: calc(100% - 70px);
    margin: 0 35px;
}
.detail-table .inside-table .inside-table-row span:first-child {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 1px;
    color: #000000;
    text-align: left;
    padding: 8px;
    flex: 0 0 260px;
    display: inline-block;
}
.detail-table .inside-table .inside-table-row span:last-child {
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 1px;
    color: #000000;
    text-align: left;
    padding: 8px 4px;
}
.order-titles {
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 1px;
    color: #000000;
    margin-bottom: 8px;
    text-align: center;
}
.order-content {
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 1px;
    color: #000000;
    padding: 0;
    text-overflow: ellipsis;
    overflow: hidden;
    display: block;
    flex: 0 0 100%;
    width: 100%;
    /* height: 100%; */
    text-align: center;
}
.order-link {
    display: inline-block;
    border-style: solid;
    border-width: 1px;
    border-radius: 23px !important;
    max-width: 379px;
    text-align: left;
    position: relative;
    border: 1px solid #AD80FF;
    background: linear-gradient(45deg, #AD80FF, #F86988, #AD80FF) border-box;
    height: 42px;
    width: 100%;
}
.order-link .link {
    position: absolute;
    z-index: 8;
    background: #fff;
    padding: 10px 28px;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 20px;
    display: inline-block;
    align-items: center;
    letter-spacing: 1px;
    color: #2421AD;
    padding-right: 84px;
    border-radius: 23px;
    height: 40px;
    white-space: nowrap;
    overflow: hidden;
    width: 100%;
    text-overflow: ellipsis;
}
.copy-link {
    position: absolute;
    right: 0;
    z-index: 11;
    background: #AD80FF border-box;
    border-radius: 23px;
    height: 100%;
    font-style: normal;
    font-weight: 500;
    font-size: 13px;
    line-height: 16px;
    display: flex;
    align-items: center;
    text-align: center;
    letter-spacing: 1px;
    color: #000000;
    cursor: pointer;
}
.copy-link:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: calc(100% - 2px);
    height: calc(100% - 0px);
    background: #fff;
    border-radius: 20px;
    z-index: 1;
    border: 0;
    right: 0;
    margin: auto;
    opacity: 1;
    transition: all 0.3s;
}
.copy-link span {
    position: relative;
    z-index: 1;
}
.copytext:hover~.copy-link:before,
.copy-link:hover:before {
    opacity: 0;
}
.copytext:hover~.copy-link,
.copy-link:hover {
    color: #fff;
}

.order-content {
    display: flex;
    flex-wrap: wrap;
    gap: 8px; /* space between tags */
    align-items: center;
}

.order-hash-tag {
    display: flex;
    align-items: center;
    background: #f5f5f5;
    border-radius: 16px;
    padding: 4px 12px;
    font-size: 14px;
    margin: 0;
    white-space: nowrap;
    justify-content: center;
}

.order-hash-tag img {
    margin-right: 6px;
    width: 18px;
    height: 18px;
}

.order-accept-div {
    padding: 0 50px 34px;
    display: flex;
    max-width: 770px;
    margin: 0 auto;
}

.order-accept-text {
    font-style: normal;
    font-weight: 700;
    font-size: 20px;
    line-height: 25px;
    display: flex;
    align-items: center;
    letter-spacing: 1px;
    color: #000000;
}
.order-accept-buttons {
    margin-left: auto;
}
.accept-buttons {
    display: inline-flex;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 1px;
    color: #000000;
    flex-direction: column;
    text-align: center;
    margin: 0 18px;
    justify-content: center;
    align-items: center;
    border: 0;
    background: transparent;
    text-transform: capitalize;
}
.accept-buttons img {
    width: 40px;
    margin-bottom: 4px;
    border-radius: 50%;
    transition: all 0.3s;
}
.manreq-title {
    font-style: normal;
    font-weight: 800;
    font-size: 32px;
    line-height: 40px;
    color: #353B5F;
    padding: 30px 0 23px;
}
.manreq-text {
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    color: #353B5F;
    max-width: 65%;
    margin: 0 auto 26px;
}
.data-table.cac {
    max-width: 900px;
    text-align: left;
}
.data-set-user {
    width: 138px;
    margin: 0 auto;
}
.data-set-foll {
    width: 163px;
    margin: 0 auto;
}
.data-set-price {
    width: 126px;
    margin: 0 auto;
}
.data-set-user a,
.data-set-user span {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inline-block;
}
.influncerlist-dropdown button.accordion-button {
    background: rgb(255 245 243);
    border-radius: 20px !important;
    border: 1px solid #E8EFF6;
    display: flex;
    padding: 10px 14px;
    align-items: center;
}
.accordion-item {
    border: 0;
    box-shadow: none;
    border-radius: 20px !important;
    margin: 8px 0;
    width: 100%;
}
.img-to img {
    width: 24px;
    height: 24px;
}
.img-dropdown {
    margin-left: auto;
}
.dp-text {
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    color: #000000;
    padding-left: 26px;
}
.influncerlist-dropdown button.accordion-button:after {
    display: none;
}
.img-dropdown img {
    width: 29px;
    height: 29px;
    transform: rotate(180deg);
}
.influncerlist-dropdown button.accordion-button,
.influncerlist-dropdown button.accordion-button:focus {
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25) !important;
    z-index: 2;
    position: relative;
}
span.sertp {
    white-space: nowrap;
    max-width: 156px;
    overflow: hidden;
    text-overflow: ellipsis;
}
.extra-time-content p {
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 23px;
    color: #515151;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}
div.requesttime .modal-dialog {
    min-width: 730px;
}
.extra-time-content p span {
    color: #7ECB00;
    font-size: 48px;
}
.cont-suy {
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 23px;
    color: #000000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 56px 0 20px;
}
.cont-suy img {
    margin: 0 11px 0 0;
}
.extra-time-content p img {
    margin: 0 20px;
}
.et-submit {
    background: #AD80FF;
    border-radius: 12px;
    border: solid 1px #AD80FF;
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 44px;
    color: #FFFFFF;
    padding: 0 9px;
    display: inline-flex;
    min-width: 135px;
    height: 42px;
    transition: all 0.3s;
    margin: 30px 0 23px;
    text-align: center;
    white-space: pre-wrap;
    align-items: center;
    justify-content: center;
}
.et-submit:hover {
    color: #63CA5E;
    background: transparent !important;
}
.extra-time-content-bs {
    margin-bottom: 30px;
    text-align: center;
}
.extra-time-content {
    padding-top: 42px;
}
.accordion-item.open {
    border-radius: 22px !important;
    margin: 0;
    position: relative;
}
.accordion-item.open .accordion-header {
    border-radius: 21px;
    z-index: 2;
    position: relative;
}
.influncerlist-dropdown .accordion-collapse.show:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    border: 1px solid #9a9a9a45;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 20px;
    width: 100%;
    height: calc(100% + 51px);
    z-index: 0;
    margin-top: -51px;
}
div.accordion-collapse {
    position: relative;
}
.dr-row {
    display: flex;
    align-items: center;
    justify-content: center;
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    color: #353B5F;
    padding: 12px 0;
    position: relative;
}
.delete-inf {
    display: inline-block;
    margin-right: 13px;
    cursor: pointer;
}
.influencer-name,
.influencer-follo {
    margin: 0 0 0 45px;
}
.influencer-image {
    margin: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    flex: 0 0 40px;
}
.influencer-price {
    min-width: 139px;
    text-align: left;
}
.dr-row:after {
    position: absolute;
    content: "";
    background: #CCCCCC;
    height: 2px;
    width: 282px;
    border: 0;
    bottom: -1px;
    margin: 0 auto;
    left: 0;
    right: 7px;
    padding: 0 0;
}
.dr-row:last-child:after {
    display: none;
}
.dr-row:last-child {
    padding-bottom: 19px;
}
.influencer-name {
    width: 151px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 0 0 151px;
}
.influencer-follo {
    width: 154px;
    margin-left: auto;
}
.start-camp-btn {
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    background: #AD80FF;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 12px;
    font-style: normal;
    font-weight: 800;
    font-size: 14px;
    line-height: 18px;
    text-align: center;
    color: #FFFFFF;
    width: 160px;
    height: 44px;
    display: inline-flex;
    line-height: 39.09px;
    border: solid 1px #AD80FF;
    transition: all 0.3s;
    justify-content: center;
    align-items: center;
}
.start-camp-btn:hover {
    background: transparent;
    color: #63CE63;
}
.collapsed .img-dropdown img {
    transform: rotate(180deg);
}
ul.nav-tabs.ordertab li {
    flex: 0 0 50%;
}
ul.nav-tabs.ordertab li button {
    width: 100%;
}
.inside-table-row {
    display: flex;
}
.open .collapsed .img-dropdown img {
    transform: rotate(0);
}
.confirm-content .modal-lg,
.confirm-content .modal-xl {
    max-width: 1259px;
}
.media-data-inner {
    width: 60px;
    height: 60px;
}
.media-data-inner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.post-text .post-title {
    width: 332px;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: normal;
    white-space: nowrap;
}
.post-text {
    text-align: center;
    position: relative;
}
.post-link a {
    font-style: normal;
    font-weight: 600;
    font-size: 13px;
    line-height: 18px;
    text-align: center;
    color: #2094FF;
    height: auto;
    margin: 0;
}
.cunformBtn:hover {
    color: #63C063;
    background: transparent;
}
.post-link {
    line-height: normal;
    height: auto;
    position: absolute;
    left: 0;
    right: 0;
    top: 100%;
}
.button-text p {
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 20px;
    color: #3E3E3E;
    border-radius: 10px;
    margin-bottom: 22px;
}
.support-btn {
    border-radius: 10px;
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 20px;
    color: #FFFFFF;
    display: inline-block;
    padding: 11px 38px;
    transition: all 0.3s;
    border: solid 1px;
}
.button-text {
    margin: 23px 0 28px;
}
.picture-container {
    position: relative;
    cursor: pointer;
    text-align: center;
    width: 100%;
    display: flex;
    align-items: center;
    height: 100%;
}
.picture {
    width: 100%;
    height: 100%;
    background-color: #fff;
    color: #FFFFFF;
    margin: 0 auto;
    overflow: hidden;
    -webkit-transition: all 0.2s;
    -moz-transition: all 0.2s;
    -o-transition: all 0.2s;
    transition: all 0.2s;
}
.picture>.icon {
    width: 100%;
    height: 100%;
    display: flex;
    color: #37474F;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -o-border-radius: 50%;
    border-radius: 50%;
    -webkit-transition: all 0.2s;
    -moz-transition: all 0.2s;
    -o-transition: all 0.2s;
    transition: all 0.2s;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.picture:hover .icon {
    border-color: greenyellow;
}
.picture>.icon>svg {
    height: 1.4em;
    font-size: 4em;
}
.picture>svg {
    width: 100%;
    height: 100%;
    display: none;
}
.picture input[type="file"] {
    cursor: pointer;
    display: block;
    height: 100%;
    left: 0;
    opacity: 0 !important;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 100;
    right: 0;
    bottom: 0;
    background: #F9FAFB;
    border: 1px dashed #9CA3AF !important;
    border-radius: 4px;
}
.circ {
    opacity: 0;
    display: none;
    stroke-dasharray: 130;
    stroke-dashoffset: 130;
    -webkit-transition: all .75s;
    -moz-transition: all .75s;
    -ms-transition: all .75s;
    -o-transition: all .75s;
    transition: all .75s;
}
.tick {
    stroke-dasharray: 50;
    stroke-dashoffset: 50;
    -webkit-transition: stroke-dashoffset .4s 0.5s ease-out;
    -moz-transition: stroke-dashoffset .4s 0.5s ease-out;
    -ms-transition: stroke-dashoffset .4s 0.5s ease-out;
    -o-transition: stroke-dashoffset .4s 0.5s ease-out;
    transition: stroke-dashoffset .4s 0.5s ease-out;
}
.drawn>svg .path {
    display: block;
    opacity: 1;
    stroke-dashoffset: 0;
}
.drawn {
    border-color: #fff;
}
[data-toggle="popover"] {
    cursor: pointer;
}
span.popover-content-remove {
    padding-left: 10px;
    color: red;
    cursor: pointer;
    float: right;
}
.pb10 {
    padding-bottom: 10px;
}
.popover-header {
    text-align: center;
}
.popover {
    min-width: 200px;
}
.nav-tabs.filtertab .nav-link {
    border-color: #9A9A9A;
    background: #FFFFFF;
    border: 1px solid gray;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 15px;
    display: inline-block;
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 18px;
    color: #000000;
    text-align: center;
    width: 138.01px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 11px;
    transition: all 0.3s;
}
ul.nav-tabs.filtertab {
    justify-content: center;
    margin: 18px 0 25px;
}
.nav-tabs.filtertab .nav-link.active,
.nav-tabs.filtertab .nav-link:hover {
    background: #9A9A9A;
    color: #fff;
    border-color: #9A9A9A;
}
.nav-tabs.filtertab .nav-link.action {
    border-color: #FF0000;
}
.nav-tabs.filtertab .nav-link.action.active,
.nav-tabs.filtertab .nav-link.action:hover {
    background: #FF0000;
}
.nav-tabs.filtertab .nav-link.cancelled {
    border-color: #FF0000;
}
.nav-tabs.filtertab .nav-link.cancelled.active,
.nav-tabs.filtertab .nav-link.cancelled:hover {
    background: #FF0000;
}
.nav-tabs.filtertab .nav-link.complete {
    border-color: #63C063;
}
.nav-tabs.filtertab .nav-link.complete.active,
.nav-tabs.filtertab .nav-link.complete:hover {
    background: #63C063;
}
.nav-tabs.filtertab .nav-link.inprogress {
    border-color: #AD80FF;
}
.nav-tabs.filtertab .nav-link.inprogress.active,
.nav-tabs.filtertab .nav-link.inprogress:hover {
    background: #AD80FF;
}
.accept-buttons:hover img {
    box-shadow: 0 0 0;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.5);
}
.contact-support {
    max-width: 660px;
    margin: 0 auto;
}
.pause-campaign-text {
    padding: 0px 40px 0px 40px;
}
.pause-campaign-text li{
    padding-bottom: 15px;
}
body textarea.form-control {
    background: #FFFFFF;
    border: 2px solid #ABABB5;
    border-radius: 20px;
    max-width: 660px;
    height: 300px;
    padding: 8px 12px;
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 26px;
    color: #ACACAC;
    width: 100%;
    background: #FFFFFF;
    border: 2px solid #ABABB5;
    border-radius: 20px;
}
.text-center.wizardHeading-subheading {
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    color: #000000;
    margin-bottom: 18px;
}
.custom-file-picker {
    width: 660px;
    height: 113px;
    background: #F9FAFB;
    border: 1px dashed #9CA3AF;
    border-radius: 4px;
    margin-top: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin-bottom: 36px;
}
.custom-file-picker h4.info_text {
    display: none;
}
.popover-container.text-center {
    display: none;
}
.smaltext {
    font-family: 'Inter';
    font-family: 'Mulish', sans-serif;
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 14px;
    text-align: center;
    text-decoration-line: underline;
    color: #60A5FA;
}
.bigtext {
    font-family: 'Inter';
    font-family: 'Mulish', sans-serif;
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 12px;
    text-align: center;
    color: #4B5563;
    margin-top: 8px;
}
.form-border {
    border: 1px solid #AD80FF;
    border-radius: 20px;
    padding: 20px 47px 36px;
    margin: 15px 0;
    display: inline-block;
    width: 100%;
}
.confirm-submit .modal-dialog.modal-lg {
    max-width: 751px;
}
.confirm-submit .wizardForm {
    max-width: 559px;
}
label#content_display {
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 30px;
    color: #000000;
}
.form-border .widauto.socialComp.align-content-center {
    margin-top: 22px;
}
.connectPrising table td.soclDetail.ntr {
    max-width: 18%;
}
.modal .markeplaceRequest {
    margin-bottom: 35px;
}
.copytext {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 100%;
    z-index: 111;
    opacity: 0;
    cursor: pointer;
    width: 77px;
}
.collapsed img {
    cursor: pointer;
}
.accImg img {
    width: 169px;
}
.socialdetail {
    display: flex;
    align-items: center;
}
.socialimage {
    width: 90px;
    flex: 0 0 70px;
    margin-right: 13px;
}
.socialimage img {
    width: 100%;
    margin: 0 !important;
}
.ssn {
    max-width: 458px;
    margin: 37px auto 0;
    align-items: center;
}
.socialcontent {
    text-align: left;
}
.socialcontent-posttype {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 30px;
    color: #404040;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 258px;
}
.socialcontent-postlink a {
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 30px;
    color: #3733FF;
    white-space: nowrap;
    width: 258px;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0;
}
.ssn .socialComp>div>div {
    text-align: left;
}
.ssn .socialComp {
    margin: 0 -66px 0 auto;
}
.tasklist i.fas.fa-check {
    color: #1FDE00;
    font-size: 16px;
    margin-right: 23px;
}
.tasklists .bold-text {
    max-width: 469px;
    margin-top: -2px;
    padding-top: 0;
}
.et-submit.complant-btn {
    max-width: 212px;
    padding: 0;
    line-height: normal;
    image-rendering: inherit;
}
.modal-content .complaint-popup .wizardHeading {
    margin-top: 56px !important;
    text-transform: none;
    letter-spacing: normal;
    margin-bottom: 32px;
}
.complaint-popup .widthBtnPopup {
    max-width: 585px;
    margin: 0 auto;
}
.font-size-16 {
    font-size: 16px;
}
.complaint-popup .et-submit {
    margin-top: 50px;
    margin-bottom: 32px;
}
img.complaint-confirm-image {
    max-width: 150px;
    margin: 30px 0;
}
.complaint-confirm-popup .modal-lg {
    max-width: 913px;
}
.complaint-confirm {
    max-width: 736px;
    margin: 0 auto;
}
.influncerlist-dropdown button.accordion-button.collapsed {
    box-shadow: none !important;
}
.accordion-button .img-dropdown {
    transform: rotate(180deg);
}
.accordion-button.collapsed .img-dropdown {
    transform: rotate(0deg);
}
.taslListCustomCheckbox {
    position: relative;
}
.taslListCustomCheckbox input~div.tasklistContent:before {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    width: 18px;
    height: 18px;
    content: "";
    border: 1px solid #868686;
    border-radius: 2px;
}
.taslListCustomCheckbox input {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    opacity: 0;
    cursor: pointer;
    z-index: 3;
}
.taslListCustomCheckbox input~div.tasklistContent {
    padding-left: 28px;
    cursor: pointer;
}
.taslListCustomCheckbox input:checked~div.tasklistContent:after {
    content: "\f00c";
    font-weight: 900;
    font-family: "Font Awesome 6 Free";
    position: absolute;
    left: 2px;
    font-size: 16px;
    color: #868686;
}
.bold-text.margin {
    margin: 0 -9px;
}
.customcheckbox input {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    z-index: 1;
    cursor: pointer;
    width: 25px;
    height: 25px;
}
.customcheckbox {
    position: relative;
    display: flex;
    margin-right: 13px;
    align-items: center;
}
.customcheckbox input~label {
    width: 25px;
    height: 25px;
    margin: 0;
    border-radius: 2px;
    line-height: 25px;
    padding-left: 35px;
    font-family: 'Mulish';
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    color: #383838;
    text-transform: none;
}
.customcheckbox input~label i {
    font-size: 19px;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    align-items: center;
    justify-content: center;
    height: 25px;
    display: none;
    line-height: 25px;
    text-align: center;
}
.customcheckbox input:checked~label i {
    display: block;
    width: 25px;
    color: #AD80FF;
    font-weight: 900 !important;
}
.hide-check .customcheckbox {
    display: none;
}
.delete-inf:hover img {
    box-shadow: 0 5px 3px rgba(0, 0, 0, 0.2);
}
.delete-inf img {
    transition: all 0.3s;
    height: 18px;
}
.show-check .delete-inf {
    display: none;
}
.start-camp {
    position: relative;
    z-index: 1;
    margin: 50px 0 !important;
}
p.thank-title1 {
    font-style: normal;
    font-weight: 800;
    font-size: 36px;
    line-height: 45px;
    text-align: center;
    letter-spacing: 3px;
    text-transform: uppercase;
    color: #353B5F;
    margin-top: 21px;
}
p.thank-title2 {
    font-style: normal;
    font-weight: 500;
    font-size: 24px;
    line-height: 30px;
    text-align: center;
    color: #000000;
    margin: 27px 0 29px;
}
.text-transform-normal {
    text-transform: none !important;
    letter-spacing: normal !important;
}
p.review-thank-text {
    font-style: normal;
    font-weight: 600;
    font-size: 32px;
    line-height: 40px;
    text-align: center;
    color: #3A3A3A;
    max-width: 653px;
    margin: 0 auto;
}
.modal .et-submit {
    margin-top: 15px;
    margin-bottom: 30px;
}
.influencer-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.delete-inf.dsd {
    pointer-events: none;
}
.openpopinfu {
    margin: 0 8px 0 8px;
}
.openpopinfu img {
    width: 26px;
    height: 26px;
}
.connectPrising .camp-button.collapsed img {
    transform: rotate(180deg);
    transition: all 0.3s;
}
.connectPrising .camp-button img {
    transform: rotate(0deg);
    transition: all 0.3s;
}
img.colorblk {
    filter: brightness(0) invert(0);
}
.cancle-camp {
    background: #FE3628;
    border-radius: 12px;
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 1px;
    color: #FFFFFF;
    border: solid 1px #FE3628;
    height: 44px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
}
.cancle-camp:hover {
    background: transparent;
    color: #FE3628;
}
.droporg {
    text-align: center;
    font-style: normal;
    font-weight: 700;
    font-size: 24px;
    line-height: 30px;
    color: #353B5F;
    margin: 0 0 34px;
}
.ontro {
    padding: 0 55px 46px;
}
.ontro .dr-row {
    padding-left: 42px;
    padding-right: 0;
    width: 100%;
}
.ontotalcount {
    display: flex;
    flex-direction: row;
    padding-left: 0;
    padding-right: 0;
    font-style: normal;
    font-weight: 800;
    font-size: 16px;
    line-height: 20px;
    display: flex;
    align-items: center;
    color: #353B5F;
    margin: 0;
    justify-content: center;
}
.ontotalcount .influencer-follo {
    border-top: solid 2px #878787;
    padding-top: 12px;
}
.continue-link {
    background: #3CC23C;
    border-radius: 24px;
    width: 252px;
    height: 42px;
    display: inline-block;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 14px;
    color: #FFFFFF;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: solid 1px #3CC23C;
    transition: all 0.3s;
}
.continue-link-outer {
    margin-top: 53px;
    margin-bottom: 26px;
}
.continue-link:hover {
    color: #3CC23C;
    background: transparent;
}
.continue-link:hover img {
    filter: invert(30%) sepia(19%) saturate(7462%) hue-rotate(65deg) brightness(89%) contrast(79%);
}
.payment {
    display: flex;
    flex-wrap: wrap;
    color: #000000;
    margin: 0;
    width: 303px;
    color: #000;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}
.payment span {
    flex: 0 0 50%;
    margin: 0 0 9px;
}
.payment .d-flex {
    flex: 0 0 100%;
}
.payment .bold span {
    font-weight: 800;
}
.payment-one {
    align-items: center;
    justify-content: center;
    margin: 38px 0 0;
}
.payment-one img {
    height: 38px;
}
.payment-one a {
    display: inline-block;
    margin: 6px 20px;
}
.or-text {
    display: flex;
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    display: flex;
    align-items: center;
    color: #000000;
    justify-content: center;
    margin: 19px 0;
}
.or-text:after {
    width: 78.03px;
    content: "";
    height: 1px;
    background: #9a9a9a8c;
    margin-left: 15px;
}
.or-text:before {
    width: 78.03px;
    content: "";
    height: 1px;
    background: #9a9a9a8c;
    margin-right: 15px;
}
a.pay-pal-link {
    width: 252px;
    height: 42px;
    margin: 0 auto;
    display: flex;
    text-align: center;
    background: #F7C657;
    border-radius: 20px;
    align-items: center;
    justify-content: center;
    border: solid 1px #F7C657;
    transition: all 0.3s;
}
a.pay-pal-link img {
    height: 20px;
}
a.pay-pal-link:hover {
    background: transparent;
}
.payment-form {
    max-width: 600px;
    margin: 0 auto;
    flex-wrap: wrap;
    justify-content: space-between;
}
.half-field {
    width: calc(50% - 25px);
    display: flex;
    justify-content: space-between;
}
.full-field {
    flex: 0 0 100%;
}
.half-field .half-field {
    width: calc(50% - 5px);
}
.payment-form .input-group {
    margin-bottom: 22px;
}
.payment-form .customcheckbox input~label {
    width: auto;
}
.customcheckbox input~label:before {
    content: "";
    width: 25px;
    height: 25px;
    margin: 0;
    border: 1px solid #868686;
    border-radius: 2px;
    display: inherit;
    position: absolute;
    left: 0;
    top: 0;
}
.payment-form .customcheckbox {
    margin-top: 15px;
}
.text-center.continue-link-outer {
    width: 100%;
}
a.continue-link:hover img {
    filter: brightness(0);
}
.copy-link a span {
    padding: 4px 13px;
    line-height: 16px;
    height: 40px;
    line-height: 32px;
    color: #000000;
    display: inline-block;
}
.copy-link a:hover span {
    color: #fff !important;
}
.pointer-e-none {
    pointer-events: none;
}
div.continue-link-outer {
    justify-content: center;
}
.payment span.ms-auto {
    text-align: left;
    width: 80px;
    margin-left: auto;
    flex: 0 0 80px;
}
div#card-errors1 {
    color: red;
    font-size: 13px;
}
.btn-container {
    min-width: 324px;
    justify-content: center;
}
span#copylert {
    right: 0px;
    top: 0;
    width: 377px;
    text-align: center;
    line-height: inherit;
    height: auto;
    white-space: nowrap;
    background: #FFF5F3;
    border: 0;
    border-radius: 30px;
    color: #2421AD;
    position: absolute;
    line-height: 40px;
    padding: 0;
    font-size: 16px;
}
span#copylert:hover {
    color: #2421AD !important;
}
.stars.disabled .star-div img.fill {
    filter: brightness(0.1);
    opacity: 0.5;
    pointer-events: none;
}
.popover-container.text-center.show-file {
    display: flex !important;
    height: 100%;
    align-items: center;
    width: 100%;
    justify-content: center;
    z-index: 1111;
    position: relative;
    color: #000;
}
.popover-container.text-center.show-file p.btn-popover {
    margin: 0;
    font-weight: 700;
}
input.btn.btn-success.btn-xs.btn-file-view {
    width: auto;
    height: 37px;
    border-radius: 15px;
    border: 1px solid #9A9A9A;
    font-style: normal;
    font-weight: 800;
    font-size: 13px;
    text-align: center;
    color: #FFFFFF;
    transition: all 0.3s;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    display: inline-flex;
    margin: 5px 6px;
    letter-spacing: 0;
    white-space: normal;
    line-height: normal;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    background: #FF9180;
    border-color: #FF9180;
}
input.btn.btn-success.btn-xs.btn-file-view:hover {
    color: #FF9180;
    background: transparent;
}
.ratingPopup .disabled~label {
    pointer-events: none;
    opacity: 0.5;
}
.ratingPopup .rate>input.disabled:checked~label {
    filter: brightness(0.1);
}
.usernamewidth:hover:before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 41px;
    background: #AD80FF;
    color: #fff;
    padding: 6px 10px;
    left: 0;
    right: 0;
    width: fit-content;
    margin: auto;
    border-radius: 13px;
    z-index: 1;
    max-width: 167px;
    white-space: normal;
    text-align: center;
    line-height: normal;
    margin-left: -13px;
    margin-right: -13px;
    font-size: 12px;
}
.connectPrising .accordion-item {
    margin: 20px 0;
}
.usernamewidth:hover:after {
    content: "";
    position: absolute;
    border-style: solid;
    border-width: 8px;
    top: -10px;
    left: 0;
    right: 0;
    width: 0;
    margin: auto;
    border-color: #AD80FF transparent transparent;
    border-bottom: 0;
}
.mobile-menu {
    display: none;
}
td.show-pricing {
    white-space: nowrap;
}
.side-top {
    position: relative;
    z-index: 11;
    background: #fff;
}
.wizardForm ul li {
    position: relative;
}
.modal .steps.selectedUserOuter {
    box-shadow: none;
    margin-top: 0;
}
.custDetail .usernamewidth span {
    width: 100%;
    display: inline-block;
    overflow: hidden;
    min-width: unset;
    white-space: nowrap;
    text-overflow: ellipsis;
}
button.btn-view.btn-load-more {
    margin: 0 auto;
    display: inherit;
    background: #AD80FF;
    border-color: #AD80FF;
    width: 130px;
    height: 37px;
    border-radius: 15px;
    border: 1px solid #9A9A9A;
    font-style: normal;
    font-weight: 800;
    font-size: 13px;
    text-align: center;
    color: #FFFFFF;
    transition: all 0.3s;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    display: inherit;
    margin: 19px auto 2px;
    letter-spacing: 0;
    white-space: normal;
    line-height: 34px;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}
button.btn-view.btn-load-more:hover {
    color: #AD80FF;
    background: transparent;
}
.menudiv span.reqCont {
    position: absolute;
    right: 14px;
    background: red;
    font-size: 13px;
    min-width: 20px;
    height: 20px;
    bottom: 0;
    top: 0;
    margin: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    line-height: 20px;
    border-radius: 20px;
    padding: 2px;
}
.detailCrt {
    text-align: center;
    max-width: 90%;
    margin: 0 auto;
    border: solid 1px #FF9180;
    border-radius: 15px;
    font-size: 17px;
    font-weight: 700;
    line-height: 29px;
    padding: 10px;
    margin: 2em auto;
}
span#listFieldError ul.filled {
    background: #fff;
}
input.table-btn.finish-camp {
    width: 145px;
    margin-left: 0 !important;
    margin-right: 0 !important;
    margin-top: 9px !important;
    margin-bottom: 0 !important;
}
.vertical-bottom span.timing {
    margin-bottom: 5px;
}
.mobile-menu span.reqCont {
    position: absolute;
    right: -10px;
    top: -6px;
    top: 0;
    font-weight: 700;
    background: red;
    color: #fff;
    min-width: 18px;
    height: 18px;
    font-size: 11px;
    line-height: 14px;
    border-radius: 15px;
    padding: 2px;
    z-index: 1;
}
.taskName img {
    display: none;
}
.general-button {
    text-transform: capitalize;
    background: #AD80FF;
    border-radius: 10px;
    border: solid 1px #AD80FF;
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 40px;
    color: #FFFFFF;
    padding: 0 9px;
    display: inline-flex;
    min-width: 135px;
    height: 42px;
    transition: all 0.3s;
    text-align: center;
    white-space: pre-wrap;
    align-items: center;
    justify-content: center;
}
.general-button:hover {
    background: transparent;
    color: #AD80FF;
}
.media-data-inner video {
    width: 100%;
    height: 100%;
}
.saved-card {
    text-align: center;
    font-weight: 700;
}
.workingTimer.show-mobile {
    display: none;
    position: relative;
}
img.remove-card {
    width: 30px;
    margin: -7px 0 0 20px;
    display: inline-block;
    cursor: pointer;
}
table.connectPrising tr.campningDiv>td.accepted:last-child {
    border-right: 1px solid #3CC23C;
}
table.connectPrising tr.campningDiv>td.accepted {
    border: 1px solid #3CC23C;
}
table.connectPrising tr.campningDiv>td.accepted:first-child {
    border-left: 1px solid #3CC23C;
    box-shadow: 3px 4px 3px #3CC23C;
}
/* New step form */
.section-heading{
    text-align: center;
    color: #AD80FF !important;
    padding-bottom: 56px;
}
.section-button{
    padding-right: 49px;
    padding-bottom: 10px;
}
.new_card_row{
    padding-left: 40px;
    padding-right: 40px;
}
.steps-section {
    border-radius: 20px;
    background: #FFF;
    margin: 35px 20px 40px;
    padding: 24px 32px;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
}
.steps-icon {
    border-radius: 50px;
    border: 1px solid #3CC23C;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    cursor: pointer;
}
.steps-icon img {
    width: 24px;
    filter: invert(48%) sepia(6%) saturate(952%) hue-rotate(182deg) brightness(90%) contrast(86%);
}
.steps-cont {
    color: #4B5563;
    font-size: 12px;
   font-family: 'Inter', sans-serif;
   font-family: 'Mulish', sans-serif;
    font-style: normal;
    font-weight: 400;
    line-height: 12px;
    padding-top: 16px;
}
.steps-which {
    color: #1F2937;
    font-size: 16px;
   font-family: 'Inter', sans-serif;
   font-family: 'Mulish', sans-serif;
    font-style: normal;
    font-weight: 600;
    line-height: 16px;
    margin-top: 4px;
}
.steps-position {
    color: #4B5563;
    font-size: 12px;
   font-family: 'Inter', sans-serif;
   font-family: 'Mulish', sans-serif;
    font-style: normal;
    font-weight: 500;
    line-height: 12px;
    border-radius: 20px;
    background: #F3F4F6;
    padding: 4px 8px;
    display: inline-block;
    margin-top: 12px;
}
.steps-point:after {
    content: "";
    border-radius: 20px;
    background: #F3F4F6;
    position: absolute;
    left: 56px;
    top: 19px;
    height: 3px;
    width: calc(100% - 72px);
}
.steps-point {
    position: relative;
    flex: 0 0 calc(100% / 5);
    max-width: calc(100% / 5);
    pointer-events: none;
}
.steps-point:last-child:after {
    display: none;
}
img.step-icon-check {
    display: none;
    filter: none;
}
.completed img.step-icon-check {
    display: block;
}
.completed img.step-icon {
    display: none;
}
.completed .steps-icon {
    border-color: #10B981;
}
.inprogress .steps-icon {
    border-color: #AD80FF;
}
.inprogress .steps-icon img.step-icon {
    filter: brightness(0) saturate(100%) invert(30%) sepia(90%) saturate(5000%) hue-rotate(258deg) brightness(110%) contrast(105%);
    display: block;
}
.completed .steps-position {
    background-color: #10B981;
    color: #fff;
}
.inprogress .steps-position {
    background-color: #AD80FF;
    color: #fff;
}
.steps-position:after {
    content: "Pending";
}
.completed .steps-position:after {
    content: "Completed";
}
.inprogress .steps-position:after {
    content: "In Progress";
}
.steps-point.completed:after {
    background: #10B981;
}
.page_tab.new_steps {
    padding: 20px;
    position: relative;
}
.social_media_radio {
    border-radius: 20px;
    background: #FEFEFC;
    box-shadow: 0px 4px 12px 4px #73737366;
    flex: 0 0 calc(50% - 40px);
    margin: 20px;
    padding: 18px;
    display: flex;
    width: calc(50% - 40px);
    position: relative;
}
#new_steps0 .social_media .social_media_radio {
    filter: blur(2px);
    pointer-events: none;
}
.social_media_radio span.media_platform {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: start;
    position: static;
    padding-left: 43px;
}
.media_platform label img {
    max-width: 68px;
    max-height: 68px;
    margin-right: 23px;
    filter: grayscale(100%);
}
.media_platform label {
    color: #000;
    font-size: 20px;
    font-family: Mulish;
    font-style: normal;
    font-weight: 600;
    line-height: 21px;
    margin: 0;
    position: relative;
}
.media_platform label:before {
    content: "";
    border-radius: 12px;
    border: 2.4px solid #111;
    width: 24px;
    height: 24px;
    position: absolute;
    left: -40px;
    top: 0;
    bottom: 0;
    margin: auto;
}
.social_media_radio span.media_platform input {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    margin: auto;
    z-index: 1;
    opacity: 0;
    cursor: pointer;
}
.media_platform img {
    margin: 26px 0;
}
.media_platform_post_type .form-group {
    margin: 9px 0;
    flex: 0 0 50%;
    position: relative;
}
span.media_platform_post_type .form-group label {
    color: #fff;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    font-family: 'Manrope', sans-serif;
    margin: 0 !important;
    padding-left: 28px;
}
.media_platform_post_type .form-group label:before {
    content: "";
    width: 20px;
    height: 20px;
    border-radius: 12px;
    border: 2.4px solid #FFF;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    background: transparent;
}
span.media_platform_post_type .form-group input {
    position: absolute;
    top: 0;
    bottom: 0;
    margin: auto;
    height: 20px;
    width: 20px;
    opacity: 0;
    z-index: 1;
    cursor: pointer;
    left: 0;
}
.media_platform input:checked~label:after {
    content: "";
    width: 14px;
    height: 14px;
    background: #000;
    position: absolute;
    left: -35px;
    top: 0;
    bottom: 0;
    margin: auto;
    border-radius: 50%;
}
.campaign-type-content {
    /* background: linear-gradient(95.92deg, rgba(253, 155, 141, 0.95) 31.88%, rgba(248, 105, 136, 0.95) 55.38%, rgba(255, 128, 216, 0.95) 93.1%); */
    background:#AD80FF;
    filter: drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25));
    box-shadow: none;
    margin-top: 0;
    position: relative;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 0;
    width: 100%;
    border-radius: 20px;
}
.social_media_radio.campaign-type.commingsoon .campaign-type-content{
    /* background: #b4b4b4; */
    height: 100%;
    pointer-events: none;
    user-select: none;
    -moz-user-select: none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -o-user-select: none;
}
a:hover .campaign-type-content {
    background: linear-gradient(95.92deg, rgba(253, 155, 141, 0.60) 31.88%, rgba(248, 105, 136, 0.60) 55.38%, rgba(255, 128, 216, 0.60) 93.1%);
}
.campaign-type .social_media_radio.campaign-type:nth-child(odd) a:hover .campaign-type-content {
    background: conic-gradient(from 180deg at 50% 50%, rgb(253 155 141 / 60%) -18.75deg, rgb(255 128 216 / 60%) 61.87deg, rgb(248 105 136 / 60%) 189.38deg, rgb(253 155 141 / 60%) 341.25deg, rgb(255 128 216 / 60%) 421.87deg);
}
.campaign-type-text {
    color: #FFF;
    text-align: center;
   font-family: 'Inter', sans-serif;
   font-family: 'Mulish', sans-serif;
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: 59.023%;
    padding: 9px 0 16px;
}
.campaign-type-share-information {
    color: #FFF;
    text-align: center;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 59.023%;
    padding-top: 16px;
}
.comming-soon-text {
    color: #FFF;
    text-align: center;
    font-family: Mulish;
    font-size: 32px;
    font-style: normal;
    font-weight: 800;
    line-height: 59.023%;
    position: absolute;
    left: 0;
    right: 0;
    filter: none;
    top: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;
    -moz-user-select: none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -o-user-select: none;
}
.steps-section h2 {
    color: #353B5F;
    font-family: Mulish;
    font-size: 38px;
    font-style: normal;
    font-weight: 800;
    line-height: normal;
    margin: -18px 0;
    padding: 0 10px;
}
.social_media_radio.campaign-type.commingsoon {
    filter: blur(4px);
}
.position-relative.otrcm {
    flex: 0 0 calc(50% - 40px);
    margin: 20px;
    pointer-events: none;
}
.position-relative.otrcm .social_media_radio.campaign-type.commingsoon {
    margin: 0;
    width: 100%;
    height: 100%;
}
.social_media_radio.campaign-type {
    position: relative;
    display: inline-block;
    margin: 20px;
    padding: 0;
    box-shadow: none;
}
.position-relative.otrcm .campaign-type-content {
    margin: 0;
}
.get_type,
.media_platform_post_type {
    display: none;
}
.social_media_radio.active {
    border-radius: 20px;
    /* background: linear-gradient(236deg, rgba(205, 0, 119, 0.60) 16.65%, rgba(222, 15, 74, 0.60) 61.17%, rgba(252, 148, 22, 0.60) 93.56%); */
    /* box-shadow: 0px 4px 12px 4px #C6008D; */
    background:#AD80FF ;
     box-shadow: 0px 4px 12px 4px #AD80FF;
}
.active .media_platform label:before {
    border-color: #fff;
}
.active .media_platform input:checked~label:after {
    background: #fff;
}
.active .media_platform label {
    color: #fff;
}
span.media_platform_post_type {
    width: 40%;
    align-items: center;
    justify-content: start;
}
.active .media_platform label img {
    filter: none;
}
.social_media_radio.facebook.active {
    background: #4267B2;
    box-shadow: 0px 4px 12px 4px #4267B2;
}
.social_media_radio.youtube.active {
    background: rgb(255 0 0 / 70%);
    box-shadow: 0px 4px 12px 4px rgb(255 0 0 / 70%);
}
.social_media_radio.twitter.active {
    background: rgb(0 172 238 / 70%);
    box-shadow: 0px 4px 12px 4px #00acee;
}
.social_media_radio.tiktok.active {
    background: rgb(0 0 0 / 70%);
    box-shadow: 0px 4px 12px 4px rgb(0 0 0 / 70%);
}
.media_platform_post_type .form-group input:checked~label:after {
    width: 10px;
    height: 10px;
    background: #fff;
    content: "";
    position: absolute;
    left: 5px;
    top: 0;
    bottom: 0;
    margin: auto;
    border-radius: 50%;
}
.step-nevigationbutton>div>img {
    border-radius: 50px;
    background: #AD80FF;
    display: flex;
    width: 50px;
    height: 50px;
    padding: 7px 11px;
    justify-content: center;
    align-items: center;
}
.step-nevigationbutton {
    display: flex;
    justify-content: space-between;
    margin: 20px 20px 0;
    align-items: center;
    z-index: 111;
    position: relative;
}
.step-nevigationbutton>div {
    cursor: pointer;
    transition: all 0.3s;
    border-radius: 50%;
}
.step-nevigationbutton>div:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}
.campaign-type-content label {
    text-align: center;
    margin: 0;
    padding: 24px;
    cursor: pointer;
}
.social_media_radio input {
    position: absolute;
    opacity: 0;
}
.new-titles {
    border-radius: 15px;
    background: #AD80FF;
    max-width: 516px;
    height: 45px;
    width: 100%;
    margin: 0 auto;
    color: #FFF;
    text-align: center;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    display: flex;
    align-items: center;
    justify-content: center;
}
.form-shadow-box {
    border-radius: 20px;
    border: 1px solid #AD80FF;
    background: #FFF;
    box-shadow: 0px 4px 5px 2px #AD80FF;
    margin-top: 40px;
    margin-bottom: 25px;
    padding: 12px;
    margin: 20px 20px 40px;
}
.top-title {
    color: #000;
    font-family: Mulish;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 30px;
    margin-bottom: 20px;
}
.outer-task {
    display: inline-block;
    width: 100%;
}
.form-shadow-box.long-box {
    display: flex;
    justify-content: center;
}
.custom-task-list label {
    color: #383838;
    font-family: Mulish;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 27px;
    min-width: 320px;
    /* margin: 0 0 18px; */
    /* padding-left: 45px; */
    color: #383838;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px; /* 187.5% */
}
.custom-task-list {
    margin-bottom: 42px;
}
.custom-task-list label:before {
    width: 26.365px;
    height: 26.365px;
    border-radius: 2px;
    border: 1px solid #868686;
    content: "";
    position: absolute;
    left: 17px;
    top: 0;
    bottom: 0;
    margin: auto;
    border-radius: 6px;
    border: 1px solid var(--stepper-active, #AD80FF);
}
.form-check {
    position: relative;
    padding-left: 0;
    margin-bottom: 0;
}
.custom-task-list input:checked~label.form-check-label:after {
    content: "";
    font-family: "Font Awesome 6 Free";
    font-weight: 700;
    position: absolute;
    left: 18px;
    top: 0;
    /* background: url(../images/icons/icon-gray-check.png); */
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23AD80FF' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e") !important;
    width: 24px;
    height: 24px;
    background-repeat: no-repeat;
    background-position: center;
    bottom: 0;
    margin: auto;
}
.custom-task-list input {
    position: absolute;
    left: 17px;
    margin: 0 !important;
    width: 26.365px;
    height: 26.365px;
    z-index: 1;
    opacity: 0;
}
.custom-task-list.checked-list label i.fas.fa-check {
    width: 26.365px;
    height: 26.365px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 0;
    top: 0;
}
.custom-task-list.checked-list label:before {
    display: none;
}
.form-shadow-box.half-box {
    margin: 26px;
    width: calc(50% - 52px);
    padding: 33px 61px;
}
.task-options.new {
    display: flex;
    flex-wrap: wrap;
}
.task-options.new .new-titles {
    margin-bottom: 20px;
}
.task-options.new input.form-control {
    border-radius: 20px !important;
    border: 1px solid #D1D5DB;
    background: #FFF;
    /* color: #9A9A9A; */
    font-family: 'Inter', sans-serif;
    font-family: 'Mulish', sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
    padding: 0 13px;
}
.task-options.new input.form-control~span.input-group-text {
    background: transparent !important;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 99999;
}
.filepond--drop-label {
    border-radius: 10px;
    border: 1px dashed #AD80FF;
    background: #F9FAFB;
    height: 60px !important;
    min-height: unset !important;
}
a.filepond--credits {
    display: none;
}
.filepond--root.filepond.filepond--hopper {
    margin: 0 auto !important;
    width: 87%;
    height: 61px !important;
}
.form-shadow-box.half-box .form-group {
    margin: 0;
}
.filepond--root.filepond.filepond--hopper .filepond--drop-label label {
    color: #4B5563;
    text-align: center;
   font-family: 'Inter', sans-serif;
   font-family: 'Mulish', sans-serif;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 12px;
    display: flex;
    flex-direction: column-reverse;
    padding: 0;
}
.filepond--root.filepond.filepond--hopper .filepond--drop-label label span.filepond--label-action {
    color: #60A5FA;
    text-align: center;
   font-family: 'Inter', sans-serif;
   font-family: 'Mulish', sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 14px;
    text-decoration-line: underline;
    margin-bottom: 16px;
}
.tagify--outside {
    border: 0;
}
.tagify--outside .tagify__input {
    order: -1;
    flex: 100%;
    transition: .1s;
    border-radius: 10px;
    border: 1px solid #9A9A9A;
    background: #FFF;
    padding: 10px 12px;
    margin: 0;
    color: #9A9A9A;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
    margin-bottom: 14px;
}
.tagify--outside .tagify__input:hover {
    border-color: var(--tags-hover-border-color);
    width: 100%;
}
.tagify--outside.tagify--focus .tagify__input {
    transition: 0s;
    border-color: var(--tags-focus-border-color);
}
tags.tagify.tagify--outside {
    border: 0;
    margin-bottom: -25px;
    width: 100%;
}
tag.tagify__tag {
    border-radius: 10px;
    background: #AD80FF !important;
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    padding: 9px 3px;
    border: 0;
    padding-right: 8px;
    margin: 2px 13px 2px 0;
}
.tagify__tag>div::before {
    display: none;
}
.tagify--outside .tagify__tag__removeBtn {
    width: 18px;
    height: 18px;
    border: solid 2px #fff;
    color: #fff;
    font-size: 17px;
    justify-content: center;
    line-height: 11px;
    text-align: center;
}
.tagify__tag__removeBtn:after {
    height: 18px;
    min-width: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 14px;
}
span.tagify__tag-text {
    color: #FFF;
   font-family: 'Inter', sans-serif;
   font-family: 'Mulish', sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
    margin: 0;
}
tag.tagify__tag>div {
    padding: 0;
    width: auto;
}
span.tagify__tag-text:before {
    content: "#";
}
.influencer-filter button {
    border: solid 1px #AD80FF;
    border-radius: 15px;
    background: #AD80FF;
    width: 250px;
    height: 51px;
    flex-shrink: 0;
    color: #FFF;
    text-align: center;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    position: relative;
}
.influencer-filter button img {
    position: absolute;
    right: 10px;
}
.influencer-filter>img {
    height: 50px;
    margin-left: 32px;
}
.influencer-list {
    padding-top: 50px;
    padding-top: 0;
    display: flex;
    flex-wrap: wrap;
    /* margin: 0 -15px 43px; */
    justify-content: center;
    align-content: flex-start;
    width: 100%;
}
.take-list .form-group {
    margin: 0;
}
.influencer-detail {
    border-radius: 15px;
    background: #FFF;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.25);
    width: calc(20% - 30px);
    margin: 15px;
    padding: 10px;
    display: flex;
}
.influncerleft .influencer-image {
    width: 64px;
    height: 64px;
    flex: 0 0 64px;
    margin-bottom: 5px;
}
.influncerleft {
    width: 64px;
}
.influencer-flags {
    display: flex;
    justify-content: center;
}
.influencer-flags img {
    height: 16px;
    margin: 0 3px;
}
.user-in span {
    color: #424141;
    font-family: Mulish;
    font-size: 13px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    fill: #FFF;
    stroke-width: 1px;
    stroke: #AD80FF;
    border: solid 1px #AD80FF;
    text-align: center;
    border-radius: 13px;
    padding: 3px 5px;
    margin-bottom: 8px;
    text-overflow: ellipsis;
    overflow: hidden;
    transition: all 0.3s;
    width: 100%;
    white-space: nowrap;
    display: inherit;
}
.influncerright button {
    border-radius: 12px;
    background: #AD80FF;
    border: solid 1px #AD80FF;
    color: #FFF;
    text-align: center;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
    letter-spacing: -0.2px;
    width: 93.617px;
    height: 44px;
    flex-shrink: 0;
    margin: 0 auto;
    display: block;
}
.influncerright {
    padding-left: 13px;
    flex: 1;
    max-width: calc(100% - 64px);
}
.influencer-filter {
    padding-bottom: 15px;
    border-radius: 20px;
    background: #FFF;
}
.pagination ul {
    margin: 0;
    padding: 0;
    display: flex;
    list-style: none;
}
.pagination ul li a {
    color: #4B5563;
   font-family: 'Inter', sans-serif;
   font-family: 'Mulish', sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    padding: 3px;
    border-radius: 6px;
    border: 1px solid transparent;
    background: #FFF;
    min-width: 40px;
    display: inline-block;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.pagination ul li a.active {
    border: 1px solid #AD80FF;
    color: #000;
   font-family: 'Inter', sans-serif;
   font-family: 'Mulish', sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 16px;
}
.pagination ul li a svg {
    margin-right: 6px;
    margin-left: 6px;
}
.pagination ul li:last-child a,
.pagination ul li:first-child a {
    padding: 12px;
}
.step-nevigationbutton>div.pagination:hover {
    box-shadow: none;
    border-radius: 0;
}
.step-nevigationbutton>div.influencer-cart {
    /* background: linear-gradient(323.03deg, #AD80FF -29.21%, #F86988 43.78%, #AD80FF 70.61%); */
    background: #AD80FF;
    width: 90px;
    height: 90px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 114px;
    bottom: 0;
    z-index: 1;
}
.step-nevigationbutton .influencer-cart img {
    background: transparent;
    padding: 0;
    width: 44px;
    height: 44px;
    border-radius: 0;
}
span.influencer-cart-count {
    width: 23px;
    height: 23px;
    flex-shrink: 0;
    background: #FF0000;
    color: #FFF;
    text-align: center;
    font-family: 'Inter', sans-serif;
    font-family: 'Mulish', sans-serif;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 14px;
    top: 14px;
}
.influencer-filter:before {
    border-radius: 20px;
    background: rgba(0, 0, 0, 0.70);
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 111;
}
.filter-div {
    padding: 23px 12px;
    margin: 0;
    background: #fff;
    z-index: 1;
    position: relative;
    border-radius: 20px;
    background: #FFF;
    position: absolute;
    width: calc(100% - 78px);
    z-index: 111;
}
.filter-buttons {
    display: flex;
    padding-bottom: 15px;
}
.filter-buttons span.select2.select2-container.select2-container--default {
    border: solid 1px #AD80FF;
    border-radius: 15px;
    background: #AD80FF;
    height: 51px;
    flex-shrink: 0;
    color: #FFF;
    text-align: center;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    position: relative;
    width: 250px !important;
    margin: 0 33px;
}
.filter-buttons span.select2.select2-container.select2-container--default span.selection {
    height: 100%;
    background: transparent;
}
.filter-buttons span.select2.select2-container.select2-container--default span.selection span.select2-selection.select2-selection--single {
    background: transparent;
    border: 0;
    height: 100%;
}
.filter-buttons span.select2.select2-container.select2-container--default span.selection span.select2-selection.select2-selection--single span.select2-selection__rendered {
    height: 100%;
    padding: 0;
    line-height: 48px;
    color: #FFF;
    text-align: center;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
}
.filter-buttons span.select2.select2-container.select2-container--default span.selection span.select2-selection.select2-selection--single span.select2-selection__arrow {
    height: 96%;
    color: #fff;
}
.filter-buttons span.select2.select2-container.select2-container--default span.selection span.select2-selection.select2-selection--single span.select2-selection__arrow b {
    color: #fff;
    border-color: #fff transparent transparent transparent;
}
.select2-container--open .select2-dropdown {
    border-radius: 15px !important;
    overflow: hidden;
}
.select2-container--default .select2-search--dropdown .select2-search__field {
    border-radius: 15px;
}
.filter-div label {
    color: #424141;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}
.filter-form {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}
.filter-form .form-group {
    margin: 25px 15px;
    flex: 0 0 calc(20% - 30px);
}
button.button-reset-filter {
    background: #D22B2B;
    border-color: #D22B2B;
}
button.button-reset-filter img {
    top: 12px;
}
.filter-buttons span.select2.select2-container.select2-container--open span.selection span.select2-selection.select2-selection--single span.select2-selection__arrow b {
    border-color: transparent transparent #fff transparent;
}
.filter-form span.select2-selection.select2-selection--single {
    border-radius: 10px;
    border: 1px solid #EBEBFF;
    background: #F9F9FC;
    height: 35px;
}
.filter-form span.select2-selection.select2-selection--single .select2-selection__rendered {
    height: 100%;
    line-height: 33px;
    padding: 0 15px;
    text-transform: capitalize;
}
.filter-form .select2-container--default .select2-selection.select2-selection--multiple {
    height: 100%;
    line-height: 33px;
    padding: 0 15px;
    text-transform: capitalize;
    border-radius: 10px;
    border: 1px solid #EBEBFF;
    background: #F9F9FC;
    height: 35px;
}
.filter-form span.select2-selection.select2-selection--single span.select2-selection__arrow {
    height: 35px;
}
.filter-form .form-group input.filter-text {
    border-radius: 10px;
    border: 1px solid #9A9A9A;
    background: #FFF;
    display: inline-flex;
    height: 35px;
    padding: 0 15px;
    align-items: center;
    flex-shrink: 0;
    width: 100%;
    color: #ACACAC;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}
.filter-box-outer {
    display: none;
}
.influencer-filter.filter-button-outer:before {
    display: none;
}
.step-nevigationbutton>div.selected-influencer-box {
    box-shadow: 0px 4px 4px 0px #00000040;
    background: #FFFFFF;
    width: 557px;
    height: auto;
    border-radius: 12px;
    padding: 33px 15px;
    position: absolute;
    right: 110px;
    bottom: 41px;
    z-index: 0;
    display: none;
    cursor: default;
}
.selected-influencer-data {
    display: flex;
    justify-content: center;
    margin-top: 29px;
}
.selected-influencer-data .data-detail {
    border-radius: 20px;
    border: 1px solid #AD80FF;
    margin: 0 7.5px;
    padding: 10px 15px 20px;
    color: #252525;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}
.uj {
    width: 30px;
    height: 30px;
    margin-bottom: 2px;
}
.uj img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.step-nevigationbutton>div.selected-influencer-box.open {
    display: block;
}
.step-nevigationbutton>div>img.close_inf_box {
    background: transparent;
    padding: 0;
    width: 28px;
    height: 28px;
    position: absolute;
    right: 9px;
    top: 9px;
    cursor: pointer;
}
.selected-influencer-in-cart {
    display: flex;
    flex-wrap: wrap;
    max-height: 476px;
    overflow: auto;
}
.selected-influencer-in-cart .influencer-detail {
    width: calc(50% - 26px);
    margin: 6.5px 13px;
}
div.filter-overlay.open {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    border-radius: 21px;
    background: rgba(0, 0, 0, 0.70);
    cursor: default;
    z-index: 5;
}
.selected-influencer-in-cart .influncerright button {
    background: #D22B2B;
    border-color: #D22B2B;
}
.finishTask {
    color: #353B5F;
    font-family: Mulish;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    text-align: center;
}
.form-shadow-box.middle-box {
    margin-top: 24px;
    max-width: 768px;
    width: 100%;
    margin: 24px auto 0;
    padding: 30px 48px 15px 40px;
    position: relative;
}
.middle-box {
    width: 768px;
    margin: 0 auto;
}
.middle-box table {
    width: 100%;
}
.middle-box table th,
.middle-box table td {
    color: #353B5F;
    /* font-family: Mulish; */
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    padding: 9px 0;
}
.middle-box table thead th {
    border-bottom: solid 1px #e5e5e5;
}
.middle-box table thead th.td-finish-last {
    /* border-color: transparent; */
}
.finish-image {
    width: 40px;
    height: 40px;
}
.finish-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.middle-box table tfoot tr:first-child td {
    border-top: solid 1px #e5e5e5;
}
.middle-box table td.dark {
    color: #000;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 800;
    line-height: normal;
}
.middle-box table td {
    padding: 12px 0;
}
td.td-finish-las {
    border-color: transparent !important;
}
.middle-box table tfoot tr td {
    padding: 2px 0;
}
.terms-div.middle-box.text-center {
    color: #000;
    text-align: center;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    padding-top: 16px;
    max-width: 803px;
    width: 100%;
}
.submit-button-infl {
    text-align: center;
}
button.button-Request {
    border-radius: 12px;
    /* background: linear-gradient(167deg, #AD80FF 7.45%, #F86988 49.48%, #AD80FF 98.91%); */
    background: #AD80FF;
    border: 0;
    width: 220px;
    height: 44px;
    color: #FFF;
    font-family: Mulish;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    margin-top: 30px;
    margin-bottom: 10px;
}
td.remove-list img {
    cursor: pointer;
}
.this-steps .new_steps {
    display: none;
}
.inprogress .steps-icon img.step-icon-check {
    display: none;
}
ul.parsley-errors-list.filled {
    position: static;
    top: 0;
    right: 0;
    font-size: 13px;
    font-weight: 600;
    background: transparent;
    bottom: auto;
    padding: 0;
    border-top-left-radius: 12px;
    border-bottom-left-radius: 12px;
    border-top-right-radius: 20px;
}
.steps-point.inprogress,
.steps-point.completed {
    pointer-events: all;
    cursor: pointer;
}
span#error-select-social-media,
span#error-post-type {
    position: relative;
    text-align: center;
}
span#error-hashrags ul {
    margin: 7px 0 0;
}
.step_form_custom .disabled img.step-icon-check {
    display: block;
    filter: brightness(0.1) invert(0);
    opacity: 0.5;
}
.step_form_custom .current img.step-icon-check {
    opacity: 1;
    filter: none;
    filter: invert(27%) sepia(51%) saturate(1306%) hue-rotate(296deg) brightness(91%) contrast(120%);
}
.step_form_custom .current .steps-icon {
    border-color: #AD80FF;
}
span.numberTab {
    color: #4B5563;
    font-size: 12px;
   font-family: 'Inter', sans-serif;
   font-family: 'Mulish', sans-serif;
    font-style: normal;
    font-weight: 400;
    line-height: 12px;
    padding-top: 16px;
    display: block;
}
span.tabText {
    color: #1F2937;
    font-size: 16px;
   font-family: 'Inter', sans-serif;
   font-family: 'Mulish', sans-serif;
    font-style: normal;
    font-weight: 600;
    line-height: 16px;
    margin-top: 4px;
}
.ser_op.disabled::after {
    background: #F3F4F6;
}
.ser_op.current::after {
    background: #AD80FF;
}
.ser_op {
    position: relative;
    max-width: unset;
    cursor: pointer;
}
.ser_op:last-child {
    max-width: unset;
}
.ser_op:nth-child(1),
.ser_op:nth-child(2),
.ser_op:nth-child(3),
.ser_op:nth-child(4),
.ser_op:nth-child(5) {
    flex: 0 0 calc(20% - 11.4px);
}
.ser_op::after {
    background: #3CC23C;
    content: "";
    border-radius: 20px;
    position: absolute;
    left: 56px;
    top: 19px;
    height: 3px;
    width: calc(100% - 72px);
}
.connectWithInner.disconnectedToSocial {
    border-radius: 15px;
    border: 1px solid #AD80FF;
    background: #FFF;
}
a.soclBtn.connect.table-btn.green-btn {
    color: #FFF;
    font-family: Mulish;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    padding: 10px 20px;
    border-radius: 15px;
    background: #63C063 !important;
    box-shadow: none !important;
    width: 140px;
    line-height: 23px;
    display: inline-block;
    height: auto;
}
a.soclBtn.connect.table-btn.green-btn:hover {
    background: transparent !important;
    color: #63C063 !important;
}
a.soclBtn.disconnect.table-btn.red-btn {
    color: #FFF;
    font-family: Mulish;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    padding: 10px 20px;
    border-radius: 15px;
    background: #FF0000 !important;
    box-shadow: none !important;
    width: 140px;
    line-height: 23px;
    display: inline-block;
    height: auto;
    border-color: #FF0000;
}
a.soclBtn.disconnect.table-btn.red-btn:hover {
    color: #FF0000;
    background: transparent !important;
}
.disabled .steps-icon {
    border-color: #6B7280;
}
.step_form_custom img.step-icon-check {
    display: block;
}
.am-selected-media {
    text-align: center;
}
.am-selected-media ul.media-box {
    display: inline-flex;
    list-style: none;
    border-radius: 50px;
    border: 2px solid #AD80FF;
    background: #FEFEFC;
    padding: 15px 27px;
    justify-content: center;
    align-items: center;
    gap: 61px;
    min-width: 519px;
    margin-bottom: 20px;
}
.am-selected-media ul.media-box li {
    width: 40px;
    height: 40px;
    position: relative;
}
.am-selected-media ul.media-box li img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    filter: grayscale(168%);
}
.am-selected-media ul.media-box li.selected {
    filter: none;
}
.selected-media-action .d-flex {
    max-width: 892px;
    margin: 0 auto;
    justify-content: space-between;
}
.selected-media-action .social_media_radio.campaign-type {
    width: 100%;
    margin: 0;
}
.selected-media-action .campaign-type-content label {
    display: flex;
    align-items: center;
    padding: 17px;
    width: 367px;
    height: 129px;
}
.selected-media-action .campaign-type-content label .campaign-type-share-information {
    display: none;
}
.selected-media-action .campaign-type-content label img {
    width: 35px;
}
.selected-media-action {
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.selected-media-action .campaign-type-content {
    margin: 25px 0;
}
.selected-media-action .campaign-type-text {
    color: #FFF;
    text-align: center;
   font-family: 'Inter', sans-serif;
   font-family: 'Mulish', sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 59.023%;
    margin: 0;
    padding: 0 0 0 22px;
}
.selected-media-action .react-action-price-one.d-flex {
    display: inline-flex !important;
    align-items: center;
    padding: 0 31px;
}
.selected-media-action .react-action-price-one.d-flex .trf-image {
    border: solid 1px #63C063;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    padding: 5px;
    text-align: center;
}
.in-value {
    border-radius: 10px;
    border: 1px solid #3CC23C;
    background: #FFF;
    width: 156px;
    height: 35px;
    flex-shrink: 0;
    margin-left: 20px;
    text-align: center;
    align-items: center;
    display: flex;
    justify-content: center;
    color: #252525;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
}
.in-value input.width-dynamic.proba.dva {
    min-width: 53px;
    width: 53px;
    border: 0;
    padding: 0;
    margin-left: 6px;
    background-color: transparent !important;
}
.react-action-price-one.disable.d-flex {
    padding-right: 0;
}
.selected-media-action .react-action-price-one.d-flex .trf-image img {
    max-width: 24px;
    max-height: 24px;
}
.selected-media-action .campaign-type-content label:before {
    width: 44px;
    height: 24px;
    flex-shrink: 0;
    content: "";
    border-radius: 20px;
    position: absolute;
    right: 22px;
    background: #d1d1d1;
    transition: all 0.3s;
}
.selected-media-action .campaign-type-content input:checked~label:before {
    background: #05E005;
}
.selected-media-action .campaign-type-content label:after {
    content: "";
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    border-radius: 10px;
    background: #FFF;
    box-shadow: 0px 4px 6px -1px rgba(0, 0, 0, 0.10), 0px 2px 5px 0px rgba(0, 0, 0, 0.06);
    position: absolute;
    right: 43px;
    transition: all 0.3s;
}
.selected-media-action .campaign-type-content input:checked~label:after {
    right: 24px;
    background-image: url(../images/icons/mdi_tick.svg);
    background-repeat: no-repeat;
    background-position: center;
}
.selected-media-action .react-action-price-one.disable.d-flex .trf-image {
    background: rgba(154, 154, 154, 0.30);
    border-color: #9A9A9A;
}
.selected-media-action .react-action-price-one.disable.d-flex .in-value {
    background: rgba(154, 154, 154, 0.30);
    border-color: #9A9A9A;
    color: #9A9A9A;
}
.selected-media-action .react-action-price-one.disable.d-flex .in-value input {
    background: transparent;
    color: #9A9A9A;
}
.in-value input {
    color: #252525;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
}
.active-inactive label {
    color: #353B5F;
    font-family: Mulish;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    margin-right: 17px;
    margin-bottom: 0;
}
.active-inactive {
    display: flex;
    justify-content: center;
    margin-top: 60px;
}
.active-inactive-inner {
    display: flex;
    align-items: center;
    margin-bottom: 43px;
}
.active-inactive-input {
    position: relative;
    cursor: pointer;
}
.active-inactive-input input.ds {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    opacity: 0;
}
.input-label {
    border-radius: 15px;
    background: #CD2727;
    box-shadow: 0px 10px 20px 0px rgba(35, 170, 35, 0.10);
    width: 236px;
    height: 54px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
input:checked~.input-label {
    background: #63C063;
}
.input-label:before {
    content: "";
    position: absolute;
    right: 16px;
    background: #fff;
    width: 49px;
    height: 26px;
    border-radius: 14px;
}
.input-label:after {
    content: "";
    position: absolute;
    right: 43px;
    background: #CD2727;
    width: 16px;
    height: 16px;
    border-radius: 14px;
    transition: all 0.3s;
}
input:checked~.input-label:after {
    right: 20px;
    background: #63C063;
}
span.active-inactive-text {
    color: #FFF;
    font-family: Mulish;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    margin: 12px;
}
input:checked~.input-label span.active-inactive-text:before {
    content: "Online";
}
input~.input-label span.active-inactive-text:before {
    content: "Offline";
}
.publish-info {
    max-width: 545px;
    margin: 0 auto;
}
.publish-info .social_media_radio.campaign-type {
    width: 100%;
    margin: 0;
}
.publish-info .react-action,
.publish-info .influencer-detail {
    width: calc(50% - 15px);
}
.publish-info .campaign-type-content {
    margin: 0;
    padding: 9px 14px;
    height: 152px;
    align-items: unset;
    justify-content: unset;
}
.publish-info .campaign-type-share-information {
    display: none;
}
.publish-info .campaign-type-content label {
    padding: 0;
    display: flex;
    margin: 0;
    align-items: center;
}
.publish-info .campaign-type-content label img {
    width: 35px;
}
.publish-info .campaign-type-content label .campaign-type-text {
    padding: 0;
    color: #FEFEFC;
    text-align: center;
   font-family: 'Inter', sans-serif;
   font-family: 'Mulish', sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 59.023%;
    padding-left: 14px;
}
.publish-info .campaign-type-content .radio-content label {
    color: #FEFEFC;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    position: relative;
}
.publish-info .campaign-type-content .radio-content label:before {
    content: "";
    width: 24px;
    height: 24px;
    border-radius: 12px;
    border: 2.4px solid #FEFEFC;
    margin-right: 8px;
}
.publish-info .campaign-type-content .form-group {
    margin: 8px 4px;
    position: relative;
    cursor: pointer;
    display: inline-block;
}
.publish-info .campaign-type-content .radio-content input:checked~label:after {
    content: "";
    position: absolute;
    left: 5px;
    top: 0;
    bottom: 0;
    width: 14px;
    height: 14px;
    background: #fff;
    margin: auto;
    border-radius: 50%;
}
.publish-info .campaign-type-content .form-group input {
    width: 100%;
    height: 100%;
    z-index: 1;
    left: 0;
    opacity: 0;
}
.publish-info .influencer-detail {
    margin-top: 0;
    height: 148px;
}
.publish-info .influencer-detail button.select-button {
    opacity: 0;
    pointer-events: none;
}
.am-selected-media ul.media-box li input.active ~ img.icon {
    filter: none;
}
.am-selected-media ul.media-box li input {
    position: absolute;
    left: 0;
    top: 0;
    width: 0%;
    height: 0%;
    z-index: 2;
    cursor: pointer;
    opacity: 0;
}
.am-selected-media ul.media-box li input:disabled~img.icon {
    opacity: 0.4;
}
.selected-media-action .campaign-type-content input {
    opacity: 0;
}
.steps_con .step-nevigationbutton {
    margin: 38px -15px -10px;
}
#collection4 .am-selected-media ul.media-box {
    margin-bottom: 25px;
}
.final-step-outer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px;
}
.final-step {
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    align-items: center;
    border-radius: 20px;
    border: 1px solid #AD80FF;
    background: #FFF;
    box-shadow: 0px 4px 5px 2px #AD80FF;
    color: #353B5F;
    font-family: Mulish;
    font-size: 22px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    padding: 26px 90px;
    margin-bottom: 53px;
}
.final-step img.icon {
    margin: 0 0 17px;
    width: 185px;
}
button.button-ccg.new-style {
    border-radius: 40px;
    background: var(--gradient-look, linear-gradient(143deg, rgba(253, 155, 141, 0.95) 12.50%, rgba(248, 105, 136, 0.95) 46.09%, rgba(255, 128, 216, 0.95) 100%));
    width: 350px;
    height: 50px;
    flex-shrink: 0;
    color: #FFF;
    font-family: Mulish;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    border: 0;
    margin: 0;
}
.user-in span>span {
    border: 0;
    width: auto;
    display: inline-block;
    padding: 0 3px;
    margin: 0;
}
.active-inactive-input input[type="checkbox"] {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
    opacity: 0;
    z-index: 9;
}
.disabled-input .dva {
    opacity: 0;
}
.am-selected-media ul.media-box li input:checked ~ label img.active.icon, #collection4 .am-selected-media ul.media-box li img.active.icon {
    filter: none;
}
div#latest-influencer-lists {
    min-height: 500px;
    padding: 20px;
}
.statics-html {
    width: 88%;
    height: auto;
    margin: 0 auto;
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 18px 0 65px;
}
.statics-html:before {
    content: "";
    background: #AD80FF;
    width: 5px;
    height: 100%;
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;
    top: 0;
    border-radius: 7px;
}
.statics-html-text {
    color: #000;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    position: relative;
    width: 50%;
    margin: 10px 0;
    display: flex;
    align-items: flex-start;
}
.statics-html-text:before {
    content: "";
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    position: absolute;
    right: -8px;
    background: #AD80FF;
    border-radius: 50%;
    box-shadow: 0px 0px 10px #AD80FF;
    top: 1px;
    bottom: auto;
    margin: auto;
}
.statics-html-text:nth-child(even) {
    margin-left: auto;
    text-align: left;
    justify-content: end;
    flex-direction: row-reverse;
    margin-bottom: 37px;
}
.statics-html-text:nth-child(even):before {
    right: auto;
    left: -8px;
}
.statics-html-text.pls b:after {
    content: "";
    width: 20px;
    height: 20px;
    display: inline-block;
    background: url(../images/icons/mdi_plus-circle.svg);
    margin-left: 8px;
    margin-right: 8px;
}
.statics-html-text.min b:after {
    content: "";
    width: 20px;
    height: 20px;
    display: inline-block;
    background: url(../images/icons/mdi_minus-circle.svg);
}
span.timeline-date {
    position: absolute;
    right: -181px;
    top: 0;
    bottom: 0;
    margin: auto;
}
.statics-html-text:nth-child(even) span.timeline-date {
    left: -181px;
    right: auto;
}
.statics-html-text.min:nth-child(even) b:after {
    margin-right: 8px;
    margin-left: 8px;
}
.statics-html-date {
    color: #000;
    text-align: center;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    margin-bottom: 15px;
    padding-top: 47px;
}
.statics-html-text b {
    display: flex;
    align-items: center;
}
.statics-html-text:after {
    content: "";
    display: inline-block;
    border-bottom: dashed 1px #AD80FF;
    flex: 1;
    margin-top: 10px;
    min-width: 16px;
}
.your-score {
    text-align: center;
    margin-top: 60px;
}
.your-score-conternt {
    display: inline-flex;
    border-radius: 20px;
    background: #FFF;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.05);
    background: rgba(173, 128, 255, 1), rgba(248, 105, 136, 1), rgba(253, 155, 141, 1);
    background: linear-gradient(180deg, #AD80FF 0%, #F86988 46.88%, #AD80FF 100%);
    position: relative;
    display: flex;
    width: 393px;
    padding: 14px 93px;
    margin: 0 auto;
    color: #000;
    font-family: Mulish;
    font-size: 20px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    align-items: center;
}
.your-score-conternt:before {
    position: absolute;
    content: "";
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: #fff;
    width: calc(100% - 2px);
    height: calc(100% - 2px);
    margin: auto;
    border-radius: 19px;
}
.your-score-conternt span {
    position: relative;
    white-space: nowrap;
}
.your-score-conternt img {
    position: relative;
    margin-left: 14px;
}
.your-score strong {
    font-weight: 700;
}
.timeline-content {
    display: flex;
    justify-content: center;
    margin-top: 60px;
}
.timeline-content span {
    width: 40px;
    height: 40px;
    display: inline-block;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(180deg, #AD80FF 0%, #F86988 46.88%, #AD80FF 100%);
    border-radius: 50%;
    position: relative;
}
.trofy-box {
    display: flex;
    align-items: center;
    width: 491px;
    justify-content: space-between;
}
.timeline-content span:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    width: calc(100% - 2px);
    height: calc(100% - 2px);
    background: #fff;
    border-radius: 50%;
}
.timeline-content span img {
    position: relative;
}
.timeline-content span.score {
    width: auto;
    position: absolute;
    background: transparent;
    color: #000;
    text-align: center;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    bottom: -32px;
}
.timeline-content span.score:before {
    background: transparent;
    display: none;
}
.sort-progress {
    flex: 1;
    position: relative;
    height: 3px;
    background: rgba(154, 154, 154, 1);
    border-radius: 11px;
    margin: 0 10px;
}
.sort-progress span {
    position: absolute;
    left: 0;
    top: 0;
    height: 3px;
    width: 50%;
    background: rgba(253, 155, 141, 1);
    content: "";
    border-radius: 10px;
}
.sort-progress span:before {
    display: none;
}
.sort-progress span:after {
    content: '';
    width: 13px;
    height: 13px;
    position: absolute;
    right: -7px;
    top: -15px;
    background: url(../images/icons/logo-icon.png);
    background-size: contain;
    background-position: center;
}
.influncerdetailpopup .modal-header {
    border-radius: 20px 20px 0px 0px;
    background: linear-gradient(180deg, #AD80FF 3.94%, #F86988 25.49%, #AD80FF 63.69%);
    padding: 0;
    justify-content: center;
}
button.close-popup-button {
    position: absolute;
    right: 7px;
    top: 7px;
    background: transparent;
    border: 0;
    width: 28px;
    height: 28px;
    margin: 0 auto;
    padding: 0;
}
button.close-popup-button img {
    width: 100%;
    height: 100%;
}
.header-influencer-image {
    border-radius: 120px;
    background: url(<path-to-image>), lightgray 50% / cover no-repeat;
    box-shadow: 0px 5px 5px 0px rgba(0, 0, 0, 0.35);
    width: 120px;
    height: 120px;
    flex-shrink: 0;
    overflow: hidden;
}
.header-influencer-image img {
    width: 100%;
    height: 100%;
}
.modal-header-outer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.modal-header-content {
    position: relative;
    margin: 20px;
}
.header-influencer-flage {
    position: absolute;
    right: 0;
    top: 0;
    width: 30px;
}
.header-influencer-flage img {
    width: 100%;
}
.header-influencer-trophy {
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;
    width: 40px;
    height: 40px;
    flex-shrink: 0;
    background: #FFF;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    bottom: -20px;
}
.modal-header-username {
    color: #FFF;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    padding: 3px 0 9px;
}
.popup-hashtag span {
    color: #252525;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    padding: 10px 8px;
}
.popup-social-image a {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.popup-social-section {
    width: 805px;
    margin: 0 auto;
    border-bottom: solid 1px #BEBEBE;
    padding: 50px 0;
    flex-wrap: wrap;
    gap: 2%;
}
.popup-social-image img {
    max-width: 50px;
    max-height: 50px;
    object-fit: contain;
    width: 100%;
    height: 100%;
    margin-bottom: 12px;
}
.influncerdetailpopup .modal-lg {
    max-width: 1073px;
}
.latest-campaigns-title {
    color: #252525;
    font-family: Mulish;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    text-align: center;
    padding: 35px 0;
}
.latest-campaigns-box {
    border-radius: 20px;
    border: 1px solid #AD80FF;
    padding: 18px;
    margin-bottom: 79px;
    width: calc(33.33% - 40px);
    margin: 0 20px 79px;
}
.latest-campaigns-name {
    color: #252525;
    text-align: center;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    padding-bottom: 6px;
}
.latest-campaigns-date {
    color: #252525;
    text-align: center;
    font-family: Mulish;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    padding-bottom: 18px;
}
.latest-campaigns-stars {
    width: 211px;
    text-align: center;
    margin: 0 auto 17px;
    gap: 17px;
    display: flex;
    justify-content: space-between;
}
.latest-campaigns-stars img {
    width: calc(20% - 16px);
}
.latest-campaigns-text {
    color: #252525;
    text-align: center;
    font-family: Mulish;
    font-size: 16px;
    font-style: italic;
    font-weight: 300;
    line-height: normal;
}
.latest-campaigns-box-outer {
    display: flex;
    padding: 0 20px;
    justify-content: center;
    height: 258px;
}
.home-comming-soon {
    background: linear-gradient(78.29deg, rgba(173, 128, 255, 0.7) -14.87%, rgba(248, 105, 136, 0.7) 56.96%, rgba(252, 150, 140, 0.7) 87.56%);
    display: flex;
    justify-content: center;
    align-items: center;
    height: auto;
    overflow: hidden;
    position: relative;
    min-height: 100vh;
}
.home-comming-soon:before {
    content: "";
    background: rgba(254, 254, 252, 1);
    width: 1280px;
    height: 1280px;
    flex-shrink: 0;
    border-radius: 1280px;
    background: #FEFEFC;
    filter: blur(40px);
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
}
.home-comming-soon .container {
    position: relative;
}
.comming-soon-box .logo {
    text-align: center;
}
.home-heading {
    color: #212427;
    text-align: center;
    font-family: Mulish;
    font-size: 40px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    padding-top: 60px;
}
.comming-soon-box .logo img {
    width: 547px;
}
.comming-soon-loader {
    width: 857px;
    height: 68px;
    flex-shrink: 0;
    border-radius: 50px;
    border: 5px solid #212427;
    margin: 60px auto 113px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: end;
}
.comming-soon-loader:before {
    content: "";
    border-radius: 50px;
    background: #212427;
    width: 451px;
    height: 54px;
    flex-shrink: 0;
    position: absolute;
    top: 0;
    left: 3px;
    bottom: 0;
    margin: auto;
}
.custom-loader {
    color: #212427;
    text-align: center;
    font-family: Mulish;
    font-size: 24px;
    font-style: normal;
    font-weight: 800;
    line-height: 24px;
    width: calc(50%);
}
.comming-step-box {
    text-align: center;
    padding: 0 14px;
}
.comming-step-box img {
    max-height: 75px;
    margin-bottom: 34px;
}
.comming-step-box-heading {
    color: #212427;
    text-align: center;
    font-family: Mulish;
    font-size: 24px;
    font-style: normal;
    font-weight: 800;
    line-height: 24px;
    padding-bottom: 7px;
}
.comming-step-box-text {
    color: #212427;
    text-align: center;
    font-family: Mulish;
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
}
button.apply-btn {
    width: 286px;
    height: 62px;
    color: #fff;
    font-family: Mulish;
    font-style: normal;
    font-weight: 600;
    line-height: 14px;
    border: 0;
    text-align: center;
    transition: all 0.3s;
    border-radius: 12px;
    background: #AD80FF;
}
.comming-soon-box {
    padding: 80px;
}
button.apply-btn:hover {
    color: #fff;
    background: linear-gradient(94.49deg, rgba(253, 155, 141, 0.95) 100%, rgba(248, 105, 136, 0.95) 0%, rgba(173, 128, 255, 0.95) 0%);
}
.applicationform .modal-lg {
    width: 719px;
}
.application-heading {
    color: #212427;
    text-align: center;
    font-family: Mulish;
    font-size: 30px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    letter-spacing: 5px;
    padding: 0 0 24px;
}
.applicationform .modal-body {
    padding: 35px;
}
.application-text {
    color: #212427;
    text-align: center;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    padding-bottom: 30px;
}
.application-small-heading {
    color: #212427;
    text-align: center;
    font-family: Mulish;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    padding: 30px 0;
}
.applicationform .nav-tabs {
    justify-content: space-around;
    margin-bottom: 48px;
    display: none;
}
.applicationform .nav-tabs .nav-link {
    color: #212427;
    text-align: center;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    border-radius: 50px;
    border: 2px solid #AD80FF;
    background: #FEFEFC;
    width: 176px;
    height: 52px;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    text-transform: capitalize;
}
.applicationform .nav-tabs .nav-link.active {
    background: #AD80FF;
    color: #fff;
}
label.application-label {
    color: #1F2937;
    font-family: 'Inter', sans-serif;
    font-family: 'Mulish', sans-serif;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: 12px;
    text-transform: uppercase;
    margin-bottom: 14px;
    float: left;
    display: inline-block;
}
body .form-control.application-input {
    display: flex;
    padding: 8px 16px;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    align-self: stretch;
    border-radius: 10px;
    border: 1px solid #9A9A9A;
    color: #1F2937;
   font-family: 'Inter', sans-serif;
   font-family: 'Mulish', sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    width: 100%;
    margin-bottom: 0;
    height: 38px;
    margin: auto;
}
.ml-a {
    float: right;
}
.applicationform .form-group {
    margin-bottom: 21px;
    width: 298px;
}
.applicationform .form-group img {
    width: 40px;
    height: 40px;
    margin-right: 18px;
    object-fit: contain;
}
body .d-flex .form-control.application-input {
    flex: 1;
}
.applicationform .form-check label:before {
    display: flex;
    width: 20px;
    height: 20px;
    padding: 0;
    justify-content: center;
    align-items: center;
    gap: 10px;
    flex-shrink: 0;
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    border-radius: 2px;
    border: 1px solid #868686;
}
.applicationform .form-check label {
    margin: 0;
    padding: 0;
    position: relative;
    padding-left: 33px;
    color: #212427;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
}
.applicationform .form-check {
    padding: 0;
    line-height: 20px;
}
.applicationform .form-check input {
    position: static;
    margin: 0 !important;
    position: absolute;
    left: 0;
    top: 0;
    float: none;
    width: 20px;
    height: 20px;
    z-index: 11;
    opacity: 0;
}
.applicationform .form-group.d-flex {
    align-items: center !important;
    margin-top: 10px;
}
.applicationform .form-check input:checked~label:after {
    content: "";
    width: 12px;
    content: "\f00c";
    font-family: "Font Awesome 6 Free";
    font-weight: 700;
    position: absolute;
    left: 4px;
    top: 0px;
}
.application-send {
    color: #FEFEFC;
    text-align: center;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    width: 176px;
    height: 52px;
    flex-shrink: 0;
    border-radius: 50px;
    background: #63C063;
    border: solid 1px #63C063;
    text-transform: capitalize;
    transition: all 0.3s;
}
.application-send:hover {
    color: #63C063;
    background: transparent;
}
.icon-before .order-titles:before {
    content: "";
    width: 16.667px;
    height: 16.667px;
    display: inline-block;
    margin-right: 11px;
    background-image: url(../images/icons/blk-check.svg);
}
.icon-before .order-titles {
    display: flex;
    justify-content: start;
    align-items: center;
    color: #000;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 1px;
    gap: 19px;
}
a.download-link {
    border-radius: 20px;
    display: inline-flex;
    width: 196px;
    height: 34px;
    padding: 0px 61px;
    justify-content: center;
    align-items: center;
    color: #000;
    text-align: center;
    font-family: Mulish;
    font-size: 13px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 1px;
    border: 1px solid transparent;
    background: linear-gradient(45deg, #AD80FF, #F86988, #AD80FF) border-box;
    position: relative;
}
a.download-link:before {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 20px;
    transition: all 0.3s;
}
a.download-link:hover:before {
    width: 0;
}
a.download-link:hover {
    color: #fff;
}
.am-selected-media.not-active,
.publish-last-div.not-active {
    opacity: 0.5;
    pointer-events: none;
}
main.mobile {
    display: block;
    margin-top: 0;
    padding: 94px 0 50px;
    background: #fff;
    min-height: calc(100vh - 136px);
}
.home-text {
    color: #353B5F;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    padding-bottom: 20px;
}
.pagesections {
    text-align: center;
    border-bottom: 1px solid #CACACA;
    background: #FFF;
    padding: 30px 17px 60px;
}
.home-text.font-bold {
    color: #353B5F;
    text-align: center;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    letter-spacing: 2px;
}
a.home-button {
    color: #FFF;
    text-align: center;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    border-radius: 20px;
    /* background: linear-gradient(148deg, #AD80FF 5.21%, #F86988 45.31%, #AD80FF 91.15%); */
    background: #AD80FF;
    display: inline-block;
    width: 227px;
    height: 50px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    /* border: solid 2px transparent; */
    transition: all 0.3s;
    margin-top: 15px;
}
a.home-button:hover {
    border-color: #AD80FF;
    background: transparent;
    color: #000;
    border-width: 2px;
    border-style: solid;
    transition: all 0.3s;
}
main.mobile .container {
    padding: 0;
}
.faqHeading {
    border-radius: 15px;
    background: #AD80FF;
    width: 319px;
    height: 50px;
    margin: 0 auto;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #FFF;
    text-align: center;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
}
.mobile button.accordion-button {
    padding: 0;
    color: #353B5F;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    background: transparent;
    box-shadow: none;
    display: flex;
    align-items: start;
}
/* .mobile div.accordion {
    width: 325px;
    margin: 48px auto 0;
    text-align: left;
} */
.mobile button.accordion-button:before {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    flex: 0 0 20px;
    margin-right: 12px;
    background: url(../images/icons/simple-line-icons_minus.png);
    background-size: cover;
}
.mobile button.accordion-button.collapsed:before {
    background-image: url(../images/icons/simple-line-icons_plus.png);
}
.mobile button.accordion-button:after {
    display: none;
}
.mobile .accordion-item {
    margin-bottom: 25px;
    margin-top: 0;
}
.pagesections:last-child {
    padding-bottom: 0;
    border: 0;
}
.disabled-input .in-value {
    background: rgba(154, 154, 154, 0.30);
    border-color: #9A9A9A;
    color: #9A9A9A;
}
.custom-task-list.checked-list label, .custom-task-list label {
    pointer-events: none;
    margin: 0;
    z-index: 1;
    padding: 13px;
    padding-left: 55px;
}
.checked-list .form-check, .custom-task-list .form-check {
    /* background: linear-gradient(113.77deg, rgba(255, 145, 128, 1) 1.45%, rgba(173, 128, 255, 0.7) 50.18%); */
    background: #AD80FF;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 22px;
    position: relative;
}
.popup-social-image.green-circle {
    border: solid 2px #AD80FF;
    border-radius: 25px;
    /* box-shadow: 0 5px 43px 5px rgba(0, 128, 0, 1) inset;
    -webkit-box-shadow: 0 5px 43px 5px rgba(0, 128, 0, 1) inset; */
    /* -moz-box-shadow: 0 5px 43px 5px rgba(0, 128, 0, 1) inset; */
    margin: 10px 10px;
}
.popup-social-image {
    border: solid 2px transparent;
    padding: 14px 20px 7px;
}
.popup-social-image span {
    color: #252525;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}
.user-in span.user_name {
    cursor: pointer;
}
.user-in span.user_name:hover {
    background: #AD80FF;
    color: #fff;
}
/* .influencer-detail.selected {
    pointer-events: none;
    opacity: 0.5;
} */
.userDetails span.handelpletform img {
    width: 100%;
}
.ntfMenu img {
    width: 24px;
    height: 24px;
}
span.mobile-text {
    display: none;
}
.step_form_custom .steps-section, .step_form_custom .steps_con {
    margin-left: 0;
    margin-right: 0;
}
.connectPrising .form-check input[type=radio] ~ label.form-check-label:before {
    content: "";
    width: 20px;
    height: 20px;
    display: inline-block;
    background-image: url(../images/icons/black-radio-blank.svg);
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    margin-right: 7px;
}
.connectPrising .form-check input[type=radio]:checked ~ label.form-check-label:before {
    background-image: url(../images/icons/black-radio-fill.svg);
}
.connectPrising .form-check input {
    /* position: absolute; */
    /* left: -9999px; */
}
.load-more__btn-wrap {
    width: 155px;
    text-align: center;
    position: absolute;
    bottom: 18px;
    left: 0;
    right: 0;
    margin: auto;
    z-index: 11;
    display: inline-block;
}
.load-more__btn-wrap a {
    color: #4B5563;
   font-family: 'Inter', sans-serif;
   font-family: 'Mulish', sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    padding: 3px;
    border-radius: 0;
    border: 2px solid transparent;
    background: #FFF;
    min-width: auto;
    display: inline-block;
    height: auto;
    transition: all 0.3s;
    width: auto;
}
.step-nevigationbutton>div.pagination {
    display: none;
}
.load-more__btn-wrap a:hover {
    border-bottom-color: black;
}
.statics-html .load-more__btn-wrap {
    bottom: -35px;
}
.mobile-step-detail {
    display: none;
}
.filter-form input#amount-price {
    height: 100%;
    line-height: 33px;
    padding: 0 15px;
    text-transform: capitalize;
    border-radius: 10px;
    border: 1px solid #EBEBFF;
    background: #F9F9FC;
    height: 35px;
}
span#card_number {
    color: red;
    font-size: 13px;
    font-weight: 600;
}
.user-in span > span {
    display: initial;
    line-height: 20px;
}
input.form-control.iconlast {
    padding-right: 42px !important;
}
span#required-task-error, .alert-select-option {
    font-size: 13px;
    font-weight: 600;
    color: red;
    list-style: none;
    line-height: 14px;
}
.tagify--outside ~ ul.parsley-errors-list {
    display: none;
}
.tagify--outside.tagify--noTags ~ .tagify--outside ~ ul.parsley-errors-list {
    display: block;
}
.tagify--outside ~ .tagify--outside ~ ul.parsley-errors-list.filled {
    padding-top: 13px;
}
.alert-select-option.mb-3 {
    top: 11px;
    left: 0;
    margin: 0 auto 0 !important;
    right: 0;
    text-align: center;
}
#latest-influencer-lists .influencer-list.load-more {
    justify-content: start;
}
.step-nevigationbutton>div.submit-button-infl:hover {
    box-shadow: none;
}
.trofy-box .sort-progress.fir:last-child {
    display: none;
}
.selected-influencer-in-cart .user-in span.user_name {
    pointer-events: none;
}
input.complaint-btn {
    font-style: normal;
    font-weight: 600;
    font-size: 12px;
    text-align: center;
    white-space: pre-wrap;
    letter-spacing: 0;
    line-height: normal;
    display: inline-block;
    left: 0;
    bottom: -18px;
    height: auto;
    padding: 0;
    margin: 0;
    border: 0;
    color: rgb(237 0 0 / 80%);
    font-weight: 700;
    background: transparent;
}
.sort-progress.progress_done {
    background: rgba(253, 155, 141, 1);
}
span.sertp[data-name-tooltip="Share Content from Customer - Post/Story"]:hover:before {
    content: attr(data-name-tooltip);
    position: absolute;
    bottom: calc(100% + 9px);
    left: 0;
    right: 0;
    margin: auto;
    background: #AD80FF;
    width: 100%;
    white-space: pre-wrap;
    color: #fff;
    text-align: center;
    font-size: 12px;
    border-radius: 13px;
    padding: 3px 0;
}
.connectPrising table td.soclDetail .d-flex.align-items-center {
    position: relative;
}
span.sertp[data-name-tooltip="Share Content from Customer - Post/Story"]:hover:after {content: "";position: absolute;border-style: solid;border-width: 7px 7px 0 7px;left: 0;right: 0;margin: auto;width: 0;bottom: calc(100% - -2px);border-color: #AD80FF transparent transparent;}
.connectPrising .velidation-additional-tasks .form-check label.form-check-label, .connectPrising .velidation-required-tasks .form-check label.form-check-label {
    /* padding-left: 44px; */
    /* white-space: wrap; */
}
.welcome-text {
    padding: 35px 0 5px;
    justify-content: center;
    color: #FEFEFC;
    text-align: center;
    font-family: Mulish;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    letter-spacing: 3px;
    border-radius: 20px;
    background: linear-gradient(180deg, #AD80FF 12.82%, #F86988 52.19%, #AD80FF 107.83%);
}
.welcome-text p {
    padding: 19px 0 30px;
    margin: 0;
}
.welcome-text img {
    width: 75px;
}
#thankyoupopup .application-text {
    max-width: 565px;
    margin: 0 auto;
    padding: 37px 0 19px;
}
#thankyoupopup input.application-send.ds {
    margin: 28px 0 10px;
}
.language-section {
    position: absolute;
    left: 20px;
    top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.language-section img {
    width: 25px;
    height: 25px;
}
.language-section ul {
    margin: 0;
    padding: 0;
    padding-left: 4px;
    list-style: none;
    display: flex;
}
.language-section ul li a, .language-section ul {
    color: #212427;
    font-family: Mulish;
    font-size: 20px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
}
.language-section ul li a.active {
    font-weight: 800;
}
.language-section ul li {
    padding: 0 6px;
}
.form-check.popup-check {
    margin-bottom: 12px;
}
span.select2-container.select2-container--default.select2-container--open {
    z-index: 1111111;
}
body .application-tab-section .select2-container--default .select2-selection {
    border: 1px solid #9A9A9A;
    overflow: auto;
    padding: 4px 16px;
    height: 38px;
}
body .application-tab-section .select2-container--default .select2-selection--single .select2-selection__rendered {
    padding: 0;
    height: auto;
    line-height: 28px;
}
body .application-tab-section .select2-container--default span.select2-selection.select2-selection--multiple {
    padding: 0px 8px;
    height: 38px;
}
body .application-tab-section .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 36px;
}
.application-tab-section ul.parsley-errors-list.filled {
    padding-top: 6px;
}
.parsley-errors-list.filled.socialmediaerror {
    min-width: 100%;
}
.soon-dropdown {
    position: absolute;
    right: 20px;
    top: 20px;
}
.soon-dropdown button.btn.btn-secondary.dropdown-toggle {
    border: 0;
    background: transparent;
    padding: 0;
    margin-right: 10px;
}
.soon-dropdown button.btn.btn-secondary.dropdown-toggle:after {
    display: none;
}
.form-check-label a {
    color: #000;
    font-weight: 700;
}
.soon-dropdown .dropdown-menu.show a {
    white-space: nowrap;
    display: inline-block;
    width: 100%;
    font-size: 18px;
    padding: 7px 20px;
    color: #000;
}
.soon-dropdown .dropdown-menu.show a:hover {
    background: linear-gradient(94.49deg, rgba(253, 155, 141, 0.95) 18.23%, rgba(248, 105, 136, 0.95) 51.77%, rgba(173, 128, 255, 0.95) 93.98%);
    color: #fff !important;
}
body .application-tab-section .country .select2-container--default span.select2-selection.select2-selection--multiple {
    overflow: hidden;
    background-image: url(../images/icons/icon-dropdown.svg) !important;
    background-size: 19px;
    background-repeat: no-repeat;
    background-position: right 4px center;
}
.content-area {
    min-height: calc(100vh - 284px);
    overflow-x: hidden;
}
p.caution-message {
    margin: 0 0 -9px 26px;
    flex: 0 0 100%;
}
#latest-influencer-lists .influencer-list {
    justify-content: start;
}
#backtop {
    color: #AD80FF;
    font-size: 50px;
    width: 50px;
    height: 50px;
    line-height: 50px;
    margin-left: 27px;
    margin-right: auto;
    display: none;
    background: #fff;
}
div#backtop.visible {
    display: block;
}
#new_steps3 .step-nevigationbutton.fixed {
    position: fixed;
    bottom: 57px;
    width: calc(100% - 406px);
    left: 326px;
}
.nhu div.nav-left {
    border-radius: 50px;
    background: #AD80FF;
    display: flex;
    width: 50px;
    height: 50px;
    padding: 7px 11px;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}
.nhu div.nav-left:hover, button.button-ccg.new-style:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}
.am-selected-media ul.media-box li .active img {
    filter: none;
}
#collection4 .publish-info .campaign-type-content {
    justify-content: center;
    align-items: center;
    flex-direction: column !important;
}
#collection4 .publish-info .campaign-type-content label {
    flex-direction: column;
}
#collection4 .publish-info .campaign-type-content label .campaign-type-text {
    padding: 0;
}
#collection4 .publish-info .campaign-type-content label img {
    width: 45px;
    margin-bottom: 12px;
}
#collection4 .publish-info .influencer-detail {
    width: calc(50% - 7px);
    margin-right: 0;
}
.accept-buttons.accept img {
    padding: 2px;
}
.step-nevigationbutton>div.backtop>img {
    background: transparent;
    padding: 0;
}

.dashboard-right main.all-content.mobile {
    padding: 0;
}
.dashboard-right .pagesections {
    border: 1px solid #CACACA;
    border-radius: 8px;
    margin: 23px 0;
    padding: 40px 17px;
}
.home_login-layout .page_tab {
    padding: 30px 30px;
}
.home_login-layout .page_tab .pagesections:first-child {
    margin-top: 0;
}
.home_login-layout .page_tab .pagesections:last-child {
    border: 0;
    margin: 0;
    padding-top: 20px;
    padding-bottom: 0;
}
.cna {
    color: #000;
    text-align: center;
    font-family: Mulish;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    margin-left: auto;
    margin-right: 18%;
}
.blur .socialBtnConnect, .blur .conttr {
    filter: blur(1.3px);
    pointer-events: none;
}
.disconnectedToSocial.blur .socialBtnConnect {
    filter: none;
    pointer-events: all;
}
.campaign-type-content.blur {
    /* background: #b4b4b4; */
    filter: blur(4px);
    pointer-events: none;
}
#new_steps0 .social_media_radio.manageActive {
    pointer-events: all;
    filter: none;
}
.titleone {
    color: #212427;
    text-align: center;
    font-family: Mulish;
    font-size: 20px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    margin-top: 49px;
}
button.apply-btn.withoutbg:before {
    content: "";
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    position: absolute;
    background: #fff;
    left: 2px;
    top: 2px;
    border-radius: 44px;
    z-index: 0;
}
.apply-btn.withoutbg:before {
    content: "";
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    position: absolute;
    background: #fff;
    left: 2px;
    top: 2px;
    border-radius: 44px;
    z-index: 0;
}
.apply-btn.withoutbg {
    width: 266px;
    height: 44px;
    color: #fff;
    font-family: Mulish;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    margin: 113px auto 0;
    border-radius: 50px;
    border: 0;
    background: linear-gradient(94.49deg, rgba(253, 155, 141, 0.95) 18.23%, rgba(248, 105, 136, 0.95) 51.77%, rgba(173, 128, 255, 0.95) 93.98%);
    text-align: center;
    transition: all 0.3s;
    position: relative;
    color: #212427;
    margin-top: 13px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
}
.loginpage header.site-header,
.loginpage footer.site-footer {
    display: none;
}
section.loginpage {
    background-color: rgba(173, 128, 255, 0.20);
    min-height: 100vh;
    min-width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 33px 0;
}
.loginpage h1.pageHeading {
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 25px;
    position: relative;
    margin-top: 4vh;
    margin-bottom: 0;
    color: #212427;
    font-family: Outfit;
    font-size: 26px;
    font-style: normal;
    font-weight: 700;
    line-height: 100%;
}
.loginpage h1.pageHeading span {
    position: relative;
    z-index: 1;
}
.loginpage
 .floating-label i {
    display: none;
}
.loginpage
 label.floatLabel {
    color: #718096;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: Outfit;
    font-size: 15px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px; /* 125% */
    letter-spacing: -0.154px;
    margin-bottom: 8px;
}
button.start-camp-btn.smallBtn {
    box-shadow: none;
    border: 0;
    width: 100%;
    height: 44px;
    border-radius: 12px;
    background: #AD80FF;
    color: #F7FAFC;
    font-family: Outfit;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 28px; /* 140% */
    border: solid 1px #AD80FF;
}
.loginpage .form-control {
    width: 100%;
    height: 44px;
    padding: 15px !important;
    border-radius: 12px;
    border: 1px solid #CBD5E0;
    background: #F7FAFC;
    box-shadow: 0px 2px 0px 0px rgba(231, 235, 238, 0.20) inset;
    color: #4A5568;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: Inter;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 125% */
    letter-spacing: -0.154px;
}
.logo {
    text-align: center;
}
.lgregistration-page {
    position: relative;
    margin-top: 4vh;
    flex-wrap: wrap;
}
.lgregistration-page .col-12.text-center button.apply-btn {
    margin-top: 24px;
    margin-bottom: 19px;
    width: 276px;
    height: 44px;
    color: #FFF;
    font-family: Outfit;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    border: solid 1px #AD80FF;
    margin: 16px 12px 0;
}
.lgregistration-page .col-12.text-center {
    display: flex;
    justify-content: center;
}
.loginpage .logo img {
    width: 304px;
}
.app-text {
    text-align: center;
    color: #718096;
    font-family: Outfit;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%; /* 27px */
}
.loginRight form {
    margin: 0 auto;
    width: 420px;
}
button.start-camp-btn.smallBtn:hover {
    color: #fff;
    background: transparent;
    color: #AD80FF;
}
.loginRight .form-group {
    margin: 0;
}
.blur .socialBtnConnect a.soclBtn.connect.table-btn.green-btn {
    background: #959595 !important;
    border-color: #959595 !important;
}
.bannersection {
    background: rgba(173, 128, 255, 0.20);
    height: auto;
    padding-top: 139px;
}
.bannerImage {
    flex: 1;
    margin-top: auto;
}
.bannerImage img {
    width: 106%;
}
.bannersection .container {
    height: 100%;
}
.bannersection>.container>.d-flex {
    height: 100%;
}
.bannerContentSubtitle {
    color: #AD80FF;
    font-family: Outfit;
    font-size: 20px;
    font-weight: 500;
    letter-spacing: 1.6px;
    line-height: normal;
    padding-top: 42px;
    text-transform: uppercase;
}
.bannerContentTitle {
    font-family: Outfit;
    font-size: 40px;
    font-weight: 700;
    letter-spacing: 16px;
    line-height: normal;
    padding-bottom: 13px;
}
.bannerContentText {
    color: #000;
    font-family: Outfit;
    font-size: 14px;
    font-weight: 400;
    padding-bottom: 24px;
    line-height: normal;
    padding-right: 67px;
}
.bannerContentBtn .btn {
    border-radius: 8px;
    padding: 9px 17px;
    font-family: Outfit;
    font-size: 14px;
    line-height: normal;
    font-weight: 500;
    min-width: 172px;
}
.bannerContentBtn {
    gap: 9px;
}
.bannerContentBtn .btn.btn-primary {
    background: #AD80FF;
    border-color: #AD80FF;
}
.bannerContentBtn .btn.btn-secondary {
    border-color: #AD80FF;
    background: transparent;
    color: #AD80FF;
}
.bannerContentBtn .btn.btn-secondary:hover {
    background: #AD80FF;
    color: #fff;
}
.bannerContentBtn .btn.btn-primary:hover {
    background: transparent;
    color: #AD80FF;
}
.bannerContent {
    flex: 0 0;
}
.mainHeading {
    color: #212427;
    text-align: center;
    font-family: Outfit;
    font-size: 40px;
    font-weight: 600;
    line-height: normal;
}
.subHeading {
    color: #212427;
    font-family: Outfit;
    font-size: 20px;
    text-align: center;
    line-height: normal;
    font-weight: 600;
    margin-top: -6px;
}
section.pageSectionOuter {
    padding-top: 70px;
}
.pageSectioncontent {
    flex: 0 0 calc(100% - 322px);
    max-width: 694px;
    padding-left: 42px;
    /* padding-right: 42px; */
    margin-left: auto;
}
.pageSectionImage img {
    /* width: 401px; */
    position: relative;
    width: 100%;
}
.pageSectionTitle {
    font-weight: 600;
    color: #212427;
    font-family: Outfit;
    font-size: 32px;
    padding-bottom: 16px;
}
.pageSectionText {
    color: #212427;
    font-family: Outfit;
    font-size: 22px;
    font-weight: 400;
}
.pageSectionBtn .btn {
    border-radius: 12px;
    color: #FFF;
    font-family: Outfit;
    font-size: 21.921px;
    font-weight: 500;
    padding: 17px 26px;
    min-width: 267.982px;
}
.pageSectionBtn {
    gap: 13px;
    padding-top: 100px;
}
.pageSectionBtn .btn.btn-primary {
    background: #AD80FF;
    border-color: #AD80FF;
}
.pageSectionBtn .btn.btn-secondary {
    color: #AD80FF;
    background: transparent;
    border-color: #AD80FF;
}
.pageSectionBtn .btn.btn-secondary:hover {
    background: #AD80FF;
    color: #fff;
}
.pageSectionBtn .btn.btn-primary:hover {
    color: #AD80FF;
    background: transparent;
}
body.-layout.not-logedin .content-area {
    background: #fff;
    overflow-x: hidden;
}
.pageSection.flex-row-reverse .pageSectionImage {
    /* margin: -98px -73px -210px -48px; */
    margin-left: auto;
    position: relative;
}
.pageSection {
    padding: 50px 0;
    position: relative;
}
.linkingSection img.way-image {
    max-width: 100%;
    max-width: 710px;
    margin: 0 auto;
    width: calc(100% - 110px);
    position: relative;
    z-index: 0;
}
.linkOption {
    position: absolute;
    border-radius: 12px;
    width: 161px;
    height: 145px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    z-index: 9;
    gap: 10px;
}
.linkingSection {
    margin: 52px 0;
    position: relative;
    padding: 53px 0;
    width: auto;
    z-index: 0;
}
.linkOptiontext {
    text-align: center;
    color: var(--Neutral, #FFF);
    font-family: Outfit;
    font-size: 16px;
    font-weight: 500;
    height: 41.207px;
    padding: 0 9px;
    line-height: normal;
    display: flex;
    align-items: center;
}
.linkOption1 {
    background: #D5BFFF;
    left: 0;
    top: 45%;
}
.linkOption2 {
    background: #AD80FF;
    left: calc(29% - 98px);
    top: 0;
}
.linkOption3 {
    background: #AD80FF;
    top: 0;
    left: 0;
    bottom: 0;
    margin: auto;
    right: 0;
}
.linkOption4 {
    background: #F86988;
    bottom: 0;
    right: calc(29% - 94px);
}
.linkOption5 {
    background: #FBB3C3;
    right: 0;
    bottom: 45%;
}
.pageFaqSection .nav {
    width: 310.069px;
    border-radius: 12px;
    border: 1px solid #E9F3FE;
    background: #E2E2E2;
    padding: 24px;
    gap: 30px;
    justify-content: start;
}
.oskii {
    color: #212427;
    font-family: Outfit;
    font-size: 24px;
    font-weight: 700;
    line-height: normal;
}
.pageFaqSection .nav .nav-link {
    display: inline-block;
    width: auto;
    height: auto;
    padding: 0;
    text-align: left;
    color: #877F7F;
    font-family: Outfit;
    font-size: 16px;
    font-weight: 500;
}
.pageFaqSection .nav .nav-link.active, .pageFaqSection .nav .nav-link:hover {
    background: transparent;
    color: #AD80FF;
}
.pageFaqSection .accordion-item {
    border-radius: 12px !important;
    border: 1px solid #DEDEDE;
    margin: 24px 0 !important;
    width: 100%;
}
.pageFaqSection .accordion-item .accordion-header {
    background: transparent !important;
}
.pageFaqSection .accordion-item h2 button.accordion-button {
    background: transparent;
    padding: 24px;
    color: #212427;
    font-family: Outfit;
    font-size: 16px;
    font-weight: 500;
    box-shadow: none;
}
.pageFaqSection .accordion {
    gap: 28px;
}
.pageFaqSection .accordion-item h2 button.accordion-button[aria-expanded="true"] {font-weight: 600;box-shadow: none;}
.pageFaqSection .accordion-item div.accordion-collapse .accordion-body {
    padding-top: 0;
    border: 0 !important;
    color: #888580;
    font-family: Outfit;
    font-size: 16px;
    padding: 24px;
    padding-top: 0 !important;
}
.pageFaqSection .accordion-item div.accordion-collapse {
    border: 0 !important;
}
.pageFaqSection .accordion-item.active {
    border-radius: 12px;
    border: 1px solid #F0F8F8;
    background: #FFF;
    box-shadow: 0px 10px 40px 0px rgba(0, 0, 0, 0.05);
}
div#tocTabContent {
    max-width: 816px;
    flex: 1;
}
.pageFaqSection .accordion-item:first-child {
    margin-top: 0 !important;
}
.pageFaqSection .accordion-item:last-child {
    margin-bottom: 0 !important;
}
.pageFaqSection {
    padding-top: 60px;
}
footer {
    background: #efe7ff;
    /* border-bottom: solid 66px rgba(0, 0, 0, 0.2); */
}
.footerSection {
    max-width: 1254px;
    margin: 0 auto;
    display: flex;
    align-items: center;
}
.footerHeading {
    color: #AD80FF;
    font-family: Outfit;
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 1px;
    line-height: normal;
    margin-bottom: 8px;
}
ul.footerlink li a {
    letter-spacing: 1px;
    color: #000000;
    font-family: Outfit;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 12px;
    margin: 0 0 11px 0;
}
ul.footerlink {list-style: none;margin: 0;padding: 0;}
.footerLogo {
    width: 195px;
    padding-bottom: 21px;
}
.footerLogo img {
    width: 100%;
}
.footerLogoText {
    color: #000000;
    font-family: Outfit;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px; /* 142.857% */
    letter-spacing: 0.7px;
}
.footerLogoSection {
    width: 230px;
    flex: 0 0 230px;
}
.footerlink {flex: 0 0 291px;margin-left: auto;}
.footerContainer .d-flex .footerSection {
    align-items: flex-start;
    padding: 114px 15px;
}
.footerLogoText.borderBottom {border-bottom: solid 1px #AD80FF;padding-bottom: 12px;margin-bottom: 16px;}
.linkOy, .linkOy a {
    color: #000000;
    font-family: Outfit;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 1px;
}
.linkOy a {
    margin-bottom: 16px;
    display: inline-block;
}
.footerlink:nth-child(2) {
    flex: 0 0 130px;
}
.footerlink:nth-child(3) {
    flex: 0 0 203px;
}
#hsj-menu li {
    margin-left: 0px;
}
#hsj-menu li a {
    padding: 0 4px;
    color: #1E1854;
    font-family: Outfit;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    text-decoration-line: underline;
}
section#FAQ-content {
    padding-bottom: 70px;
    padding-top: 79px;
    display: inline-block;
    width: 100%;
}
.login-layout .floating-label {
    margin-bottom: 20px;
}
.lgregistration-page .col-12.text-center button.apply-btn:hover {
    background: transparent;
    color: #AD80FF;
}
.form-group.form-check.custom-check input:checked ~ label.form-check-label {
    border-color: #AD80FF;
}
.form-group.form-check.custom-check input ~ label:before {
    width: 16px;
    height: 16px;
    border: solid 2px #000;
    content:"";
    position: absolute;
    border-radius: 3px;
    left: 1px;
    top: 4px;
}
.dashboard-right ul#scrolling-menu {
    display: none;
}
.site-header.rink-header.fixed-header .header-logo {
    height: 30px;
}
.site-header.rink-header.fixed-header {
    background: #fff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 9px 0;
    z-index: 2;
}
.without-login-deader header.site-header {
    background: transparent;
    position: fixed;
    top: 0;
    left: 0;
}
li.ntfMenu.display-none {
    display: none;
}
.without-login-deader span.showUser {
    display: none;
}
span.count {
    min-width: 12px;
    height: 12px;
    text-decoration: none;
    min-width: 14px;
    height: 14px;
    display: inline-block;
    background: rgba(255, 0, 0, 1);
    color: #FFF;
    text-align: center;
    font-size: 10px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    border-radius: 11px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: -4px;
    right: -4px;
    left: auto;
}
#hsj-menu li a.icon {
    text-decoration: none;
    position: relative;
    padding: 9px;
    border-radius: 5px;
    transition: all 0.3s;
}
ul.h-menu.logn-menu {
    align-items: center;
}
.task-div-outer .form-group {
    width: 470px;
    flex: 0 0 470px;
}
.task-div-outer {
    gap: 88px;
    width: 100%;
}
.task-title {
    height: 60px;
    background: #AD80FF;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFF;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    gap: 18px;
    margin-top: 50px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
}
.checked-list .form-check:before, .custom-task-list .form-check:before {
    content: "";
    width: calc(100% - 2px);
    height: calc(100% - 2px);
    background: #fff;
    position: absolute;
    z-index: 0;
    border-radius: 14px;
}
.checked-list .form-check input {
    z-index: 1;
}
.checked-list .form-check {
    pointer-events: none;
}
td.remove-list {
    width: 22px;
    text-align: right;
}
td.pricing {
    width: 158px;
}
.inside-table.request-content {
    display: flex;
    flex-wrap: wrap;
    gap: 44px 55px;
    padding: 50px 110px 78px;
}
.request-content .inside-table-row {
    flex: 0 0 153px;
    width: 153px;
    position: relative;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 20px;
    border: 1px solid var(--stepper-active, #AD80FF);
    gap: 10px;
    padding: 14px 0 19px;
    margin-top: 20px;
    min-height: 120px;
}
.request-popup .modal-dialog.default-width {
    max-width: 1000px;
}
span.type-label {
    position: absolute;
    bottom: 102%;
    left: 0;
    right: 0;
    color: #000;
    text-align: center;
    font-family: 'Mulish';
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 1px;
}
span.type-image {
    width: 35px;
    height: 35px;
}
span.type-content {
    color: #000;
    text-align: center;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 1px;
}
.request-content-data.icon-before {
    display: flex;
    flex-wrap: wrap;
    gap: 34px 65px;
    padding: 29px 67px 46px;
}
.request-content-data .inside-table-row {
    display: block;
    border-radius: 20px;
    border: 1px solid var(--stepper-active, #AD80FF);
    width: 399px;
    padding: 12px;
}
.request-content-data.icon-before .order-titles:before {
    min-width: 40px;
    height: 40px;
    background-size: contain;
    background-repeat: no-repeat;
    margin: 0;
}
.request-content-data .order-content:before {content: "";background: url(../images/icons/icon-arrow-left-black.svg);width: 25px;height: 25px;flex: 0 0 25px;background-size: contain;}
.request-content-data .order-content {
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 1px;
    color: #000000;
    padding: 0;
    align-items: center;
    text-overflow: ellipsis;
    overflow: hidden;
    display: flex;
    flex: 0 0 100%;
    width: 100%;
    gap: 9px;
    margin-top: 25px;
    margin-bottom: 14px;
}
.request-content-data.icon-before .order-titles {
    margin: 0;
    text-align: left;
}
.dashboard_outer .site-header.rink-header .container {
    max-width: unset;
}
.accordion-collapse .accordion-body {
    position: relative;
}
.ontotalcount .influencer-price {
    border-top: solid 2px #878787;
    padding-top: 12px;
}
.ontro .ontotalcount .dr-row {
    padding-top: 0;
    color: #353B5F;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 800;
    line-height: normal;
    padding-bottom: 0;
}
.payment-total {
    padding: 0 110px;
}
.payment-title {
    color: #000;
    font-family: Mulish;
    font-size: 20px;
    font-style: normal;
    font-weight: 800;
    line-height: normal;
    margin-bottom: 15px;
    margin-top: 35px;
}
.payment .d-flex.bold {
    border-top: solid 1px #000;
    margin-top: 8px;
    padding-top: 17px;
}
.payment .d-flex.bold span {
    margin: 0;
}
.cardsimages.d-flex {
    gap: 20px;
    margin-bottom: 39px;
    margin-top: 39px;
}
.cardsimages.d-flex img {
    width: 36px;
}
#stripe_card label {
    margin: 0 0 16px !important;
}
#stripe_card .form-control {
    border-radius: 10px;
    border: 1px solid #9A9A9A;
    background: transparent;
}
#stripe_card .form-group {
    margin-bottom: 26px;
}
.card-design {
    display: inline-flex;
    border-radius: 16px;
    background: var(--Primary-Base, #1DAB87);
    flex-direction: column;
    width: 223.737px;
    /* height: 130px; */
    overflow: hidden;
}
.card-design-bottom.d-flex {
    border-radius: 0px 0px 16px 16px;
    background: var(--btn-primary, #AD80FF);
    padding: 3px 13px 3px;
    align-items: center;
}
.card-design-top {
    /* border-radius: 16px; */
    background: var(--stepper-active, #AD80FF);
    padding: 16px 0 13px 13px;
    background-image: url(../images/icons/icon-card-structure.png);
}
.card-nm {
    color: var(--Others-White, #FFF);
    font-family: Roboto;
    font-size: 13px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%; /* 19.5px */
    letter-spacing: 0.3px;
    margin-top: 24px;
    display: flex;
    gap: 9px;
}
.crimll {
    color: var(--Others-White, #FFF);
    font-family: Roboto;
    font-size: 13px;
    font-style: normal;
    font-weight: 600;
    line-height: 150%; /* 19.5px */
    letter-spacing: 0.3px;
}
.crimdd {
    color: var(--Others-White, #FFF);
/* body/xsmall/regular */
    font-family: Roboto;
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%; /* 15px */
    letter-spacing: 0.3px;
    opacity: 0.6;
    margin-top: 2px;
}
.crim-img {
    margin-left: auto;
}
.card-radio {
    width: 15px;
    height: 15px;
    flex-shrink: 0;
    border: solid 1px #000;
    border-radius: 50%;
    position: relative;
    margin-right: 34px;
}
.card-radio:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    background: #000;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    margin:  auto;
}
.card-remove-btn {
    border-radius: 15px;
    background: #EB0000;
    color: #FFF;
    text-align: center;
    font-family: Roboto;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: 150%; /* 18px */
    letter-spacing: 0.3px;
    width: 111px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: solid 1px #EB0000;
    transition: all 0.3s;
    cursor: pointer;
    margin-left: 34px;
}
.card-remove-btn:hover {background: transparent;color: #EB0000;}
.card-design-outer {
    margin-top: 50px;
}
.dropdown-item.active, .dropdown-item:active{
    background-color: transparent;
}
.camping-box {
    border-radius: 50px;
    border: 1px solid #b7b7b7;
    background: #f4f4f4;
    flex: 0 0 262px;
    padding: 21px 21px 28px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;
    width: 262px;
}
.new-camping {
    gap: 57px;
}
.cb-image {
    filter: invert(83%) sepia(0%) saturate(0%) hue-rotate(344deg) brightness(88%) contrast(95%);
    text-align: center;
    width: 40px;
    height: 40px;
    margin: 0 auto;
}
.cb-title {
    color: #b7b7b7;
    font-family: Mulish;
    font-size: 20px;
    font-style: normal;
    font-weight: 800;
    line-height: 125%; /* 25px */
    text-align: center;
    padding-bottom: 11px;
}
.cb-sublabel {
    color: #000;
    font-family: Mulish;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 125%; /* 15px */
    position: relative;
    padding-left: 30px;
    margin-bottom: 16px;
    height: 30px;
}
.cb-sublabel:before {
    content: "";
    background: url(../images/icons/icon-checkicon-gray.svg);
    width: 17px;
    height: 17px;
    position: absolute;
    left: 0;
    top: 0;
    filter: invert(73%) sepia(0%) saturate(21%) hue-rotate(224deg) brightness(107%) contrast(77%);
}
.cb-socialmedia {
    display: flex;
    gap: 21px;
    justify-content: space-between;
    margin-bottom: 16px;
}
.cb-socialmedia img.active {
    filter: invert(79%) sepia(85%) saturate(1066%) hue-rotate(297deg) brightness(94%) contrast(113%);
}
.cb-socialmedia img {
    filter: invert(85%) sepia(31%) saturate(16%) hue-rotate(17deg) brightness(87%) contrast(95%);
    border-bottom: solid 1px transparent;
}
.cb-smlabl {
    color: #000;
    font-family: Mulish;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 125%; /* 15px */
    padding-bottom: 11px;
}
.cb-smbl-img {
    border: solid 1px #b7b7b7;
    border-radius: 50%;
    min-width: 30px;
    height: 30px;
    text-align: center;
    flex: 0 0 30px;
}
.cb-smbl-img img {
    width: 20px;
    height: 20px;
    object-fit: contain;
    filter: invert(73%) sepia(0%) saturate(21%) hue-rotate(224deg) brightness(107%) contrast(77%);
}
.cb-smbl {
    display: flex;
    gap: 18px;
    align-items: center;
}
.cb-smbl-prc, .cb-smbl-prc input {
    color: #b7b7b7;
    font-family: Mulish;
    font-size: 15px;
    font-style: normal;
    font-weight: 800;
    line-height: 125%; /* 18.75px */
}
.cb-prcing {
    margin-top: 16px;
}
.prcnt img {
    filter: invert(66%) sepia(15%) saturate(2211%) hue-rotate(71deg) brightness(93%) contrast(84%);
    width: 20px;
    height: 20px;
}
.prcnt {
    /* color: #3CC23C; */
    font-family: Mulish;
    font-size: 15px;
    font-style: normal;
    font-weight: 800;
    line-height: 125%; /* 18.75px */
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 24px;
}
.cbInput {
    z-index: 11;
    cursor: pointer;
    width: 44px;
    height: 24px;
    position: absolute;
    right: 25px;
    top: 25px;
    border: solid 1px #b7b7b7;
    background: #fff;
    border-radius: 21px;
}
label.cbInputLabel {
    /* width: 44px; */
    /* height: 24px; */
    /* position: absolute; */
    /* right: 25px; */
    /* top: 25px; */
    /* border: solid 1px #AD80FF; */
    /* background: #fff; */
    /* border-radius: 21px; */
}
label.cbInputLabel:before {
    content: "";
    width: 18px;
    height: 18px;
    position: absolute;
    left: 2px;
    top: 0;
    background: #b7b7b7;
    border-radius: 50%;
    bottom: 0;
    margin: auto;
    transition: all 0.3s;
}
input:checked ~ label.cbInputLabel:before {
    left: 22px;
    background-image: url(../images/icons/icon-check-white.svg);
    background-position: center;
    background-repeat: no-repeat;
    transition: all 0.3s;
}
.cbInput input.cbInput-radio {
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}
img.dash-image {
    /* display: none; */
    position: absolute;
    left: -185px;
    height: 131%;
}
.pageSectionImage img.shadow-box {
    position: absolute;
    width: 194.1%;
    top: -18.24%;
    left: -67%;
    z-index: 1;
    height: auto;
}
.informationDiv table input[type="text"] {
    width: 142px;
}
label.checkboxAllCheck {
    position: relative;
    margin-left: 0;
    margin-bottom: 7px;
}
.pageSectionImage {
    flex: 1;
    display: inline-block;
    position: relative;
    max-width: 239px;
    flex: 0 0 239px;
}
.flex-row-reverse img.dash-image {
    left: auto;
    right: -211px;
    transform: rotate(180deg);
}
.dashboard-right li.ntfMenu.display-none {
    display: block !important;
}
.dashboard-right #hsj-menu li.active a.icon, .dashboard-right #hsj-menu li a.icon:hover {background: #AD80FF;transition: all 0.3s;}
.dashboard-right #hsj-menu li.active a.icon img, .dashboard-right #hsj-menu li a.icon:hover img {
    filter: brightness(0) invert(1);
    transition: all 0.3s;
}
.dashboard-right li.ntfMenu {
    padding: 0 12px 0 0;
}
.profile-page form {
    max-width: 930px;
    margin: 0 auto;
    width: 100%;
}
.profile-page form .form-control {
    border-radius: 20px;
    border: 1px solid #D1D5DB;
    background: #D9D9D9;
    height: 30px;
}
body .profile-page .select2-container--default .select2-selection, body .checkinh .select2-container--default .select2-selection {
    border-radius: 20px;
    border: 1px solid #D1D5DB;
    background: #D9D9D9;
    height: 30px;
}
body .profile-page .select2-container--default .select2-selection--single .select2-selection__rendered, body .checkinh .select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 24px;
}
body .profile-page .select2-container--default .select2-selection--single .select2-selection__arrow, body .checkinh .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 30px;
}
.profile-connect-btn {
    border-radius: 50px;
    background: rgba(99, 91, 255, 0.95);
    color: #FFF;
    text-align: center;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px; /* 150% */
    display: inline-flex;
    width: 265px;
    height: 50px;
    flex-direction: column;
    justify-content: center;
    margin-bottom: 0;
}
span.accnumberoo {
    color: #AD80FF;
    display: inline-block;
    margin: 9px 0 0;
    font-weight: 700;
}
.middle-box table td.total-follower {
    width: 27%;
}
.middle-box table td.influencer-name {
    width: 33%;
}
.cbInput input {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
    opacity: 0;
    cursor: pointer;
}
.cb-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}
img.mediaCheck {
    position: inherit;
    pointer-events: none;
}
img.active.mediaCheck {
    pointer-events: all;
    cursor: pointer;
}
img.active.mediaCheck:hover,img.active.checked.mediaCheck {
    border-color: #e56d5c8a;
}
.home-page {
    background: #fff !important;
}
.camping-box.disabled .cb-image,
.camping-box.disabled .cb-socialmedia img.active {
    filter: invert(99%) sepia(57%) saturate(0%) hue-rotate(337deg) brightness(109%) contrast(100%);
}
.camping-box.disabled .cb-sublabel:before,
.camping-box.disabled .prcnt img {
    filter: brightness(0) invert(1);
}
.camping-box.disabled .cb-smbl-img {
    background: #fff;
}
.camping-box.disabled {
    background: #b7b7b7 !important;
    border-color: #b7b7b7 !important;
    pointer-events: none;
    user-select: none;
    -moz-user-select: none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -o-user-select: none;
}
.camping-box.disabled .cb-smbl-prc,
.camping-box.disabled .cb-title,
.camping-box.disabled .cb-sublabel,
.camping-box.disabled .cb-smlabl,
.camping-box.disabled .cb-smbl-img,
.camping-box.disabled .prcnt {
    color: #fff;
}
.camping-box.disabled img.active.mediaCheck {
    filter: invert(100%) sepia(100%) saturate(0%) hue-rotate(288deg) brightness(102%) contrast(102%);
}
img.active.mediaCheck {
    pointer-events: all;
    cursor: pointer;
    filter: invert(73%) sepia(0%) saturate(21%) hue-rotate(224deg) brightness(107%) contrast(77%);
}
#processTabs .nav-link {
    border-radius: 12px;
    color: #FFF;
    font-family: Outfit;
    font-size: 18px;
    font-weight: 500;
    padding: 10px 26px;
    min-width: 267.982px;
    border: 1px solid transparent;
    color: #AD80FF;
    background: transparent;
    border-color: #AD80FF;
    line-height: normal;
    text-align: center;
    justify-content: center !important;
    flex-direction: row;
}

ul#processTabs {
    gap: 13px;
    padding-top: 100px;
    justify-content: center;
}


#processTabs .nav-link:hover, #processTabs .nav-link.active {
    background: #AD80FF;
    border-color: #AD80FF;
    color: #fff;
}
#hsj-menu li.leng a {
    padding: 0;
    text-decoration: none;
    color: #877F7F;
    padding: 0 2px;
}

#hsj-menu li.leng {
    padding-right: 6px;
}
#hsj-menu li.leng a.active {
    color: #1E1854;
}
.freeprivacypolicy-com---palette-light .cc-nb-title {
    font-family: Outfit;
}

.freeprivacypolicy-com---palette-light .cc-nb-text {
    font-family: Outfit;
}

.freeprivacypolicy-com---nb .cc-nb-okagree, .freeprivacypolicy-com---nb .cc-nb-reject, .freeprivacypolicy-com---nb .cc-nb-changep {
    font-family: Outfit;
}

.freeprivacypolicy-com---nb .cc-nb-okagree, .freeprivacypolicy-com---nb .cc-nb-reject, .freeprivacypolicy-com---nb .cc-nb-changep {
    border-radius: 8px;
    padding: 9px 17px;
    font-family: Outfit;
    font-size: 14px;
    line-height: normal;
    font-weight: 500;
    min-width: auto;
    border: solid 1px #AD80FF;
    transition: all 0.3s;
}

body .freeprivacypolicy-com---nb .cc-nb-okagree {
    background: #AD80FF;
    border-color: #AD80FF;
}

body .freeprivacypolicy-com---nb .cc-nb-reject {
    border-color: #AD80FF;
    background: transparent;
    color: #AD80FF;
}

body .freeprivacypolicy-com---nb .cc-nb-okagree:hover {
    background: transparent;
    color: #AD80FF;
}

body .freeprivacypolicy-com---nb .cc-nb-reject:hover {
    background: #AD80FF;
    color: #fff;
}

section#FAQ {
    padding: 50px 0;
}
.open img.active.mediaCheck:hover, .open img.active.checked.mediaCheck {
    border-color: black;
}
span.orange-color {
    color: #AD80FF;
}
body .checkinh .smalSpace .form-control {
    border-radius: 20px;
    border: 1px solid #D1D5DB;
    background: #D9D9D9;
    height: 30px;
}

.checkinh {
    max-width: 930px;
    margin: 34px auto 0;
    width: 100%;
}
.camping-box.open {
    border: 1px solid var(--stepper-active, #AD80FF);
    background: #cab6ed47 !important;
}

.open .cb-image {
    filter: brightness(0) saturate(100%) invert(30%) sepia(100%) saturate(5000%) hue-rotate(260deg) brightness(120%) contrast(90%);
}

.open .cb-title {color: #AD80FF;}

.open label.cbInputLabel:before {
    background: #AD80FF;
}

.open .cbInput {
    border-color: #AD80FF;
}
.open .cb-sublabel:before {
    filter: brightness(0) saturate(100%) invert(30%) sepia(90%) saturate(5000%) hue-rotate(258deg) brightness(110%) contrast(105%);
}

.prcnt {
    filter: invert(73%) sepia(0%) saturate(21%) hue-rotate(224deg) brightness(107%) contrast(77%);
}

.open .cb-smbl-img img {
    filter: none;
}

.open .cb-smbl-img {
    border-color: #AD80FF;
    background: transparent !important;
}

.open .cb-smbl-prc {
    color: #AD80FF;
}

.open .prcnt {
    filter: none;
    color: #3CC23C;
}

.open img.active.mediaCheck {
    filter: brightness(0) saturate(100%) invert(30%) sepia(90%) saturate(5000%) hue-rotate(258deg) brightness(110%) contrast(105%);
}
.disabled .cbInput {
    border-color: #b7b7b7 !important;
}

.disabled label.cbInputLabel:before {
    background-color: #b7b7b7 !important;
}

.disabled .cb-smbl-img {
    background: #fff !important;
    border-color: #fff;
}

.disabled .cb-smbl-img img {
    filter: invert(73%) sepia(0%) saturate(21%) hue-rotate(224deg) brightness(107%) contrast(77%);
}
.profile-connect-btn img {
    width: 23px;
}

.profile-connect-btn {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;
    gap: 12px;
}
.tagify__input:empty::before{
    opacity: 0.5;
}
.connectPrising .cusomRadio .form-check input {
    left: -999px;
    position: absolute;
}
.select2-container .select2-search--inline .select2-search__field {
    margin: 0;
    display: inline-block;
    height: 16px;
    padding: 2px 3px;
    margin-bottom: -5px;
}
ul.footerlink li a img {
    filter: invert(64%) sepia(82%) saturate(4016%) hue-rotate(223deg) brightness(105%) contrast(104%);
}

.copyrightOuter {
    background: #bfb9cc;
    padding: 27px 0;
}
.bannerImageContent {
    position: absolute;
    bottom: 20%;
    left: 0;
    margin: auto;
    border-radius: 7.89px;
    background: #fff;
    max-width: 402px;
}

.bannerImage {
    position: relative;
    padding-left: 93px;
}

.bannerImageContent ul {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    line-height: normal;
    list-style: none;
    padding: 10px;
    margin: 0;
}

.bannerImageContent ul li {
    width: calc(50% - 5px);
    background: #efe6ff;
    font-size: 12px;
    display: flex;
    align-items: center;
    border-radius: 4px;
    padding: 7px;
    max-width: 402px;
    gap: 0 4px;
}

.bannerImageContent ul li:before {
    content: "";
    width: 20px;
    height: 20px;
    display: inline-block;
    background: url(../images/icons/check-purple.svg);
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    flex: 0 0 20px;
}
.mobile-lan {
    display: none;
}
.h-menu-outer {
    flex: 1;
    display: flex;
}
.dash-image-mobile{
    display: none;
}
.logedin .mobile-lan {
    display: none;
}
.image-desktop{
    display: block !important;
}
.image-mobile{
    display: none !important;
}
.form-check-input:checked[type=checkbox] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23AD80FF' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e") !important;
  }

  /*  new css for new structure  */
  body {
    background-color: #f5f5f5;
    font-family: 'Arial', sans-serif;
}
.campaign-card {
    background-color: white;
    /* padding: 20px; */
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.phase {
    flex: 1;
    padding: 10px;
    text-align: center;
    border-radius: 5px;
    margin-right: 10px;
    color: #495057;
    cursor: pointer;
}
.phase:last-child {
    margin-right: 0;
}
.phase.request { background-color: #00aaff; }
.phase.payment { background-color: #f1c40f; }
.phase.submit { background-color: #ff7675; }
.phase.review { background-color: #55efc4; }
.phase.active { background-color: #fd79a8; }
.campaign-header {
    justify-content: space-between;
    align-items: center;
    /* margin-bottom: 20px; */
    /* border: 1px solid #ddd; */
    /* margin-top: 5%; */
    padding: 10px;
    border-radius: 10px;
}
.campaign-header .campagin_info
{
    width: 54%;
}
.campaign-header .details {
    align-items: center;
    display: flex;
    /* flex-direction: column; */
    width: 40%;
}
.campaign-header .price_info {
    width: 50%;
}
.likes_statistics
{
    display: block;
    font-size: 15px;
    padding: 4% 11%;
}
.campaign_statuses_tab
{
    border: none !important;
    padding-left: 14%;
}
.all_invoices_btn
{
    padding-left: 7%;
}
.campaign-header .details > div {
    margin-right: 20px;
}
.campaign-header .details .btn {
    margin-left: 10px;
}

.campaign-header .badge {
    flex: 1;
    margin: 0 5px;
    text-align: center;
    font-size: 0.8rem;
    font-weight: 100;
    color: black !important;
}
.campaign-table {
    width: 100%;
    border-collapse: collapse;
    border: none;
    font-weight: 700;
    margin: 20px 0;
    table-layout: fixed;
}
.campaign-table th, .campaign-table td {
    border-bottom: 2px solid #ddd;
    padding: 10px;
    text-align: left;
    overflow: hidden;
}
.campaign-table td {
    width: 20%;
}
.campaign-table img {
    border-radius: 50%;
    width: 50px;
    height: 50px;
}
.statistics_images img{
    width: 20px !important;
}
.campaign-table .actions {
    white-space: nowrap;
    text-align: right;
}
.campaign-table .btn {
    margin-left: 10px;
}
.collapse-icon {
    cursor: pointer;
    font-size: 1.5em;
}
.tabs .nav-link {
    color: #6c5ce7;
    font-weight: bold;
}
.tabs .nav-link.active {
    background-color: #AD80FF;
    color: white !important;
    border: none;
}
.title {
    font-size: 2em;
    color: #6c5ce7;
    text-align: center;
    margin-bottom: 20px;
}
.top-right-button {
    top: 0;
    right: 0;
}
.header-section {
    display: flex;
    align-items: center;
    padding-top: 4px;
}
/* .nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active {
    border: none !important;
    border-color: none !important;
    background-color: #AD80FF !important;
    color: white !important;
} */
.btn-show-details {
    color: #AD80FF !important;
    border: solid 1px #AD80FF;
}
.btn-request-phase {
    background-color: #00ADEF !important;
    color: white !important;
}
.btn-payment-phase {
    background-color: #E8C11C !important;
    color: white !important;
}
.btn-submit-phase {
    background-color: #FD9B8D !important;
    color: white !important;
}
.btn-review-phase {
    background-color: #34CB79 !important;
    color: white !important;
}
.btn-finished-phase {
    background-color: #0F9D58 !important;
    color: white !important;
}
.btn-rejected-phase {
    background-color: #DB4437 !important;
    color: white !important;
}
.request-text-color{
    color: #00aaff;
}
.submit-text-color{
    color: #FD9B8D !important;
}
.review-text-color{
    color: #55efc4 !important;
}
.payment-text-color{
    color: #f1c40f !important;
}
.active-text-color{
    color: #fd79a8 !important;
}
.btn-review-phase {
    background-color: #55efc4 !important;
    color: white !important;
}
.btn-complete-phase {
    background-color: #FD9B8D !important;
    color: white !important;
}
.btn-review-phase {
    background-color: #34CB79 !important;
    color: white !important;
}
.btn-history {
    background-color: #AD80FF !important;
    color: white !important;
}
.btn-review {
    background-color: #AD80FF !important;
    color: white !important;
    width: 50%;
}
.btn-waiting {
    color: #AD80FF !important;
    border: solid 1px #AD80FF;
    width: 50%;
}
.btn-hold {
    color: #AD80FF !important;
    border: solid 1px #AD80FF;
    width: 40%;
}
.btn-show-result {
    background-color: #0F9D58;
    color: white !important;
    border: solid 1px #0F9D58;
    width: 50%;
}
.btn-finish-campaign {
    background-color: #3CC23C;
    color: white !important;
    border: solid 1px #0F9D58;
    width: 100%;
    margin-top: 7px;
}
.btn-finish-disabled {
   opacity: 0.65;
}
.btn-cancel-new {
    background-color: #EB0000;
    color: white !important;
    border: solid 1px #EB0000;
    width: 50%;
}
.btn-rejected{

    background-color: #DB4437 !important;
    color: white !important;
    border: solid 1px #EB0000;
    width: 50%;
}
.btn-campaign-status {
    color: #AD80FF !important;
    border: 1px solid #AD80FF !important;
    text-align: center;
    margin: 0px 35px 0px 45px;
    width: 90%;
}
.btn-all-invoices {
    color: #AD80FF !important;
    border: 1px solid #AD80FF !important;
    text-align: center;
    margin: 0px 35px 0px 45px;
    width: 90%;
}
.vertical-line {
    width: 1px;
    height: 150px;
    background-color: #B4ABAB;
    margin: 0px 15px 0px 15px;
}
.circle {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40px;
    width: 40px;
    border-radius: 50%;
    background-color: #AD80FF;
}
.circle.collapsed {
    transform: rotate(180deg);
}
.circle i {
    color: white;
}
#campaignDetails .tabs .nav-link.active {
    background-color: #AD80FF;
    color: white !important;
}
.campaign-card .nav-tabs .nav-link {
    display: block !important;
}
.star-rating {
    display: inline-block;
    font-size: 2rem; /* Adjust size as needed */
}

.star {
    color: gold; /* Default color for filled stars */
}

.star.unfilled {
    color: lightgray; /* Color for unfilled stars */
}
.text-disabled
{
    color: #B4ABAB;
}
.btn-completed
{
    background-color: #0F9D58;
    color: white;
}
.btn-show-result1
{
    background-color: #3CC23C;
    color: white;
}
/* MarketPlace css */
.campaign-list {
    margin-bottom: 20px;
    padding-left: 13%;
    padding-right: 20%;
}
.market_place_campaign-info {
    display: flex;
    align-items: center;
    padding: 0px 20px 0px 20px;
    width: 25%;
}
.campaign-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #fff;
    margin-bottom: 10px;
    border-radius: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    font-weight: 700;
}
.campaign-info {
    display: flex;
    align-items: center;
}
.influencer-pic {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-right: 10px;
}

.influencer-details {
    display: flex;
    flex-direction: column;
}
.campaign-pricing {
    display: flex;
}
.campaign_vat_price
{
    margin-left: 255px;
}
.remove-icon {
    cursor: pointer;
}

.order-summary {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-left: 13%;
    margin-right: 20%;
    display: flex;
    justify-content: space-between;
    padding: 20px;
    border: 1px solid #AD80FF;
}
.summary-column {
    display: flex;
    width: 40%;
}
.summary-right {
    display: flex;
    flex-direction: column;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}
.summary-item .label {
    font-weight: bold;
    color: #333;
}

.summary-item .value {
    color: #1E1854;
}

.total-container {
    background-color: #b388ff;
    padding: 20px;
    text-align: center;
    border-radius: 10px;
    margin-left: 13%;
    margin-right: 20%;
    margin-top: 10px;
}

.total-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.total-label {
    font-size: 24px;
    font-weight: bold;
    color: #fff;
}

.total-value {
    font-size: 24px;
    font-weight: bold;
    color: #fff;
    margin-left: 10px;
}
.info-text {
    margin-left: 13%;
    margin-right: 20%;
}
.info-text p {
    font-size: 14px;
    color: #555;
    margin: 0;
    padding: 10px;
    text-align: center;
}
/*  new css for small laptop  */
@media (max-width: 1550px) {
    .campaign-header .campagin_info
    {
        width: 47%;
    }
    .campaign-header .details {
        width: 50%;
    }
    .campaign-header .price_info {
        width: 38%;
    }
    .likes_statistics
    {
        display: block;
        font-size: 15px;
        padding: 4% 3%;
    }
    .campaign_statuses_tab
    {
     padding-left: 2%;
    }
    .all_invoices_btn
    {
    padding-left: 14%;
    }
    .container, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl {
        max-width: 1100px !important;
    }
}
@media (max-width: 768px) {
    body{
        font-size: 17px !important;
    }
    .campaign-header {
        margin-top: 0% !important;
        padding: 0px !important;
        margin-bottom: 0px !important;
        border: none !important;
        max-height: 200px !important;
    }
    .header-section{
        padding: 0px 4px;
    }
    .campaign-header .badge {
        font-size: 7px !important;
    }
    .phase {
        margin-right: 0;
        margin-bottom: 10px;
    }
    .campaign-header .details {
        flex-direction: column;
    }
    .campaign-header .details > div {
        margin-right: 0;
        margin-bottom: 10px;
    }
    .vertical-line {
        display: none;
    }

    .tabs .nav-link {
        display: block;
        width: 100%;
        text-align: center;
        margin-bottom: 10px;
    }

    .star-rating {
        font-size: 18px;
    }
    .circle {
        height: 30px;
        width: 30px;
        border-radius: 51%;
    }
    .collapse-icon {
        font-size: 16px;
    }
    .nav {
        flex-wrap: nowrap;
    }
    .nav-tabs .nav-item {
        margin-bottom: -56px;
    }
    .tabs .nav-link {
        /* width: 51%; */
    }
    .btn-campaign-status {
        margin: 0px;
    }
    .btn-all-invoices {
        margin: 0px;
    }
   .campaign-header .text-success{
    color: #0F9D58;
   }
   .campaign-header .text-gray{
    color: #877F7F;
   }
}
/* Mobile view css */
#mobile-view  .btn-history{
    width: 53%;
    margin-top: 8px;
    margin-bottom: 7px;
    float: right;
    margin-left: 163px;
}
#campaignForm
{
    margin-top: 65px;
}
.new-tabs-ul .nav-link.active
{
    background-color: #AD80FF;
    color: white !important;
}

.campaign-status-badge-container {
    margin-left: 0px;
    border-radius: 5px;
}

.campaign-status-badge {
    width: 200px;
    padding: 5px;
    text-align: center;
    border-radius: 5px;
    position: absolute;
}

.truncate-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 255px!important;
}

.mobile-pdf-icon {
    height: 28px !important;
}

.truncate-text-tooltip-container {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.truncate-text-tooltip {
    visibility: hidden;
    background-color: black;
    color: #fff;
    text-align: center;
    padding: 5px 10px;
    border-radius: 5px;
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.2s;
}

.truncate-text-tooltip-container.active .truncate-text-tooltip {
    visibility: visible;
    opacity: 1;
}

.campagin_info {
    margin-top: 30px;
}
