{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0", "doctrine/dbal": "^3.6", "guzzlehttp/guzzle": "^7.2", "lab404/laravel-impersonate": "^1.7", "laravel/cashier": "^14.12", "laravel/framework": "^9.0", "laravel/helpers": "^1.6", "laravel/sanctum": "^2.14", "laravel/socialite": "^5.6", "laravel/tinker": "^2.7", "laravel/ui": "^4.2", "opcodesio/log-viewer": "^3.17", "romanzipp/laravel-queue-monitor": "^5.4", "sentry/sentry-laravel": "^4.15", "socialiteproviders/instagram": "^5.0", "socialiteproviders/snapchat": "^4.1", "socialiteproviders/tiktok": "^4.1", "socialiteproviders/twitch": "^5.3", "socialiteproviders/youtube": "^4.1", "stripe/stripe-php": "^10.12"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}