<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        // Check if user is admin
        // Adjust this logic based on your admin detection method
        $user = auth()->user();
        
        // Option 1: Check for is_admin column
        if (isset($user->is_admin) && $user->is_admin) {
            return $next($request);
        }
        
        // Option 2: Check for admin role (if using roles)
        // if ($user->hasRole('admin')) {
        //     return $next($request);
        // }
        
        // Option 3: Check for specific permission
        // if ($user->can('access-admin-panel')) {
        //     return $next($request);
        // }
        
        // Option 4: Check by email domain or specific emails
        // $adminEmails = ['<EMAIL>', '<EMAIL>'];
        // if (in_array($user->email, $adminEmails)) {
        //     return $next($request);
        // }
        
        // Option 5: Check by user type or role column
        // if ($user->role === 'admin' || $user->user_type === 'admin') {
        //     return $next($request);
        // }

        // If none of the admin checks pass, deny access
        abort(403, 'Access denied. Admin privileges required.');
    }
}
