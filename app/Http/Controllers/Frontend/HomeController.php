<?php

namespace App\Http\Controllers\Frontend;
use App\Http\Controllers\Controller;

use Illuminate\Http\Request;
use App\Notifications\newContactEnquiry;
use App\Models\User;
use App\Models\Blog;
use App\Models\FaqTopic;
use App\Models\Faq;
use App\Models\CmsPage;
use App\Models\Attendee;
use App\Models\ShortCourse;
use App\Models\InfluencerRequestDetail;
use App\Models\Country;
use App\Models\Category;
use App\Models\CampaignRequestTime;


use DB;
use Auth;
use App;
use PDF;
use Session;
use Hash;  
use Stripe;

use <PERSON><PERSON>an\Location\Facades\Location;
use App\Notifications\registration;
use App\Notifications\adminUserRegistration;
use App\Notifications\RegistrationUser;

use App\Jobs\NewUserWelcomeMail;
use App\Jobs\NewsocialAlreadyUsed;
use App\Jobs\NewnewContactEnquiry;
use App\Models\City;
use Illuminate\Support\Facades\App as FacadesApp;

class HomeController extends Controller
{
    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    // public function __construct()
    // {
    //     $this->middleware('auth')->except('twitchWebhook'); 
    // }  


    public function cmsLang($en,$slug)
    { 
    
        $page = CmsPage::whereSlug($slug)->first();

        if(!$page)
            App::abort('404');

        else
            return view('front-user.pages.cms-lang',compact('page','en'));
    
    }
    public function cmsLangDe($slug)
    {
        
        $page = CmsPage::whereSlug($slug)->first();

        if(!$page)
            App::abort('404');

        else
            return view('front-user.pages.cms-lang',compact('page'));
    
    }
    public function homeCommingSoon(Request $request)
    { 
        $ipaddress = '';
        if (isset($_SERVER['HTTP_CLIENT_IP'])) {
            $ipaddress = $_SERVER['HTTP_CLIENT_IP'];
        } else if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ipaddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else if (isset($_SERVER['HTTP_X_FORWARDED'])) {
            $ipaddress = $_SERVER['HTTP_X_FORWARDED'];
        } else if (isset($_SERVER['HTTP_FORWARDED_FOR'])) {
            $ipaddress = $_SERVER['HTTP_FORWARDED_FOR'];
        } else if (isset($_SERVER['HTTP_FORWARDED'])) {
            $ipaddress = $_SERVER['HTTP_FORWARDED'];
        } else if (isset($_SERVER['REMOTE_ADDR'])) {
            $ipaddress = $_SERVER['REMOTE_ADDR'];
        } else {
            $ipaddress = 'UNKNOWN';
        }
        // echo $ipaddress;
        $json     = file_get_contents("http://ipinfo.io/$ipaddress/geo");
        $json     = json_decode($json, true);
        if(isset($json['country'])){
            $country  = $json['country']; 
            if($country == 'DE'){

                $pages = CmsPage::where('slug','privacy-policy')->first(); 
                $countries = Country::get(); 
                $category = Category::get();
                return view('front-user.pages.home-comming-soon-de',compact('countries','category','pages'));
            }else{
                $pages = CmsPage::where('slug','privacy-policy')->first(); 
                $countries = Country::get(); 
                $category = Category::get();
                return view('front-user.pages.home-comming-soon',compact('countries','category','pages'));
            } 
        }
        else{
            $pages = CmsPage::where('slug','privacy-policy')->first(); 
            $countries = Country::get(); 
            $category = Category::get();
            return view('front-user.pages.home-comming-soon',compact('countries','category','pages'));
        } 
    }
    public function homeCommingSoonEn(Request $request)
    {  
        $pages = CmsPage::where('slug','privacy-policy')->first(); 
        $countries = Country::get(); 
        $category = Category::get();
        return view('front-user.pages.home-comming-soon',compact('countries','category','pages')); 
    }

    public function homeCommingSoonDe(Request $request)
    { 
        $pages = CmsPage::where('slug','privacy-policy')->first(); 
        $countries = Country::get(); 
        $category = Category::get();
        return view('front-user.pages.home-comming-soon-de',compact('countries','category','pages'));
    }
    public function changeLanguage($lang)
    {
        
        App::setLocale($lang);
        session()->put('locale', $lang);
        return redirect()->back();
    }
    public function getCities(Request $request){
        $cities = DB::table('states')->leftjoin('cities',  'states.id', '=', 'cities.state_id' )
                    ->select('cities.*')
                    ->where('states.country_id', $request->country)->get();
        $data = '<option value=""></option>';
        foreach ($cities as $key => $row) {
            if($row->name != ''){
                $data .= "<option value=$row->id>$row->name</option>";
            }
        }
        return $data;
    }
    public function saveHomeCommingSoon(Request $request)
    { 
        try{
            $formData = request()->except(['_token']); 

            $password = str_random(10); 
            // $formData['password'] = Hash::make($password);

            // $this->validate($request, [ 
            //     'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],

            // ], [ 
            //     "email.required" => "Please enter :attribute",
            //     "email.unique" => "The email has already been registered."
            // ]); 
            $user = User::whereEmail($formData['email'])->first();

            if(!$user){

                $user_detail = array(  
                    'company_name' => isset($formData['company_name'])?$formData['company_name']:'', 
                    'user_type' => isset($formData['user_type'])?$formData['user_type']:'', 
                    'first_name' => isset($formData['first_name'])?$formData['first_name']:'', 
                    'last_name' =>isset($formData['last_name'])?$formData['last_name']:'', 
                    'email' =>isset($formData['email'])?$formData['email']:'',  
                    'category_id' => isset($formData['category_id'])?implode(',' , $formData['category_id']):'' , 
                    'phone' => isset($formData['phone'])?$formData['phone']:'' ,  
                    'country' => isset($formData['country'])?$formData['country']:'' ,  
                    'instagram_name' => isset($formData['instagram_name'])?$formData['instagram_name']:'' ,  
                    'twitter_name' => isset($formData['twitter_name'])?$formData['twitter_name']:'' ,  
                    'facebook_name' => isset($formData['facebook_name'])?$formData['facebook_name']:''  ,  
                    'youtube_name' => isset($formData['youtube_name'])?$formData['youtube_name']:'' ,  
                    'tiktok_name' => isset($formData['tiktok_name'])?$formData['tiktok_name']:'' ,  
                    'twitch_name' => isset($formData['twitch_name'])?$formData['twitch_name']:'' ,  
                    'receive_mails' => isset($formData['receive_mails'])?1:0 ,
                    'identification_no' => isset($formData['identification_no'])?$formData['identification_no']:null ,
                    'lang' => ucfirst(FacadesApp::getLocale()),
                    'country'  => isset($formData['country'])?$formData['country']:null,
                    'city'     => isset($formData['city'])?$formData['city']:null,
                    'street'   => isset($formData['street'])?$formData['street']:null,
                    'zip_code' => isset($formData['zip_code'])?$formData['zip_code']:null
                );   
              $user = User::create($user_detail);  
             
                // \Mail::send('emails.mail-template', ['data'=>$formData], function($message) use ($formData){
                //     $message->to($formData['email']);
                //     $message->subject(env('APP_NAME')." - Closed beta Application");
                //     $message->from($address = '<EMAIL>', $name = env('MAIL_FROM_NAME', env('APP_NAME')));  
                // });   

                dispatch(new NewUserWelcomeMail($user));

                //Notify the user for the registration
                // $user->notify(new RegistrationUser($user));

                //Notify the admin for the registration
                // $admin = User::whereUserType('admin')->first();
                // $admin->notify(new adminUserRegistration ($user,$admin));
                
                return response()->json(['code' => '200', 'msg' => 'Details saved successfully!!']);
            }else{
                return response()->json(['code' => '400', 'msg' => 'The email has already been registered.']);
            }
        } catch (\Exception $e) {
            return response()->json(['code' => '400', 'error' =>  $e->getMessage()]);
        }
    }


    public function saveHomeCommingSoonDe(Request $request)
    { 
        try{
            $formData = request()->except(['_token']); 

            $password = str_random(10); 
            // $formData['password'] = Hash::make($password);
            // $this->validate($request, [ 
            //     'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],

            // ], [ 
            //     "email.required" => "Please enter :attribute",
            //     "email.unique" => "Die E-Mail wurde bereits registriert"
            // ]);
            $user = User::whereEmail($formData['email'])->first();

            if(!$user){
                $user_detail = array(  
                    'company_name' => isset($formData['company_name'])?$formData['company_name']:'', 
                    'user_type' => isset($formData['user_type'])?$formData['user_type']:'', 
                    'first_name' => isset($formData['first_name'])?$formData['first_name']:'', 
                    'last_name' =>isset($formData['last_name'])?$formData['last_name']:'', 
                    'email' =>isset($formData['email'])?$formData['email']:'',  
                    'category_id' => isset($formData['category_id'])?implode(',' , $formData['category_id']):'' , 
                    // 'password' => $formData['password'] ,  
                    'phone' => isset($formData['phone'])?$formData['phone']:'' ,  
                    'country' => isset($formData['country'])?$formData['country']:'' ,  
                    'instagram_name' => isset($formData['instagram_name'])?$formData['instagram_name']:'' ,  
                    'twitter_name' => isset($formData['twitter_name'])?$formData['twitter_name']:'' ,  
                    'facebook_name' => isset($formData['facebook_name'])?$formData['facebook_name']:''  ,  
                    'youtube_name' => isset($formData['youtube_name'])?$formData['youtube_name']:'' ,  
                    'tiktok_name' => isset($formData['tiktok_name'])?$formData['tiktok_name']:'' ,  
                    'twitch_name' => isset($formData['twitch_name'])?$formData['twitch_name']:'' ,  
                    'receive_mails' => isset($formData['receive_mails'])?1:0  ,  
                    'lang' => 'De' 
                );   
                $user = User::create($user_detail);

                dispatch(new NewUserWelcomeMail($user));

                // \Mail::send('emails.mail-template-de', ['data'=>$formData], function($message) use ($formData){
                //     $message->to($formData['email']);
                //     $message->subject(env('APP_NAME')." - Closed beta Application");
                //     $message->from($address = '<EMAIL>', $name = env('MAIL_FROM_NAME', env('APP_NAME')));  
                // });
                //Notify the user for the registration
                // $user->notify(new RegistrationUser($user));

                //Notify the admin for the registration
                // $admin = User::whereUserType('admin')->first();
                // $admin->notify(new adminUserRegistration ($user,$admin));

                
                return response()->json(['code' => '200', 'msg' => 'Details saved successfully!!']); 
            }else{
                return response()->json(['code' => '400', 'msg' => 'The email has already been registered.']);
            }
        } catch (\Exception $e) {
            return response()->json(['code' => '400', 'error' =>  $e->getMessage()]);
        }
    } 
    public function unsubscribe(Request $request)
    {
        User::where('email',$request->id)->update(['activate'=>0]);
        return redirect('/')->with('success', 'Details saved successfully.');
    }

    public function subscribe(Request $request)
    {
        User::where('email',$request->id)->update(['activate'=>1]);
        return redirect('/'.$request->lang.'?subscribe=true');
    }

    public function timezone(Request $request)
    {
        $timezone = Session::get('timezone'); 
        return view('front-user.pages.setting',compact('timezone'));
    }


    public function setSession(Request $request)
    {
        Session::put($request->param, $request->value);
        return 1;
    }


    public function twitchWebhookCallback(Request $request, $provider=null)
    {
        \Log::info('webhook called123');
    }
    public function twitchWebhook(Request $request) 
    {  
        \Log::info('webhook called'); 

        $data = $request->all();
        \Log::info($data);


        // $json = file_get_contents('php://input'); $data = json_decode($json,true); switch ($data) { case $data["challenge"]!="": header('Content-Type: application/json'); echo $json; break; }


        // if($json = json_decode(file_get_contents("php://input"), true)) {
        //     print_r($json);
        //     $data = $json;
        // } else {
        //     print_r($_POST);
        //     $data = $_POST;
        // }
 
    }

    public function help()
    { 
        if(Auth::check()){ 
        return view('front-user.pages.help');
        }else{
            return redirect('/login')->with('error', 'Please login to view this page!.'); 

        }
        
    }


    public function verify_user($token)
    {
        $verifyUser = User::where('verify_token', $token)->first();
        if($verifyUser){
            $verifyUser->email_verified_at = Date('Y-m-d H:i:s');
            $verifyUser->save();

            Auth::login($verifyUser);
            return redirect('/')->with('success', 'Email verified successfully.'); 
        }else{
            return redirect('/')->with('error', 'Something went wrong!.'); 

        }
        
    }
    public function handleProviderCallback(Request $request, $provider=null)
    {  

        if($request->error!='')
            return "<script type='text/javascript'> self.close(); window.opener.refreshParentError();</script>";
        
        $result = SocialKeys::first();
        //try{ 
            if($provider=='twitch')
            { 
                $code = $request->code;
                $appId = $result->twitch_app_id;
                $secret = $result->twitch_app_secret;
                $redirectUri = config('app.url').$result->twitch_callback_url;
                // dd($redirectUri);

                // Get access token
                $ch = curl_init();

                curl_setopt($ch, CURLOPT_URL,"https://id.twitch.tv/oauth2/token");
                curl_setopt($ch, CURLOPT_POST, 1);
                //curl_setopt($ch, CURLOPT_POSTFIELDS, "postvar1=value1&postvar2=value2&postvar3=value3");

                // In real life you should use something like:
                curl_setopt($ch, CURLOPT_POSTFIELDS, 
                         http_build_query([ 
                        'client_id' => $appId,
                        'client_secret' => $secret,
                        'grant_type' => 'authorization_code',
                        'redirect_uri' => $redirectUri,
                        'code' => $code,]));

                // Receive server response ...
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

                $response = curl_exec($ch);

                curl_close ($ch);

                $content = json_decode($response); 
                 // dd($content);

                $videosApi = 'https://api.twitch.tv/helix/users'; 
                $ch = curl_init();

                curl_setopt_array($ch, array(
                    CURLOPT_HTTPHEADER => array(
                        "Accept: application/vnd.twitchtv.v5+json",
                        'Client-ID: ' . $appId,
                        "Authorization: Bearer ". $content->access_token
                    ),
                    CURLOPT_SSL_VERIFYPEER => false,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_URL => $videosApi
                ));

                $response = curl_exec($ch);
                curl_close($ch);

                $oAuth = json_decode($response, JSON_PRETTY_PRINT); 
                $videosApi = 'https://api.twitch.tv/helix/users/follows?to_id='.$oAuth['data'][0]['id']; 
                $ch = curl_init();

                curl_setopt_array($ch, array(
                    CURLOPT_HTTPHEADER => array(
                        "Accept: application/vnd.twitchtv.v5+json",
                        'Client-ID: ' . $appId,
                        "Authorization: Bearer ". $content->access_token
                    ),
                    CURLOPT_SSL_VERIFYPEER => false,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_URL => $videosApi
                ));

                $response = curl_exec($ch);
                curl_close($ch);

                $data = json_decode($response, JSON_PRETTY_PRINT);
                
                // $data = json_decode(file_get_contents('https://api.twitch.tv/helix/users/follows?to_id='.$oAuth['data'][0]['id'], true)); 

                // Get user name 
                $username = $oAuth['data'][0]['display_name'];
                $userId= $oAuth['data'][0]['id'];
                $picture= $oAuth['data'][0]['profile_image_url'];
     
                $userSocial = new \stdClass();
                $userSocial->email = isset($oAuth['data'][0]['email'])?$oAuth['data'][0]['email']:'';
                $userSocial->name = $username;
                $userSocial->id = $userId;
                $userSocial->profileUrl ="https://www.twitch.tv/".@$username; 
                $userSocial->followers_count = $data['total'];
                $userSocial->token = $oAuth['data'][0]['id'] ;
                $userSocial->tokenSecret = $content->refresh_token ;
            }
            elseif( $provider=='facebook' || $provider=='instagram')
                $userSocial = $this->configDriver($provider)->user(); 
            elseif( $provider=='youtube' )
                $userSocial = $this->configDriverGoogle($provider)->user(); 
            elseif( $provider=='twitter' )
                $userSocial = $this->configDriverTwitter($provider)->user(); 
            elseif( $provider=='snapchat' ){
                $userSocial = Socialite::driver($provider)->user(); 
                // $userSocial = Socialite::driver($provider)->fields(['friends'])->scopes(['email','user_birthday','user_friends','user_relationships','user_relationship_details'])->user();
                // $friends = $userSocial->getFriends();
            }
            else
                $userSocial = Socialite::driver($provider)->user();  
            // dd($userSocial);
            if($provider=='twitter'){
                $userSocial->profileUrl = "https://twitter.com/".@$userSocial->nickname;
            } 
            if($provider=='youtube')
            { 
                $youtube_subscribers = file_get_contents('https://www.googleapis.com/youtube/v3/channels?part=statistics&id='.$userSocial->id.'&key='.$result->youtube_app_key);
                $youtube_api_response = json_decode($youtube_subscribers, true );
                //dd($youtube_api_response);
                $userSocial->followers_count = intval($youtube_api_response['items'][0]['statistics']['subscriberCount']);

                $userSocial->profileUrl = "https://www.youtube.com/channel/".@$userSocial->id; 

                $userSocial->name =  $userSocial->nickname ;
                $userSocial->token = $userSocial->id ;

            } 
            if(@Session::get('provider')=='instagram')
            {
                $provider = 'instagram';
                $url = "https://graph.facebook.com/".@$userSocial->pages[0]['id']."?fields=instagram_business_account&access_token=".@$userSocial->token;
                $client = new Client();
                $response = $client->request('GET', $url);
                $content = $response->getBody()->getContents();
                $oAuth = json_decode($content);
                $insta_user_id = @$oAuth->instagram_business_account->id;

                if($insta_user_id=='')
                {
                    Session::put('influencer_follower_error', 'Invalid page or instagram account selected'); 
                    return "<script type='text/javascript'> self.close(); window.opener.refreshParentError();</script>";
                }
                $url = "https://graph.facebook.com/".$insta_user_id."?fields=id,name,username,profile_picture_url,followers_count&access_token=".@$userSocial->token;
                $client = new Client();
                $response = $client->request('GET', $url);
                $content = $response->getBody()->getContents();
                $oAuth = json_decode($content);

                //$userSocial = new \stdClass();
                $userSocial->name = (@$oAuth->username)?@$oAuth->username:@$oAuth->name;
                $userSocial->followers_count = @$oAuth->followers_count;
                $userSocial->profileUrl = "https://www.instagram.com/".@$oAuth->username;
                $picture = @$oAuth->profile_picture_url;
                
                 
                $userSocial->tokenSecret = $insta_user_id  ;
                
            }
            elseif($provider=='facebook')
            { 
                try{
                    $url = "https://graph.facebook.com/".@$userSocial->pages[0]['id']."?fields=id,name,username,picture,followers_count,link&access_token=".@$userSocial->token;
                    $client = new Client();
                    $response = $client->request('GET', $url);
                    $content = $response->getBody()->getContents();
                    $oAuth = json_decode($content);

                    //$userSocial = new \stdClass();
                    $userSocial->name = (@$oAuth->username)?@$oAuth->username:@$oAuth->name;
                    $userSocial->followers_count = @$oAuth->followers_count;
                    $userSocial->profileUrl = @$oAuth->link;
                    $picture = @$oAuth->picture->data->url;
                    
                    
                    $userSocial->tokenSecret = @$userSocial->pages[0]['id'] ; 
                }
                
                catch(\Exception $e){
                    Session::put('influencer_error','Facebook connect did not work. Please give the needed rights. Click here for more info.');
                
                    return "<script type='text/javascript'> self.close(); window.opener.refreshParentError();</script>";
                }
                
                // $userSocial->name = @$userSocial->pages[0]['name'];
                // $userSocial->profileUrl = @$userSocial->pages[0]['link'];
                // $picture = 'https://graph.facebook.com/'.@$userSocial->pages[0]['id'].'/picture?type=square&access_token='.@$userSocial->token;
                
                // dd($userSocial);
            }
            // return redirect('login')->with(['userSocial'=>$userSocial,'provider'=>$provider]);

            $name = @$userSocial->name!=''?@$userSocial->name:@$userSocial->nickname;
            // echo "Name: ". $name;
            // echo nl2br('<br>');
            $avatar = @$userSocial->avatar_original!=''?@$userSocial->avatar_original:@$userSocial->avatar;
            // echo "Picture: ". $avatar;
            // echo nl2br('<br>');
            // // echo "Email: ". @$userSocial->email;
            // // echo nl2br('<br>');
            // echo "URL: ". @$userSocial->profileUrl;
            // echo nl2br('<br>');
            // echo "Followers: ". @$userSocial->followers_count;
            // echo nl2br('<br>');

            // if($provider=='youtube')
            //     echo "Youtube channel: https://www.youtube.com/channel/".@$userSocial->id;
            // elseif($provider=='facebook')
            // {
            //     echo "Facebook page(s):";
            //     echo nl2br('<br>');
            //     foreach (@$userSocial->pages as $page) {
            //         echo $page['link'];
            //         echo nl2br('<br>');
            //     }
            // }  
            $name = (@$userSocial->nickname)?@$userSocial->nickname:@$userSocial->name ; 
            $socialconnect = SocialConnect::where('media',$provider)->where('name',$name)->first(); 
            if ($socialconnect == null)
            {      
                
               $name = (@$userSocial->nickname)?@$userSocial->nickname:@$userSocial->name ;
                // dd($name);
                if($provider=='twitter')
                {  
                    @$userSocial->followers_count = @$userSocial->user['followers_count'];
                }  


                $mode = Mode::first();
                if($mode){
                    if($mode->status == 1){
                        $influencer_count = $mode->production_influencer ;
                    }else{
                        $influencer_count = $mode->testing_influencer ;
                    } 
                }else{
                    $influencer_count = 0;
                } 
                if(@$userSocial->followers_count < $influencer_count){
                    Session::put('influencer_follower_error', 'Need at least '.$influencer_count.' Follower(s)'); 
                    return "<script type='text/javascript'> self.close(); window.opener.refreshParentError();</script>";
                }else{
                    
                    if(isset($picture)){
                        $fileContents = file_get_contents( $picture );
                        $filename = str_random(40);
                        Storage::disk('public')->put('social_pics/'.$filename.'.jpg', $fileContents);
                    }else{
                        $fileContents = file_get_contents(str_replace('http://', 'https://', @$userSocial->getAvatar()));
                        $filename = str_random(40);
                        Storage::disk('public')->put('social_pics/'.$filename.'.jpg', $fileContents);
                    }
 
                    $user = SocialConnect::updateOrCreate([
                        'user_id'=>Auth::id(), 
                        'name'=>$name,  
                        'media'=>$provider, 
                        'picture'=> 'social_pics/'.$filename.'.jpg',
                        'url'=>@$userSocial->profileUrl, 
                        'followers'=>@$userSocial->followers_count, 
                        'token'=>@$userSocial->token, 
                        'token_secret'=>@$userSocial->tokenSecret
                    ]);  
                } 
            } else{  
                $user = User::find(Auth::id());
                $sender_user = User::find($socialconnect->user_id);
                
                dispatch(new NewsocialAlreadyUsed($sender_user,$user));

                $sender_user->notify(new socialAlreadyUsed($sender_user,$user));
                Session::put('influencer_error','This '.$provider.' account was already linked with another clickitfame-account.Please contact the support, if you think this should have not happened!!');
                
                return "<script type='text/javascript'> self.close(); window.opener.refreshParentError();</script>";
            }

            Session::forget('influencer_follower_error');
            Session::forget('influencer_error');
            // dd(Session::get('influencer_error'));
            return "<script type='text/javascript'> self.close(); window.opener.refreshParent();</script>";
            // return redirect('/influencer')->with('success', 'Connected successfully!');
        // } catch (\Exception $e) {
        //     return back()->with('error', 'Something went wrong!');
        // }    
    }   


    /**
     * Convert a number to its text representation.
     *
     * @param float $number The number to convert
     * @return string The text representation of the number
     */
    function convertNumberToText(float $number): string
    {
        // Use PHP's built-in NumberFormatter for clean, reliable conversion
        if (class_exists('NumberFormatter')) {
            $formatter = new \NumberFormatter('en', \NumberFormatter::SPELLOUT);
            $result = $formatter->format($number);
            // Replace hyphens with spaces for better readability
            return str_replace('-', ' ', $result);
        }

        // Fallback implementation if NumberFormatter is not available
        return $this->convertNumberToTextFallback($number);
    }

    /**
     * Fallback method to convert numbers to text when NumberFormatter is not available.
     *
     * @param float $number
     * @return string
     */
    private function convertNumberToTextFallback(float $number): string
    {
        if ($number == 0) return 'zero';

        $integerPart = (int) $number;
        $decimalPart = round(($number - $integerPart) * 100);

        $result = $this->convertIntegerToText($integerPart);

        if ($decimalPart > 0) {
            $result .= ' point ' . $this->convertIntegerToText($decimalPart);
        }

        return trim($result);
    }

    /**
     * Convert integer part to text.
     *
     * @param int $number
     * @return string
     */
    private function convertIntegerToText(int $number): string
    {
        if ($number == 0) return '';

        $ones = ['', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine'];
        $teens = ['ten', 'eleven', 'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen'];
        $tens = ['', '', 'twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety'];
        $scales = ['', 'thousand', 'million', 'billion', 'trillion'];

        if ($number < 10) {
            return $ones[$number];
        } elseif ($number < 20) {
            return $teens[$number - 10];
        } elseif ($number < 100) {
            return trim($tens[intval($number / 10)] . ' ' . $ones[$number % 10]);
        } elseif ($number < 1000) {
            return trim($ones[intval($number / 100)] . ' hundred ' . $this->convertIntegerToText($number % 100));
        } else {
            $scaleIndex = 0;
            $result = '';

            while ($number > 0) {
                $chunk = $number % 1000;
                if ($chunk != 0) {
                    $chunkText = $this->convertIntegerToText($chunk);
                    if ($scaleIndex > 0) {
                        $chunkText .= ' ' . $scales[$scaleIndex];
                    }
                    $result = trim($chunkText . ' ' . $result);
                }
                $number = intval($number / 1000);
                $scaleIndex++;
            }

            return trim($result);
        }
    }

    public function index()
    {
        $blogs = Blog::whereStatus(1)->orderBy('created_at','desc')->paginate(3);
        $lang = App::getLocale();
        $faqs = Faq::whereStatus(1)->get();
        $faqtopic = FaqTopic::where('lang', ucfirst($lang))->get();

        if(Auth::check()) {
            $faqs = Faq::whereStatus(1)->get();
            App::setlocale('en');
            $faqtopic = FaqTopic::where('lang',"En")->get();
            
            $influencerDataActiveList = InfluencerRequestDetail::join(
                'influencer_details',
                'influencer_request_details.influencer_detail_id', '=',
                'influencer_details.id'
            )
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->leftjoin(
                'influencer_request_accepts',
                'influencer_request_accepts.influencer_request_detail_id', '=',
                'influencer_request_details.id'
            )
            ->select('influencer_request_details.*')
            ->where('influencer_request_details.invoice_id', '!=', '')
            ->where('influencer_request_details.user_id', Auth::id())
            ->groupBy('influencer_request_details.compaign_id')
            ->orderBy('influencer_request_details.updated_at', 'desc')
            ->get();  
            
            $influencerDataActive = 0 ;
            foreach($influencerDataActiveList as $row) {
                if(
                    $row->review !=1 &&
                    $row->is_complained !=1 &&
                    $row->invoice_id != null &&
                    $row->refund_reason == null &&
                    isset($row->social_posts) &&
                    $row->social_posts != null
                ) {
                    $influencerDataActive++;
                } 
            }  

            $influencerDataActive = $this->convertNumberToText($influencerDataActive);
            $influencerData = InfluencerRequestDetail::join(
                'influencer_details',
                'influencer_request_details.influencer_detail_id', '=',
                'influencer_details.id'
            )->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->leftjoin(
                'influencer_request_accepts',
                'influencer_request_accepts.influencer_request_detail_id', '=',
                'influencer_request_details.id'
            )->select(
                'influencer_request_details.*',
                'influencer_details.id as i_id',
                'influencer_details.user_id as i_user_id',
                'influencer_request_accepts.request',
                'influencer_request_accepts.id as influencer_request_accept_id',
                DB::raw('COUNT(influencer_request_accepts.id) AS total_count'),
                DB::raw('SUM(influencer_request_accepts.request) AS accept_request'),
                DB::raw('COUNT(influencer_request_details.id) AS total_influencer_count')
            )
            ->where('influencer_request_details.user_id', Auth::id())
            ->where('influencer_request_details.status', NULL)
            ->groupBy('influencer_request_details.compaign_id')
            ->orderBy('influencer_request_details.id', 'desc');

            $influencerDataList = $influencerData->get();
            $pendingSubmit = 0;
            foreach($influencerDataList as $row) {
                if($row->review != '2' && $row->finish !='0' && $row->request == '1') {
                    $pendingSubmit++;
                }
            }

            $pendingSubmit = $this->convertNumberToText($pendingSubmit);

            $Influencer = App\Models\InfluencerDetail::where('user_id',Auth::id())->first(); 

            $tot_count = InfluencerRequestDetail::where('influencer_detail_id',@$Influencer->id)
                            ->where('finish',NULL)->where('refund_reason',NULL)
                            ->where(function ($query) {
                                $query->where('review', '=', NULL)
                                      ->orWhere('review', '=', 0);
                            })
                            ->count(); 


            $reqCountList =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id' )   
                        ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id' )    
                        ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )  
                         ->select('influencer_request_details.*', 'influencer_details.id as i_id','influencer_details.user_id as i_user_id','influencer_request_accepts.request','influencer_request_accepts.request_time','influencer_request_accepts.request_time_accept','influencer_request_accepts.id as influencer_request_accept_id' )
                        ->where('influencer_details.user_id',Auth::id())
                        ->orderBy('influencer_request_details.id','desc')->get() ;

            $campaignRequestTime = CampaignRequestTime::first();            

            // $reqCount =$tot_count-$open_count;
             $reqCount = 0 ;
            foreach($reqCountList as $row){
                if($row->invoice_id=='' && $row->review != '2' && $row->refund_reason == ''){  
                    $time = (isset($row->request_time_accept) && $row->request_time_accept == 1)?$row->request_time+$row->time:$row->time;

                    $created_date =  date('Y-m-d H:i:s',strtotime($row->created_at));
                    $updated_date =  date('Y-m-d H:i:s',strtotime($row->updated_at));
                    $campaignDate= date('Y-m-d H:i:s', strtotime($created_date. ' + '.$campaignRequestTime->request_time.' days'));  
                    $date = date('Y-m-d H:i:s'); 
                    $seconds = strtotime($campaignDate) - strtotime($date);

                    $days = floor($seconds / 86400);  
                    if($date <= $campaignDate ) {
                        $reqCount++; 
                    }
                }

            } 

            $reqCount = $this->convertNumberToText($reqCount);

            $postCountList = InfluencerRequestDetail::join(
                'influencer_details',
                'influencer_request_details.influencer_detail_id', '=',
                'influencer_details.id'
            )
            ->leftjoin('users', 'users.id', '=', 'influencer_details.user_id')
            ->leftjoin(
                'influencer_request_accepts',
                'influencer_request_accepts.influencer_request_detail_id', '=',
                'influencer_request_details.id'
            )
            ->select(
                'influencer_request_details.*',
                'influencer_details.id as i_id',
                'influencer_details.user_id as i_user_id',
                'influencer_request_accepts.request',
                'influencer_request_accepts.request_time',
                'influencer_request_accepts.request_time_accept',
                'influencer_request_accepts.id as influencer_request_accept_id',
                'influencer_request_accepts.created_at as accept_time'
            )
            ->where('influencer_details.user_id', Auth::id())
            ->orderBy('influencer_request_details.id','desc')
            ->get();

            $postCount = 0 ; 
            foreach($postCountList as $row) {
                if(
                    $row->request == 1 &&
                    $row->invoice_id !='' &&
                    $row->review != '1' &&
                    $row->review !='0' &&
                    $row->refund_reason == '' &&
                    $row->finish != '1' &&
                    $row->social_post_id == ''
                ) {
                    $postCount++;
                }
            }         

            $postCount = $this->convertNumberToText($postCount);
                             
        } else {
            $influencerDataActive = 0;
            $reqCount = 0;
            $postCount = 0;
            $pendingSubmit = 0;
        }
        
        $countries = Country::get(); 
        $category = Category::get();
        $pages = CmsPage::where('slug','privacy-policy')->first();

        if (!session()->has('locale') && !Auth::check()) {
            $ipaddress = '';

            if (isset($_SERVER['HTTP_CLIENT_IP'])) {
                $ipaddress = $_SERVER['HTTP_CLIENT_IP'];
            } else if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
                $ipaddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
            } else if (isset($_SERVER['HTTP_X_FORWARDED'])) {
                $ipaddress = $_SERVER['HTTP_X_FORWARDED'];
            } else if (isset($_SERVER['HTTP_FORWARDED_FOR'])) {
                $ipaddress = $_SERVER['HTTP_FORWARDED_FOR'];
            } else if (isset($_SERVER['HTTP_FORWARDED'])) {
                $ipaddress = $_SERVER['HTTP_FORWARDED'];
            } else if (isset($_SERVER['REMOTE_ADDR'])) {
                $ipaddress = $_SERVER['REMOTE_ADDR'];
            } else {
                $ipaddress = 'UNKNOWN';
            }

            $json = file_get_contents("http://ipinfo.io/$ipaddress/geo");
            $json = json_decode($json, true);
            if(isset($json['country'])) {
                $country = $json['country'];  
                if($country == 'DE') {
                    App::setLocale('de');
                    session()->put('locale', 'de');
                    $faqs = Faq::whereStatus(1)->get();
                    $faqtopic = FaqTopic::where('lang','De')->get();
                } else {
                    App::setLocale('en');
                    session()->put('locale', 'en');
                    $faqs = Faq::whereStatus(1)->get();
                    $faqtopic = FaqTopic::where('lang','En')->get();
                } 

                return view('front-user.pages.home', compact(
                    'blogs',
                    'faqs',
                    'influencerDataActive',
                    'reqCount',
                    'postCount',
                    'pendingSubmit',
                    'countries',
                    'category',
                    'pages',
                    'faqtopic'
                ));
            }
            else
            {
                return view('front-user.pages.home', compact(
                    'blogs',
                    'faqs',
                    'influencerDataActive',
                    'reqCount',
                    'postCount',
                    'pendingSubmit',
                    'countries',
                    'category',
                    'pages',
                    'faqtopic'
                ));
            }
        }
        else
        {
            return view('front-user.pages.home', compact(
                'blogs',
                'faqs',
                'influencerDataActive',
                'reqCount',
                'postCount',
                'pendingSubmit',
                'countries',
                'category',
                'pages',
                'faqtopic'
            ));
        }
    }

    public function indexDe()
    {
        $blogs = Blog::whereStatus(1)->orderBy('created_at','desc')->paginate(3);
        $faqs = Faq::whereStatus(1)->get();

        $faqtopic = FaqTopic::get();


        if(Auth::check()){
            $influencerDataActiveList =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id' )   
                        ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id' )    
                        ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )  
                        ->select('influencer_request_details.*')
                        ->where('influencer_request_details.social_post_id','!=','')
                        ->where('influencer_request_details.invoice_id','!=', '')
                        ->where('influencer_request_details.user_id',Auth::id())
                        ->groupBy('influencer_request_details.compaign_id')
                        ->orderBy('influencer_request_details.updated_at','desc')->get();  
            $influencerDataActive = 0 ;
            foreach($influencerDataActiveList as $row){
                if( $row->invoice_id != NULL && $row->finish != '1' ){
                    $influencerDataActive++;
                } 
            }  
            $influencerDataActive = $this->convertNumberToText($influencerDataActive);
 

            $influencerData =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id' )   
                        ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id' )    
                        ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )  
                        ->select('influencer_request_details.*', 'influencer_details.id as i_id','influencer_details.user_id as i_user_id','influencer_request_accepts.request','influencer_request_accepts.id as influencer_request_accept_id', DB::raw('COUNT(influencer_request_accepts.id) AS total_count'), DB::raw('SUM(influencer_request_accepts.request) AS accept_request') , DB::raw('COUNT(influencer_request_details.id) AS total_influencer_count'))
                        ->where('influencer_request_details.user_id',Auth::id())
                        ->where('influencer_request_details.status',NULL) 
                        ->groupBy('influencer_request_details.compaign_id')
                        ->orderBy('influencer_request_details.id','desc') ;
      
            $influencerDataList =$influencerData->get();    
            $pendingSubmit = 0 ;
            foreach($influencerDataList as $row){
                if( $row->review != '2' && $row->finish !='0' ){
                    $pendingSubmit++;
                }

            } 
            

            // $influencerData =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id' )   
            //             ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id' )    
            //             ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )  
            //             ->select('influencer_request_details.*', 'influencer_details.id as i_id','influencer_details.user_id as i_user_id','influencer_request_accepts.request','influencer_request_accepts.id as influencer_request_accept_id', DB::raw('COUNT(influencer_request_accepts.id) AS total_count'), DB::raw('SUM(influencer_request_accepts.request) AS accept_request') , DB::raw('COUNT(influencer_request_details.id) AS total_influencer_count'))
            //             ->where('influencer_request_details.user_id',Auth::id())
            //             ->where('influencer_request_details.status',NULL)  
            //             ->groupBy('influencer_request_details.compaign_id')
            //             ->orderBy('influencer_request_details.id','desc') ;
      
            // $pendingSubmit =$influencerData->get()->count();   

            // $pendingSubmit =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id' )   
            //                 ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id' )    
            //                 ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )  
            //                 ->select('influencer_request_details.*')
            //                 ->where('influencer_request_details.user_id',Auth::id())
            //                 ->where('influencer_request_details.status',NULL)   
            //                 ->groupBy('influencer_request_details.compaign_id')
            //                 ->count();
            $pendingSubmit = $this->convertNumberToText($pendingSubmit);


             // $reqCount =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id' )   
             //                ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id' )    
             //                ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )  
             //                ->select('influencer_request_details.*','influencer_request_accepts.request')
             //                ->where('influencer_details.user_id',Auth::id())
             //                ->where('influencer_request_accepts.request', '1')
             //                ->where('influencer_request_details.invoice_id','!=','')
             //                ->where('influencer_request_details.review','!=',1)
             //                ->where('influencer_request_details.refund_reason','') 
             //                ->where('influencer_request_details.finish','!=',1)
             //                ->groupBy('influencer_request_details.compaign_id')
             //                ->orderBy('influencer_request_details.updated_at','desc')->count();

            $Influencer = App\Models\InfluencerDetail::where('user_id',Auth::id())->first(); 

            $tot_count = InfluencerRequestDetail::where('influencer_detail_id',@$Influencer->id)
                            ->where('finish',NULL)->where('refund_reason',NULL)
                            ->where(function ($query) {
                                $query->where('review', '=', NULL)
                                      ->orWhere('review', '=', 0);
                            })
                            ->count(); 


            $open_count =  InfluencerRequestDetail::join('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )  
                             ->select('influencer_request_details.*')
                             ->where('influencer_request_details.influencer_detail_id',@$Influencer->id)
                             ->where('influencer_request_details.finish',NULL)
                             ->where('influencer_request_details.refund_reason',NULL) 
                            ->where(function ($query) {
                                $query->where('influencer_request_details.review', '=', NULL)
                                      ->orWhere('influencer_request_details.review', '=', 0);
                            })            
                             ->count();  


            $reqCount =$tot_count-$open_count;
            $reqCount = $this->convertNumberToText($reqCount); 


            $postCount =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id' )   
                            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id' )    
                            ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )  
                            ->select('influencer_request_details.*')
                            ->where('influencer_request_details.review','!=',1)
                            ->where('influencer_request_details.finish','!=',1)
                            ->where('influencer_request_details.social_post_id','!=',null)
                            ->where('influencer_details.user_id',Auth::id())
                            ->groupBy('influencer_request_details.compaign_id')
                            ->orderBy('influencer_request_details.updated_at','desc')->count(); 
            $postCount = $this->convertNumberToText($postCount);
                             
        }else{
            $influencerDataActive = 0;
            $reqCount = 0;
            $postCount = 0;
            $pendingSubmit = 0;
        }
        $countries = Country::get(); 
        $category = Category::get();
        $pages = CmsPage::where('slug','privacy-policy')->first(); 
        return view('front-user.pages.home-de',compact('blogs','faqs','influencerDataActive','reqCount','postCount','pendingSubmit','countries','category','pages','faqtopic'));
    }


    public function indexEn()
    {
        $blogs = Blog::whereStatus(1)->orderBy('created_at','desc')->paginate(3);
        $faqs = Faq::whereStatus(1)->get();

        $faqtopic = FaqTopic::get();


        if(Auth::check()){
            $influencerDataActiveList =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id' )   
                        ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id' )    
                        ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )  
                        ->select('influencer_request_details.*')
                        ->where('influencer_request_details.social_post_id','!=','')
                        ->where('influencer_request_details.invoice_id','!=', '')
                        ->where('influencer_request_details.user_id',Auth::id())
                        ->groupBy('influencer_request_details.compaign_id')
                        ->orderBy('influencer_request_details.updated_at','desc')->get();  
            $influencerDataActive = 0 ;
            foreach($influencerDataActiveList as $row){
                if( $row->invoice_id != NULL && $row->finish != '1' ){
                    $influencerDataActive++;
                } 
            }  
            $influencerDataActive = $this->convertNumberToText($influencerDataActive);
 

            $influencerData =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id' )   
                        ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id' )    
                        ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )  
                        ->select('influencer_request_details.*', 'influencer_details.id as i_id','influencer_details.user_id as i_user_id','influencer_request_accepts.request','influencer_request_accepts.id as influencer_request_accept_id', DB::raw('COUNT(influencer_request_accepts.id) AS total_count'), DB::raw('SUM(influencer_request_accepts.request) AS accept_request') , DB::raw('COUNT(influencer_request_details.id) AS total_influencer_count'))
                        ->where('influencer_request_details.user_id',Auth::id())
                        ->where('influencer_request_details.status',NULL) 
                        ->groupBy('influencer_request_details.compaign_id')
                        ->orderBy('influencer_request_details.id','desc') ;
      
            $influencerDataList =$influencerData->get();    
            $pendingSubmit = 0 ;
            foreach($influencerDataList as $row){
                if( $row->review != '2' && $row->finish !='0' ){
                    $pendingSubmit++;
                }

            } 
            

            // $influencerData =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id' )   
            //             ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id' )    
            //             ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )  
            //             ->select('influencer_request_details.*', 'influencer_details.id as i_id','influencer_details.user_id as i_user_id','influencer_request_accepts.request','influencer_request_accepts.id as influencer_request_accept_id', DB::raw('COUNT(influencer_request_accepts.id) AS total_count'), DB::raw('SUM(influencer_request_accepts.request) AS accept_request') , DB::raw('COUNT(influencer_request_details.id) AS total_influencer_count'))
            //             ->where('influencer_request_details.user_id',Auth::id())
            //             ->where('influencer_request_details.status',NULL)  
            //             ->groupBy('influencer_request_details.compaign_id')
            //             ->orderBy('influencer_request_details.id','desc') ;
      
            // $pendingSubmit =$influencerData->get()->count();   

            // $pendingSubmit =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id' )   
            //                 ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id' )    
            //                 ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )  
            //                 ->select('influencer_request_details.*')
            //                 ->where('influencer_request_details.user_id',Auth::id())
            //                 ->where('influencer_request_details.status',NULL)   
            //                 ->groupBy('influencer_request_details.compaign_id')
            //                 ->count();
            $pendingSubmit = $this->convertNumberToText($pendingSubmit);


             // $reqCount =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id' )   
             //                ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id' )    
             //                ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )  
             //                ->select('influencer_request_details.*','influencer_request_accepts.request')
             //                ->where('influencer_details.user_id',Auth::id())
             //                ->where('influencer_request_accepts.request', '1')
             //                ->where('influencer_request_details.invoice_id','!=','')
             //                ->where('influencer_request_details.review','!=',1)
             //                ->where('influencer_request_details.refund_reason','') 
             //                ->where('influencer_request_details.finish','!=',1)
             //                ->groupBy('influencer_request_details.compaign_id')
             //                ->orderBy('influencer_request_details.updated_at','desc')->count();

            $Influencer = App\Models\InfluencerDetail::where('user_id',Auth::id())->first(); 

            $tot_count = InfluencerRequestDetail::where('influencer_detail_id',@$Influencer->id)
                            ->where('finish',NULL)->where('refund_reason',NULL)
                            ->where(function ($query) {
                                $query->where('review', '=', NULL)
                                      ->orWhere('review', '=', 0);
                            })
                            ->count(); 


            $open_count =  InfluencerRequestDetail::join('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )  
                             ->select('influencer_request_details.*')
                             ->where('influencer_request_details.influencer_detail_id',@$Influencer->id)
                             ->where('influencer_request_details.finish',NULL)
                             ->where('influencer_request_details.refund_reason',NULL) 
                            ->where(function ($query) {
                                $query->where('influencer_request_details.review', '=', NULL)
                                      ->orWhere('influencer_request_details.review', '=', 0);
                            })            
                             ->count();  


            $reqCount =$tot_count-$open_count;
            $reqCount = $this->convertNumberToText($reqCount); 


            $postCount =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id' )   
                            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id' )    
                            ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )  
                            ->select('influencer_request_details.*')
                            ->where('influencer_request_details.review','!=',1)
                            ->where('influencer_request_details.finish','!=',1)
                            ->where('influencer_request_details.social_post_id','!=',null)
                            ->where('influencer_details.user_id',Auth::id())
                            ->groupBy('influencer_request_details.compaign_id')
                            ->orderBy('influencer_request_details.updated_at','desc')->count(); 
            $postCount = $this->convertNumberToText($postCount);
                             
        }else{
            $influencerDataActive = 0;
            $reqCount = 0;
            $postCount = 0;
            $pendingSubmit = 0;
        }
        $countries = Country::get(); 
        $category = Category::get();
        $pages = CmsPage::where('slug','privacy-policy')->first(); 
        return view('front-user.pages.home',compact('blogs','faqs','influencerDataActive','reqCount','postCount','pendingSubmit','countries','category','pages','faqtopic'));
    }
    public function contact(Request $request)
    {
        $admin = User::whereUserType('admin')->first();

        $details = request()->except(['_token']);

        dispatch(new NewnewContactEnquiry($admin, $details));


        $admin->notify(new newContactEnquiry($admin, $details));

        return redirect()->back()->with('success', 'Your enquiry is submitted successfully. Admin will get back to you soon.');
    }

    public function blog(Request $request)
    {
        $blogs = Blog::whereStatus(1)->orderBy('created_at','desc')->paginate(3);

        foreach ($blogs as $blog) {
            $blog->content = $this->html_cut($blog->description,300);
        }

        if ($request->ajax()) {
            return \Response::json(\View::make('front-user.pages.blog-ajax', compact('blogs'))->render());
        }

        $latestBlogs = $this->getLatestBlogs($request);

        return view('front-user.pages.blog',compact('blogs','latestBlogs'));
    }

    function html_cut($text, $max_length)
    {
        $tags   = array();
        $result = "";

        $is_open   = false;
        $grab_open = false;
        $is_close  = false;
        $in_double_quotes = false;
        $in_single_quotes = false;
        $tag = "";

        $i = 0;
        $stripped = 0;

        $stripped_text = strip_tags($text);

        while ($i < strlen($text) && $stripped < strlen($stripped_text) && $stripped < $max_length)
        {
            $symbol  = $text[$i];
            $result .= $symbol;

            switch ($symbol)
            {
               case '<':
                    $is_open   = true;
                    $grab_open = true;
                    break;

               case '"':
                   if ($in_double_quotes)
                       $in_double_quotes = false;
                   else
                       $in_double_quotes = true;

                break;

                case "'":
                  if ($in_single_quotes)
                      $in_single_quotes = false;
                  else
                      $in_single_quotes = true;

                break;

                case '/':
                    if ($is_open && !$in_double_quotes && !$in_single_quotes)
                    {
                        $is_close  = true;
                        $is_open   = false;
                        $grab_open = false;
                    }

                    break;

                case ' ':
                    if ($is_open)
                        $grab_open = false;
                    else
                        $stripped++;

                    break;

                case '>':
                    if ($is_open)
                    {
                        $is_open   = false;
                        $grab_open = false;
                        array_push($tags, $tag);
                        $tag = "";
                    }
                    else if ($is_close)
                    {
                        $is_close = false;
                        array_pop($tags);
                        $tag = "";
                    }

                    break;

                default:
                    if ($grab_open || $is_close)
                        $tag .= $symbol;

                    if (!$is_open && !$is_close)
                        $stripped++;
            }

            $i++;
        }
        $result .= '...';
        
        while ($tags)
            $result .= "</".array_pop($tags).">";

        return $result;
    }

    public function getLatestBlogs(Request $request)
    {
        $condition = " 1 = 1 ";
        if(isset($request->keyword) && $request->keyword != '')
            $condition .= " and (title like '%".$request->keyword."%' or description like '%".$request->keyword."%')";

        $latestBlogs = Blog::whereRaw($condition)->whereStatus(1)->orderBy('created_at','desc')->take(5)->get();

        foreach ($latestBlogs as $blog) {
            $blog->content = $this->html_cut($blog->description,100);
        }

        if ($request->ajax()) {
            return \Response::json(\View::make('front-user.pages.latest-blog-ajax', compact('latestBlogs'))->render());
        }

        return $latestBlogs;

    }

    public function blogDetail($slug='')
    {
        $blog = Blog::whereSlug($slug)->first();

        if(!$blog)
            App::abort('404');

        else
            return view('front-user.pages.blog-individual',compact('blog'));
    }

    public function faqs(Request $request)
    {
        if(Auth::check())
        $lang            = "en";
        else
        $lang            = App::getLocale();

        $faqs = Faq::whereStatus(1)->get();

        $faqtopic = FaqTopic::where('lang',ucfirst($lang))->get();

        return view('front-user.pages.faq', compact('faqs','faqtopic'));
    }

    public function cms($slug)
    {

        $page = CmsPage::whereSlug($slug)->first();

        if(!$page)
            App::abort('404');

        else
            return view('front-user.pages.cms',compact('page'));
    
    }

    public function certificate()
    {
        $attendee = Attendee::wherePaymentStatus('Completed')
                        ->whereHas('course', function($q) {
                            $q->whereRaw("SUBSTRING_INDEX(due_date,',',-1) <= '".date('Y-m-d', strtotime(' -1 day'))."'");
                        })
                        ->where('attended',1)
                        ->first();

        $data['attendee'] = $attendee;
        $certificateTime = time();

        $path = public_path('storage/app/certificates');

        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }

        $pdfName = 'certificate'.$certificateTime.'.pdf'; 
        $pdf = PDF::loadView('front-user.common.class-certificate', $data)->setPaper('a4', 'landscape');    
        $pdf->save(public_path('storage/app/certificates/'.$pdfName));

        $file_url = public_path('storage/app/certificates/'.$pdfName);
        header('Content-Type: application/octet-stream');
        header("Content-Transfer-Encoding: Binary"); 
        header("Content-disposition: attachment; filename=\"" . basename($file_url) . "\""); 
        readfile($file_url); 

        return view('front-user.pages.home');
    }

    public function payments()
    {
        \Stripe\Stripe::setApiKey(config('settings.env.STRIPE_SECRET'));

        \Stripe\ApplePayDomain::create([
            'domain_name' => config('app.url')
        ]);

        $stripe = new \Stripe\StripeClient(config('settings.env.STRIPE_SECRET'));
        $intent = $stripe->paymentIntents->create(
          [
            'amount' => 1099,
            'currency' => 'eur',
            'payment_method_types' => ['card'],
          ]
        );

        return view('front-user.pages.payments' , compact('intent',));
    }

    public function stripeTest(Request $request)
    {
        return view('front-user.pages.stripe-test');
    }

    public function saveStripeTest(Request $request)
    {
        // dd($request->all());
        $stripe = new \Stripe\StripeClient('sk_test_51NVpfnAZVK2wbGXRJY4hgKOVDT1uI6Ki4WgAUeOUhpK55kNu8ILjvXNyMQW2qCVp18FvhTCBpoeejAMFB8p6FnYF00b9uP11Wb');
        $token = $request->get('stripeToken');
        $result = $stripe->paymentIntents->create([
            'amount' => 10* 100,
            'currency' => 'usd',
            'payment_method_types' => ['card'],
            
          ]);
    //    $result = $stripe->accounts->create(['type' => 'standard']);
        $acount_no = 'acct_1O0kgfPFknDqsWLN';
                    dd($result);
    }

}
