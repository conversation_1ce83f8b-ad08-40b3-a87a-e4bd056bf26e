<?php

namespace App\Http\Controllers\Frontend;


use App\Http\Controllers\Controller;
use App\Jobs\NewallInfluencersHaveCheckedYourRequest;
use App\Jobs\NewRequestAcceptStatusInfluencer;
use App\Jobs\NewRequestCancelInfluencer;
use App\Jobs\NewRequestInfluencer;
use App\Jobs\NewStartCampaignInfluencer;
use App\Jobs\NewyourCampaignisMovingForward;
use App\Models\AdminComission;
use App\Models\AdminGamification;
use App\Models\AdvertisingMethodNewPrice;
use App\Models\Campaign;
use App\Models\CampaignRequestTime;
use App\Models\Category;
use App\Models\Hashtag;
use App\Models\InfluencerDetail;
use App\Models\InfluencerRequestAccept;
use App\Models\InfluencerRequestDetail;
use App\Models\InfluencerRequestTime;
use App\Models\Invoice;
use App\Models\RequestTask;
use App\Models\SavedCard;
use App\Models\Statistic;
use App\Models\User;
use App\Models\City;
use App\Models\State;
use App\Notifications\allInfluencersHaveCheckedYourRequest;
use App\Notifications\RequestAcceptStatusInfluencer;
use App\Notifications\RequestCancelInfluencer;
use App\Notifications\RequestInfluencer;
use App\Notifications\StartCampaignInfluencer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Stripe\Exception\CardException;
use Stripe\PaymentMethod;
use App\Models\SocialConnect;
use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Transfer;

class StripeMarketplaceController extends Controller
{
    public function __construct()
    {
        Stripe::setApiKey(config('settings.env.STRIPE_SECRET'));
    }

    private function calculateTotalAmountsToCharge($campaignId, $socialNetworkName) {
        $influencerRequestDetails = InfluencerRequestDetail::where(
            'compaign_id', $campaignId
        )
        ->where('status', null)
        ->get();

        if ($influencerRequestDetails->isEmpty()) {
            throw new Exception('Tried to calculate total amounts for a campaign but not influencer request details were found.');
        }

        $totalAmount = 0;
        $totalVAT = 0;
        $platformFee = 0;
        $totalFollower = 0;
        
        foreach ($influencerRequestDetails as $influencerRequestDetail) {
            if (empty($influencerRequestDetail->influencerdetails)) {
                // TODO A temporary fix for badly/incomplete deletion of influencer data from admin panel
                continue;
            }

            if (
                isset($influencerRequestDetail->influencer_request_accepts->request) &&
                $influencerRequestDetail->influencer_request_accepts->request == 1
            ) {
                $socialName = SocialConnect::where(
                    'user_id',
                    $influencerRequestDetail->influencerdetails->user->id,
                )
                ->where('media', $socialNetworkName)
                ->first();

                if (empty($socialName)) {
                    throw new Exception('Something went wrong. Influencer has no social connection.');
                }

                $totalFollower += empty($socialName->followers) ? 0 : $socialName->followers;
                $totalAmount += $influencerRequestDetail->discount_price;
            }

            $newLivetreamPrice = 0;
            $addNewLivetreamPrice = 0;
            
            $user = User::find($influencerRequestDetail->influencerdetails->user->id);
            
            $vatAmount = $user->is_small_business_owner ? 0 : ($influencerRequestDetail->discount_price * 0.19);
            $totalVAT += round($vatAmount, 2);
        }

        // 5% platform fee on totalAmount, minimum 2 eur
        $platformFee = round(($totalAmount * 5) / 100, 2);
        if ($platformFee < 2) {
            $platformFee = 2;
        }

        $totalVAT = round($totalVAT + ($platformFee * 0.19), 2);

        return [
            'totalAmount' => $totalAmount,
            'totalVAT' => $totalVAT,
            'platformFee' => $platformFee
        ];
    }

    public function retrievePaymentIntent(Request $request) {
        $payload = request()->except(['_token']);

        // Retrieve PaymentIntent with expanded charges
        $paymentIntent = \Stripe\PaymentIntent::retrieve([
            'id' => $payload['paymentIntentId'],
            'expand' => ['charges'],
        ]);

        // Get the charge ID
        $chargeId = null;
        if (!empty($paymentIntent->latest_charge)) {
            $chargeId = $paymentIntent->latest_charge;
        }

        if (empty($chargeId)) {
            return response()->json([
                'success' => false,
                'paymentIntentId' => '',
                'error' => 'Failed to retrieve payment intent.'
            ], 400);
        }

        return response()->json([
            'success' => true,
            'chargeId' => $chargeId,
            'error' => ''
        ], 200);
    }

    /**
     * Step 1: Create Payment Intent for Customer
     */
    public function createPaymentIntent(Request $request)
    {
        try {
            $payload = request()->except(['_token']);

            // Validate campaignId
            if (
                empty($payload['campaignId']) ||
                empty($payload['socialNetworkName']) ||
                empty($payload['paymentMethodIdToUse'])
            ) {
                return response()->json(['error' => 'Campaign ID, payment method ID and social network name is required.'], 400);
            }

            if (
                !array_key_exists('totalAmount', $payload) ||
                !array_key_exists('totalVAT', $payload) ||
                !array_key_exists('platformFee', $payload)
            ) {
                return response()->json(['error' => 'Total amount or vat or platform fee values are missing.'], 400);
            }

            $campaignId = $payload['campaignId'];
            $socialNetworkName = $payload['socialNetworkName'];
            $totalAmount = $payload['totalAmount'];
            $totalVAT = $payload['totalVAT'];
            $platformFee = $payload['platformFee'];
            $paymentMethodIdToUse = $payload['paymentMethodIdToUse'];

            // Calculate payment details
            $paymentDetails = $this->calculateTotalAmountsToCharge($campaignId, $socialNetworkName);
            if (!is_array($paymentDetails) ||
                !array_key_exists('totalAmount', $payload) ||
                !array_key_exists('totalVAT', $payload) ||
                !array_key_exists('platformFee', $payload)
            ) {
                return response()->json(['error' => 'Total amount or vat or platform fee values are missing.'], 400);
            }

            if (
                $totalAmount != $paymentDetails['totalAmount'] ||
                $totalVAT != $paymentDetails['totalVAT'] ||
                $platformFee != $paymentDetails['platformFee']
            ) {
                Log::info('There are discrepancy in payment amounts. Payment was cancelled by the platform.', [
                    'provided' => [
                        'totalAmount' => $totalAmount,
                        'totalVAT' => $totalVAT,
                        'platformFee' => $platformFee
                    ],
                    'calculated' => $paymentDetails
                ]);
            }

            // Validate calculated amounts
            if (
                empty($paymentDetails['totalAmount']) ||
                $paymentDetails['totalAmount'] <= 0
            ) {
                return response()->json([
                    'error' => 'Invalid total amount calculated for the campaign.'
                ], 400);
            }

            $orderId = 'campaign_' . $campaignId;
            $totalAmountChargable = $paymentDetails['totalAmount'] + 
                $paymentDetails['totalVAT'] + 
                $paymentDetails['platformFee'];

            $user = $request->user();

            if (!$user || !$user instanceof User) {
                return response()->json(['error' => 'Invalid brand user.'], 400);
            }

            if (!$user->stripe_id) {
                $user->createAsStripeCustomer();
                if (empty($user->stripe_id)) {
                    return response()->json(['error' => 'Failed to create stripe customer for payment.'], 400);
                }
            }

            // Attach payment method id to user for future use
            $paymentMethod = \Stripe\PaymentMethod::retrieve($paymentMethodIdToUse);
            $paymentMethod->attach(['customer' => $user->stripe_id]);

            // Create PaymentIntent
            $paymentIntent = PaymentIntent::create([
                'amount' => $totalAmountChargable * 100, // Convert to cents
                'currency' => 'eur',
                'payment_method_types' => ['card'],
                'transfer_group' => $orderId,
                'customer' => $user->stripe_id,
                'metadata' => [
                    'platform_fee' => $paymentDetails['platformFee'],
                    'vat' => $paymentDetails['totalVAT']
                ],
                'setup_future_usage' => 'off_session',
                // Does not work to enforce 3d secure :(
                // 'payment_method_options' => [
                //     'card' => [
                //         'request_three_d_secure' => 'any',
                //     ]
                // ],
            ]);
    
            // Return client secret for frontend
            return response()->json([
                'paymentIntentId' => $paymentIntent->id,
                'clientSecret' => $paymentIntent->client_secret,
                'orderId' => $orderId
            ]);
        } catch (\Stripe\Exception\ApiErrorException $e) {
            // Handle Stripe API errors
            return response()->json(['error' => 'Stripe API error: ' . $e->getMessage()], 500);
        } catch (\Exception $e) {
            // Handle general exceptions
            return response()->json(['error' => 'An unexpected error occurred: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Process individual charges and transfers for each influencer in a campaign
     *
     * This method implements the separate charges architecture where each influencer
     * gets their own individual charge ID, enabling precise refund control without
     * affecting other influencers or platform fees.
     *
     * @param Request $request Contains payment_intent_id, campaign_id, order_id, etc.
     * @return \Illuminate\Http\JsonResponse
     */
    public function processTransferAndStartCampaign(Request $request)
    {
        try {
            \Stripe\Stripe::setApiKey(config('settings.env.STRIPE_SECRET'));

            $paymentIntentId = $request->input('payment_intent_id');
            $orderId = $request->input('order_id');
            $paymentMethodId = $request->input('payment_method_id');
            $campaignId = $request->input('campaign_id');
            $originalChargeId = $request->input('charge_id'); // Original charge from PaymentIntent
            $user = $request->user();

            Log::info('Starting separate charges architecture for campaign', [
                'campaign_id' => $campaignId,
                'payment_intent_id' => $paymentIntentId,
                'original_charge_id' => $originalChargeId,
                'user_id' => $user->id
            ]);

            /*******************************/

            // make a condition to filter out rejected influncers
            $influencerRequestDetails = InfluencerRequestDetail::where('compaign_id', $campaignId)->get();

            // Calculate the total price and platform fee
            $totalAmount = $influencerRequestDetails->sum('discount_price');
            $platformFee = max($totalAmount * 0.05, 2);

            // Calculate 19% VAT on the platform fee
            $platformVatAmount = $platformFee * 0.19;

            $platformTotal = $platformFee + $platformVatAmount;

            /*******************************/
            /***** Ensure payment method is attached to customer *****/
            // Retrieve and ensure payment method is attached to customer
            try {
                $paymentMethod = \Stripe\PaymentMethod::retrieve($paymentMethodId);
                if ($paymentMethod->customer !== $user->stripe_id) {
                    $paymentMethod->attach(['customer' => $user->stripe_id]);
                }
            } catch (\Exception $e) {
                Log::error('Failed to attach payment method to customer', [
                    'payment_method_id' => $paymentMethodId,
                    'customer_id' => $user->stripe_id,
                    'error' => $e->getMessage()
                ]);
                throw new \Exception('Payment method attachment failed: ' . $e->getMessage());
            }

            /***** Update user address data for invoice *****/
            // Get customer's default payment method
            $customer = \Stripe\Customer::retrieve($user->stripe_id);

            // First update the brand's billing address
            // TODO perhaps does not needed to update it every time, oder?
            // Better to add it in the user update settings, when they
            // change their address
            
            // TODO Note the difference between \Stripe\Customer::update() vs \Strip\Account::update().
            // What is their difference. Will those both work for Influencer (service provider) account?

            $cityName = '';
            $stateName = '';
            if (!empty($user->city) && is_numeric($user->city)) {
                $city = City::find($id = $user->city);
                if (!empty($city->name)) {
                    $cityName = $city->name;
                }

                if (!empty($city->state_id) && is_numeric($city->state_id)) {
                    $state = State::find($city->state_id);
                    if (!empty($state->state)) {
                        $stateName = $state->state;
                    }
                }
            }

            $customerData = [
                'address' => [
                    'line1' => $user->street,
                    // 'city' => $user->city, // it's city id in user
                    // 'state' => $user->state, // it's state id or empty in user
                    'postal_code' => $user->zip_code,
                    'country' => 'DE', // $user->country, // TODO This is numerical code in the database, need to find the two-letter iso code for country
                ]
            ];

            if ($cityName != '') {
                $customerData['address']['city'] = $cityName;
            }

            if ($stateName != '') {
                $customerData['address']['state'] = $stateName;
            }

            if (!empty($user->company_name)) {
                $customerData['name'] = $user->company_name;
            }

            \Stripe\Customer::update(
                $user->stripe_id,
                $customerData
            );
            /*******************************/

            // SINGLE PAYMENT + TRANSFERS ARCHITECTURE
            // Customer has already paid via the original PaymentIntent from frontend
            // We now create transfers to influencers and keep platform fee automatically

            $this->createPlatformFeeInvoice($user, $paymentMethodId, $campaignId, $paymentIntentId, $originalChargeId, $platformFee, $platformVatAmount, $platformTotal);

            Log::info('Using single payment + transfers architecture', [
                'campaign_id' => $campaignId,
                'original_payment_intent_id' => $paymentIntentId,
                'original_charge_id' => $originalChargeId,
                'platform_fee' => $platformFee,
                'platform_vat' => $platformVatAmount,
                'platform_total' => $platformTotal
            ]);

            // Process transfers to each influencer from the original payment
            // Platform fee is kept automatically, transfers are reversible for refunds
            $this->processStripeChargesForInfluencers($campaignId, $paymentMethodId, $paymentIntentId, $originalChargeId, $orderId, $user, $influencerRequestDetails);

            // Finally, start the campaign
            InfluencerRequestDetail::where('compaign_id', $campaignId)
                ->where('status', 'Cancelled')
                ->update(['status' => '2', 'read_status' => 'start_campaign', 'read_at' => NULL]);

            Campaign::where('campaign_id', $campaignId)->update(['has_started' => true]);
        } catch (\Exception $e) {
            Log::error('Error in processTransferAndStartCampaign (Separate Charges Architecture)', [
                'message' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'trace' => $e->getTraceAsString(),
                'paymentIntentId' => $paymentIntentId ?? null,
                'orderId' => $orderId ?? null,
                'campaignId' => $campaignId ?? null,
                'originalChargeId' => $originalChargeId ?? null,
                'user_id' => $user->id ?? null,
                'architecture' => 'separate_charges_v2'
            ]);

            return response()->json([
                'campaignStarted' => false,
                'errorMsg' => 'An unexpected error occurred during payment processing: ' . $e->getMessage()
            ], 500);
        }

        /*******************************/

        return response()->json(['campaignStarted' => true, 'errorMsg' => '']);
    }

    private function processStripeChargesForInfluencers($campaignId, $paymentMethodId, $paymentIntentId, $originalChargeId, $orderId, $user, $influencerRequestDetails) {
        foreach ($influencerRequestDetails as $influencerRequestDetail) {
            if (isset($influencerRequestDetail->influencer_request_accepts)) {
                // FIX: Use the existing $influencerRequestDetail instead of fetching again
                Log::info('Processing individual charge for influencer', [
                    'influencer_request_detail_id' => $influencerRequestDetail->id,
                    'campaign_id' => $campaignId,
                    'influencer_user_id' => $influencerRequestDetail->influencerdetails->user_id ?? null
                ]);

                // Retrieve the influencer user ID
                $influencerUser = User::whereId($influencerRequestDetail->influencerdetails->user_id)->first();

                // discount_price is the amount the influencer will really earn + 20% platform provision
                $originalAmount = $influencerRequestDetail->discount_price;

                // Convert original amount to cents
                $originalAmountCents = (int) round($originalAmount * 100);

                $influencerStripeAccountId = $influencerRequestDetail->influencerdetails->user->stripeAccount->stripe_user_id;

                // Update service provider (Influencer) account settings
                $cityName = '';
                $stateName = '';
                if (!empty($influencerUser->city) && is_numeric($influencerUser->city)) {
                    $city = City::find($id = $influencerUser->city);
                    if (!empty($city->name)) {
                        $cityName = $city->name;
                    }

                    if (!empty($city->state_id) && is_numeric($city->state_id)) {
                        $state = State::find($city->state_id);
                        if (!empty($state->state)) {
                            $stateName = $state->state;
                        }
                    }
                }

                $influencerBusinessProfile = [
                    'business_profile' => [
                        'name' => $influencerUser->first_name . ' ' . $influencerUser->last_name,
                        'support_address' => [
                            'line1' => $influencerUser->street,
                            // 'city' => $influencerUser->city,
                            // 'state' => $influencerUser->state,
                            'postal_code' => $influencerUser->zip_code,
                            'country' => 'DE', // $influencerUser->country, // TODO This is numerical code in the database, need to find the two-letter iso code for country
                        ],
                    ]
                ];

                if ($cityName != '') {
                    $influencerBusinessProfile['business_profile']['support_address']['city'] = $cityName;
                }

                if ($stateName != '') {
                    $influencerBusinessProfile['business_profile']['support_address']['state'] = $stateName;
                }

                if (!empty($influencerUser->company_name)) {
                    $influencerBusinessProfile['business_profile']['name'] = $influencerUser->company_name;
                }

                $influencerStripeAccount = \Stripe\Account::update($influencerStripeAccountId, $influencerBusinessProfile);

                // Default VAT amount in cents (0 if not a small business)
                $influencerVatAmountCents = 0;
                if (!$influencerUser->is_small_business_owner) {
                    // Calculate 19% VAT on the influencer amount
                    $influencerVatAmountCents = (int) round($originalAmountCents * 0.19);

                    InfluencerRequestDetail::where('id', $influencerRequestDetail->id)
                        ->update([
                            'vat_included' => true
                        ]);
                }

                // Total amount including VAT
                $totalAmountCents = $originalAmountCents + $influencerVatAmountCents;

                // Calculate 20% commission (aka provision) on the original
                $commissionRate = 0.20;
                $commissionAmountCents = (int) round($originalAmountCents * $commissionRate);

                // Calculate 19% VAT on the commission amount
                $commissionVatAmountCents = (int) round($commissionAmountCents * 0.19);

                // Total commission amount including VAT
                $totalCommissionCents = $commissionAmountCents + $commissionVatAmountCents;

                // Calculate the influencer's final amount
                $influencerAmountCents = $totalAmountCents - $totalCommissionCents;

                // Convert back to decimals for display
                $originalAmountFormatted = number_format($originalAmountCents / 100, 2);
                $influencerVatFormatted = number_format($influencerVatAmountCents / 100, 2);
                $totalAmountFormatted = number_format($totalAmountCents / 100, 2);
                $commissionFormatted = number_format($totalCommissionCents / 100, 2);
                $influencerAmountFormatted = number_format($influencerAmountCents / 100, 2);

                // Sample calculation
                // current_price = 476, discount_price = 500 (in db)
                // $originalAmountCents = 50000
                // $originalAmountFormatted = 500.00
                // $influencerVatAmountCents = 9500
                // $influencerVatFormatted = 95.00
                // $totalAmountCents = 59500 (This is what customer paid, without our 5% platform fee)
                // $totalAmountFormatted = 595.00
                // $commissionAmountCents = 50000 * 0.2 = 10000
                // $commissionVatAmountCents = 10000 * 0.19 = 1900
                // $totalCommissionCents = 10000 + 1900 = 11900
                // $commissionFormatted = 119.00
                // $influencerAmountCents = 59500 - 11900 = 47600
                // $influencerAmountFormatted = 476.00

                Log::info('Influencer payment calculation', [
                    'originalAmountCents (what seems to be influencer is getting paid)' => $originalAmountCents,
                    'originalAmountFormatted' => $originalAmountFormatted,
                    'influencerVatAmountCents (when influencer is a small business)' => $influencerVatAmountCents,
                    'influencerVatFormatted' => $influencerVatFormatted,
                    'totalAmountCents (originalAmountCents + influencerVatAmountCents)' => $totalAmountCents,
                    'totalAmountFormatted' => $totalAmountFormatted,
                    'commissionAmountCents (5% platform commission)' => $commissionAmountCents,
                    'commissionVatAmountCents (19% vat on platform commission)' => $commissionVatAmountCents,
                    'totalCommissionCents (commissionAmountCents + commissionVatAmountCents)' => $totalCommissionCents,
                    'commissionFormatted' => $commissionFormatted,
                    'influencerAmountCents (totalAmountCents - totalCommissionCents, what influencer actually got after we take 20% provision away)' => $influencerAmountCents,
                    'influencerAmountFormatted' => $influencerAmountFormatted,
                    'campaignId' => $campaignId,
                    'influencerUserId' => $influencerUser->id ?? null,
                    'influencerStripeAccountId' => $influencerStripeAccountId,
                ]);

                // SINGLE PAYMENT + TRANSFERS ARCHITECTURE: Create transfer from original payment
                // Customer paid once, we transfer portions to influencers, platform fee stays automatically

                try {
                    // Create transfer from the original charge to this influencer
                    $transfer = \Stripe\Transfer::create([
                        'amount' => $totalAmountCents,
                        'currency' => 'eur',
                        'destination' => $influencerStripeAccountId,
                        'transfer_group' => $orderId,
                        'source_transaction' => $originalChargeId, // Use original charge from frontend payment
                        'description' => "Transfer to {$influencerUser->first_name} {$influencerUser->last_name} for Campaign {$campaignId}",
                        'metadata' => [
                            'type' => 'influencer_transfer',
                            'influencer_id' => $influencerRequestDetail->id,
                            'influencer_user_id' => $influencerUser->id,
                            'campaign_id' => $campaignId,
                            'original_charge_id' => $originalChargeId,
                            'architecture' => 'single_payment_transfers'
                        ]
                    ]);

                    Log::info('Transfer created successfully from original payment', [
                        'transfer_id' => $transfer->id,
                        'original_charge_id' => $originalChargeId,
                        'transfer_amount_cents' => $totalAmountCents,
                        'destination_account' => $influencerStripeAccountId,
                        'influencer_id' => $influencerRequestDetail->id,
                        'campaign_id' => $campaignId
                    ]);

                } catch (\Stripe\Exception\ApiErrorException $e) {
                    Log::error('Failed to create individual PaymentIntent or transfer', [
                        'error' => $e->getMessage(),
                        'stripe_error_code' => $e->getStripeCode(),
                        'stripe_error_type' => $e->getError()->type ?? null,
                        'influencer_id' => $influencerRequestDetail->id,
                        'campaign_id' => $campaignId,
                        'amount_cents' => $totalAmountCents,
                        'payment_method_id' => $paymentMethodId
                    ]);
                    throw $e; // Re-throw to be caught by outer try-catch
                }

                // Create invoice for record-keeping (no payment needed - already paid via original PaymentIntent)
                $invoice = \Stripe\Invoice::create([
                    'customer' => $user->stripe_id,
                    'auto_advance' => false,
                    'collection_method' => 'send_invoice',
                    'days_until_due' => 30,
                    'description' => "Transfer Receipt for {$influencerUser->first_name} {$influencerUser->last_name} - Campaign {$campaignId}",
                    'on_behalf_of' => $influencerStripeAccountId,
                    'metadata' => [
                        'type' => 'influencer_transfer_receipt',
                        'transfer_id' => $transfer->id,
                        'original_charge_id' => $originalChargeId,
                        'refundable' => 'true'
                    ],
                ]);

                // Add invoice item for documentation
                \Stripe\InvoiceItem::create([
                    'customer' => $user->stripe_id,
                    'amount' => $influencerVatAmountCents > 0 ? $originalAmountCents : $totalAmountCents,
                    'currency' => 'eur',
                    'invoice' => $invoice->id,
                    'description' => 'Influencer Service for Campaign: ' . $campaignId,
                ]);

                // If VAT applies, add it as a separate invoice item
                if ($influencerVatAmountCents > 0) {
                    \Stripe\InvoiceItem::create([
                        'customer' => $user->stripe_id,
                        'amount' => $influencerVatAmountCents,
                        'currency' => 'eur',
                        'invoice' => $invoice->id,
                        'description' => '19% VAT on Influencer Service for Campaign: ' . $campaignId,
                    ]);
                }

                // Finalize invoice for record-keeping (mark as paid out-of-band since original payment covers this)
                $invoiceFinalized = $invoice->finalizeInvoice();
                $invoicePaid = $invoiceFinalized->pay([
                    'paid_out_of_band' => true, // Correct usage: payment was made via original PaymentIntent
                ]);
                $pdfUrl = $invoicePaid->invoice_pdf;

                // Store payment details with transfer information
                $paymentDetails = [
                    'intent_id' => $paymentIntentId, // Original PaymentIntent ID (customer paid once)
                    'original_intent_id' => $paymentIntentId, // Same as above
                    'charge_id' => $originalChargeId, // Original charge ID for refunds
                    'transfer_id' => $transfer->id, // Transfer ID for this influencer
                    'account_id' => $influencerStripeAccountId,
                    'amount' => 0, // Legacy field
                    'total_amount' => $totalAmountFormatted,
                    'influencer_amount' => $totalAmountFormatted,
                    'platform_amount' => $commissionFormatted,
                    'cash_out_amount' => $influencerAmountFormatted,
                    'receipt_url' => $pdfUrl,
                    'stripe_invoice_id' => $invoicePaid->id,
                    'architecture' => 'single_payment_transfers'
                ];

                // Create local invoice record with transfer information
                $influencerInvoice = \App\Models\Invoice::create([
                    'user_id' => Auth::id(),
                    'card_token' => $paymentIntentId, // Original PaymentIntent ID
                    'charge_id' => $originalChargeId, // Original charge ID for refund tracking
                    'campaign_id' => $campaignId,
                    'influencer_request_detail_id' => $influencerRequestDetail->id,
                    'influencer_id' => $influencerRequestDetail->influencerdetails->user_id,
                    'payment_type' => 'Influencer_Transfer',
                    'payment_amount' => $totalAmountFormatted,
                    'payment_details' => json_encode($paymentDetails),
                    'receipt' => $pdfUrl,
                    'payment_status' => 'paid',
                    'paid_amount' => $totalAmountFormatted,
                    'description' => "Transfer for {$influencerUser->first_name} {$influencerUser->last_name} - Campaign {$campaignId} (from original payment)",
                ]);

                // Store transfer information for refund control
                InfluencerRequestDetail::where('id', $influencerRequestDetail->id)
                    ->update([
                        'invoice_id' => $influencerInvoice->id,
                        'refund_txn_id' => $transfer->id, // Store TRANSFER ID for individual refunds (not original charge)
                        'platform_amount' => $commissionFormatted,
                        'influencer_amount' => $totalAmountFormatted,
                        'cash_out_amount' => $influencerAmountFormatted,
                        'payment_status' => InfluencerRequestDetail::STATUS_NEW,
                    ]);

                Log::info('InfluencerRequestDetail updated with transfer information', [
                    'influencer_request_detail_id' => $influencerRequestDetail->id,
                    'original_charge_id' => $originalChargeId,
                    'transfer_id' => $transfer->id,
                    'campaign_id' => $campaignId,
                    'refund_enabled' => true
                ]);

                dispatch(new NewStartCampaignInfluencer($influencerUser, $influencerRequestDetail));
                $influencerUser->notify(new StartCampaignInfluencer($influencerUser, $influencerRequestDetail));
            }
        }
    }

    public function saveCard(Request $request) {
        // formData has the following fields when entering a new card:
        // {name: 'payment_method', value: 'pm_1R0EX2Fv5DX420hyxpB43Y97' )
        // {name: 'name_on_card', value: 'John Doe'}
        // {name: 'card_brand', value: "visa"}
        // {name: 'last4', value: '4242'}
        // {name: 'expiry_month', value: '12'}
        // {name: 'expiry_year', value: '26'}
        $formData = request()->except(['_token']);
        
        $cardDetails = $formData;
        $cardDetails['user_id'] = Auth::id();
        $cardDetails['type'] = $formData['card_brand'];
        $cardDetails['cvv'] = '';
        $cardDetails['card_number'] = $formData['last4'];
        $card = SavedCard::create($cardDetails);
    }

    /**
     * Step 2: Webhook - On Payment Success, Transfer Funds
     * TODO Webhook still needs to be setup, needs a webhook
     * secret key from the stripe dashboard.
     */
    public function handleWebhook(Request $request)
    {
        $payload = $request->getContent();
        $sigHeader = $request->header('Stripe-Signature');
        $webhookSecret = env('STRIPE_WEBHOOK_SECRET');

        try {
            $event = \Stripe\Webhook::constructEvent(
                $payload, $sigHeader, $webhookSecret
            );
        } catch (\UnexpectedValueException $e) {
            return response('Invalid payload', 400);
        } catch (\Stripe\Exception\SignatureVerificationException $e) {
            return response('Invalid signature', 400);
        }

        if ($event->type === 'payment_intent.succeeded') {
            $paymentIntent = $event->data->object;

            // Lookup the order and connected accounts from DB (simplified here)
            $orderId = $paymentIntent->transfer_group;

            // Normally, fetch this from DB:
            $transfers = [
                ['amount' => 12000, 'destination' => 'acct_provider1'],
                ['amount' => 7000,  'destination' => 'acct_provider2'],
            ];

            foreach ($transfers as $transfer) {
                Transfer::create([
                    'amount' => $transfer['amount'],
                    'currency' => 'usd',
                    'destination' => $transfer['destination'],
                    'transfer_group' => $orderId,
                ]);
            }

            Log::info("Transfers completed for {$orderId}");
        }

        return response('Webhook handled', 200);
    }

    private function createPlatformFeeInvoice($user, $paymentMethodId, $campaignId, $paymentIntentId, $originalChargeId, $platformFee, $platformVatAmount, $platformTotal) {
        $platformFeeInvoice = \Stripe\Invoice::create([
            'customer' => $user->stripe_id,
            'auto_advance' => false,
            'collection_method' => 'send_invoice',
            'days_until_due' => 30,
            'default_payment_method' => $paymentMethodId, // Set the payment method for the invoice
            'description' => "Platform Fee for Campaign {$campaignId} - ClickItFame Service Fee",
            'statement_descriptor' => "CIF FEE {$campaignId}", // Shows in dashboard and bank statements (max 22 chars)
            'metadata' => [
                'type' => 'platform_fee',
                'campaign_id' => $campaignId,
                'refundable' => 'false'
            ],
            'custom_fields' => [
                [
                    'name' => 'Campaign ID',
                    'value' => $campaignId
                ]
            ]
            // TODO does it work? Test later..
            // 'issuer' => [ // from https://docs.stripe.com/api/invoices/create
            //     'account' => 'platformAccountId'
            // ]
        ]);

        // Create and add platform fee invoice item
        \Stripe\InvoiceItem::create([
            'customer' => $user->stripe_id,
            'amount' => intval($platformFee * 100),
            'currency' => 'eur',
            'invoice' => $platformFeeInvoice->id,
            'description' => 'Platform fee (non-refundable) for Campaign: ' . $campaignId,
        ]);

        // Create and add the VAT invoice item
        \Stripe\InvoiceItem::create([
            'customer' => $user->stripe_id,
            'amount' => intval($platformVatAmount * 100),
            'currency' => 'eur',
            'invoice' => $platformFeeInvoice->id,
            'description' => 'VAT (19%) on Platform Fee',
        ]);

        // Now finalize for documentation purpose (no payment)
        $platformFeeInvoiceFinalized = $platformFeeInvoice->finalizeInvoice();
        // We are creating this invoice only for documentation purpose. So
        // we are not calling this ->pay() method.
        // TO THE FUTURE DEVELOPER:
        // DO NOT ACTIVATE IT. THEN IT WILL CAUSE DOUBLE PAYMENT
        // $platformFeeInvoicePaid = $platformFeeInvoiceFinalized->pay();
        $platformFeeInvoicePaid = $platformFeeInvoiceFinalized->pay([
            'paid_out_of_band' => true
        ]);
        $pdfUrl = $platformFeeInvoicePaid->invoice_pdf; // Get PDF from finalized invoice

        // Store platform fee invoice in local database
        // Platform fee uses original charge ID and remains separate from individual influencer charges
        \App\Models\Invoice::create([
            'user_id' => Auth::id(),
            'card_token' => $paymentIntentId,
            'charge_id' => $originalChargeId, // Platform fee linked to original charge
            'campaign_id' => $campaignId,
            'influencer_request_detail_id' => null,
            'influencer_id' => null,
            'payment_type' => 'Platform_Fee',
            'payment_amount' => $platformFee,
            'receipt' => $pdfUrl,
            'vat_included' => true,
            'payment_status' => 'paid',
            'paid_amount' => $platformTotal, // No separate payment made
            'description' => 'Platform fee for Campaign: ' . $campaignId . ' (Covered by original payment, Non-refundable)'
        ]);

        Log::info('Platform fee invoice created', [
            'campaign_id' => $campaignId,
            'platform_fee' => $platformFee,
            'platform_vat' => $platformVatAmount,
            'platform_total' => $platformTotal,
            'original_charge_id' => $originalChargeId
        ]);
    }
}
