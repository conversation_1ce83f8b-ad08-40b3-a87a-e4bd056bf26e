<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Jobs\NewallInfluencersHaveCheckedYourRequest;
use App\Jobs\NewRequestAcceptStatusInfluencer;
use App\Jobs\NewRequestCancelInfluencer;
use App\Jobs\NewRequestInfluencer;
use App\Jobs\NewStartCampaignInfluencer;
use App\Jobs\NewyourCampaignisMovingForward;
use App\Models\AdminComission;
use App\Models\AdminGamification;
use App\Models\Campaign;
use App\Constants\CampaignType;
use App\Models\CampaignRequestTime;
use App\Models\Category;
use App\Models\Hashtag;
use App\Models\InfluencerDetail;
use App\Models\InfluencerPrice;
use App\Models\InfluencerRequestAccept;
use App\Models\InfluencerRequestDetail;
use App\Models\InfluencerRequestTime;
use App\Models\Invoice;
use App\Models\RequestTask;
use App\Models\SavedCard;
use App\Models\Statistic;
use App\Models\User;
use App\Models\City;
use App\Models\State;
use App\Notifications\allInfluencersHaveCheckedYourRequest;
use App\Notifications\RequestAcceptStatusInfluencer;
use App\Notifications\RequestCancelInfluencer;
use App\Notifications\RequestInfluencer;
use App\Notifications\StartCampaignInfluencer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Stripe\Exception\CardException;
use Stripe\PaymentMethod;

class OpenCampaignsController extends Controller
{
    public function buildOpenRequestsPage() {
        $influencerCampaignDetails = InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
            ->leftjoin('campaigns', 'campaigns.campaign_id', '=', 'influencer_request_details.compaign_id')
            ->select('influencer_request_details.*', 'campaigns.has_started', 'influencer_details.id as i_id', 'influencer_details.user_id as i_user_id', 'influencer_request_accepts.request', 'influencer_request_accepts.request_time', 'influencer_request_accepts.request_time_accept', 'influencer_request_accepts.id as influencer_request_accept_id')
            ->where('influencer_details.user_id', Auth::id())
            ->where('campaigns.has_started', false)
            ->orderBy('influencer_request_details.id', 'desc');

        $influencerCampaignDetails = $influencerCampaignDetails->get();

        foreach ($influencerCampaignDetails as $influencerCampaignDetail) {
            $requested_time = InfluencerRequestTime::where(
                'influencer_request_accept_id',
                $influencerCampaignDetail->influencer_request_accept_id
            )
            ->latest()
            ->first();

            $influencerCampaignDetail->requested_time = $requested_time;
            $influencerCampaignDetail->tasks  = RequestTask::where('influencer_request_detail_id', $influencerCampaignDetail->compaign_id)->get();
        }

        $campaignRequestTime = CampaignRequestTime::first();

        return view('front-user.pages.influencer.open-requests', compact('influencerCampaignDetails', 'campaignRequestTime'));
    }

    public function buildOpenCampaignsPage() {
        $influencerCampaignDetails =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
            ->leftjoin('campaigns', 'campaigns.campaign_id', '=', 'influencer_request_details.compaign_id')
            ->select('influencer_request_details.*', 'campaigns.has_started', 'influencer_details.id as i_id', 'influencer_details.user_id as i_user_id', 'influencer_request_accepts.request', 'influencer_request_accepts.id as influencer_request_accept_id', DB::raw('COUNT(influencer_request_accepts.id) AS total_count'), DB::raw('SUM(influencer_request_accepts.request) AS accept_request'), DB::raw('COUNT(influencer_request_details.id) AS total_influencer_count'))
            ->where('influencer_request_details.user_id', Auth::id())
            ->where('influencer_request_details.status', NULL)
            ->whereNull('influencer_request_details.refund_reason') // ✅ Exclude cancelled campaigns
            ->where('campaigns.has_started', false)
            ->groupBy('influencer_request_details.compaign_id')
            ->orderBy('influencer_request_details.id', 'desc');

        $influencerCampaignDetails = $influencerCampaignDetails->get();

        foreach ($influencerCampaignDetails as $influencerCampaignDetail) {
            $influencerDetails =  InfluencerDetail::leftjoin('influencer_request_details', function ($join) use ($influencerCampaignDetail) {
                $join->on('influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')->where('influencer_request_details.compaign_id', $influencerCampaignDetail->compaign_id);
            })
            ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
            ->leftjoin('social_connects',  'social_connects.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('advertising_method_new_prices',  'advertising_method_new_prices.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('hashtags',  'hashtags.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->select('influencer_details.id as i_id', 'influencer_details.user_id as i_user_id', 'influencer_details.*', 'social_connects.media', 'social_connects.url', 'social_connects.picture', 'social_connects.name as username', 'social_connects.followers', 'advertising_method_new_prices.*', 'hashtags.tags', 'influencer_request_details.advertising', 'influencer_request_details.id as request_status', 'influencer_request_accepts.id as accept_id', 'influencer_request_accepts.request as accept_request')
            ->where('social_connects.media', $influencerCampaignDetail->media)
            ->where('advertising_method_new_prices.type', $influencerCampaignDetail->post_type)
            ->where('advertising_method_new_prices.media', $influencerCampaignDetail->media);

            if (isset($influencerCampaignDetail->media)) {
                $influencerDetails->where('advertising_method_new_prices.media', $influencerCampaignDetail->media);
            }

            if (isset($influencerCampaignDetail->category_id)) {
                $condition = " 1 = 1 ";
                $subject_con_arr = [];
                foreach ($influencerCampaignDetail->category_id as $cat) {
                    if ($cat != '') {
                        $subject_con_arr[] = " FIND_IN_SET('" . $cat . "', influencer_details.category_id) ";
                    }
                }

                if (count($subject_con_arr) > 0) {
                    $condition .= ' and (' . implode(' or ', $subject_con_arr) . ')';
                }

                $influencerDetails->whereRaw($condition);
            }

            $influencerDetails = $influencerDetails->where('influencer_details.publish', 'Publish')->where('users.status', 1);
            $influencerDetails = $influencerDetails->groupBy('influencer_details.id')->get();

            foreach ($influencerDetails as $influencerDetail) {
                $hashtags = Hashtag::where('user_id', $influencerDetail->i_user_id)->get('tags');
                $influencerDetail->tags =  $hashtags;

                $category = Category::where('id', $influencerDetail->category_id)->get('name');
                $influencerDetail->category =   $category->implode('name', ',');
            }

            $influencerCampaignDetail->influencerslist  = $influencerDetails;
            $influencerCampaignDetail->tasks  = RequestTask::where('influencer_request_detail_id', $influencerCampaignDetail->compaign_id)->get();

            $influencerCampaignDetail->campaign_details = Campaign::where('campaign_id', $influencerCampaignDetail->compaign_id)->first();
        }

        $AdminComission = AdminComission::first();
        $campaignRequestTime = CampaignRequestTime::first();

        return view('front-user.pages.brand.open-campaigns', compact('influencerCampaignDetails', 'AdminComission', 'campaignRequestTime'));
    }

    public function updateInfluencer(Request $request)
    {
        $formData = request()->except(['_token', 'Update']);
        $campaignId = $request->compaign_id;

        $existingInfluencerRequests = InfluencerRequestDetail::where('compaign_id', $campaignId)->get();

        // Remove influencer requests that are not in the selected list
        foreach ($existingInfluencerRequests as $influencerRequest) {
            if ($request->selectInfluencer != '') {
                if (!in_array($influencerRequest->influencer_detail_id, $request->selectInfluencer)) {
                    if (is_array($request->selectInfluencerJoin)) {
                        foreach ($request->selectInfluencerJoin as $joinedInfluencerId) {
                            if ($influencerRequest->influencer_detail_id != $joinedInfluencerId) {
                                InfluencerRequestDetail::where('compaign_id', $campaignId)
                                    ->where('influencer_detail_id', $influencerRequest->influencer_detail_id)
                                    ->delete();
                            }
                        }
                    } else {
                        InfluencerRequestDetail::where('compaign_id', $campaignId)
                            ->where('influencer_detail_id', $influencerRequest->influencer_detail_id)
                            ->delete();
                    }
                }
            } else {
                InfluencerRequestDetail::where('compaign_id', $campaignId)
                    ->where('influencer_detail_id', $influencerRequest->influencer_detail_id)
                    ->delete();
            }
        }

        $remainingInfluencerIds = InfluencerRequestDetail::where('compaign_id', $campaignId)
            ->pluck('influencer_detail_id')
            ->toArray();

        $firstRequest = $existingInfluencerRequests[0];
        $currentCampaignTotal = $firstRequest->total_amount;

        // Calculate total amount for remaining influencers using new InfluencerPrice model
        $remainingInfluencerRequests = InfluencerRequestDetail::where('compaign_id', $campaignId)->get();
        $totalInfluencerAmount = 0;

        foreach ($remainingInfluencerRequests as $influencerRequest) {
            $influencerDetail = InfluencerDetail::where('id', $influencerRequest->influencer_detail_id)->first();
            $user = User::where('id', $influencerDetail->user_id)->first();

            // Get the influencer record for pricing
            $influencer = $user->influencer;
            if (!$influencer) {
                \Log::error('Influencer record not found for user', [
                    'user_id' => $user->id,
                    'influencer_detail_id' => $influencerDetail->id,
                    'campaign_id' => $campaignId
                ]);
                throw new \Exception("Influencer record not found for user ID: {$user->id}");
            }

            // Map legacy advertising types to proper campaign types
            $campaignType = match ($influencerRequest->advertising) {
                CampaignType::BOOST_ME, 'Boost me', 'boost me', 'Story', 'Story - Picture', 'Story - Video' => CampaignType::BOOST_ME,
                CampaignType::REACTION_VIDEO, 'Reaction video', 'reaction video', 'Reel' => CampaignType::REACTION_VIDEO,
                CampaignType::SURVEY, 'survey', 'Post - Picture' => CampaignType::SURVEY,
                default => throw new \Exception("Unknown advertising type: {$influencerRequest->advertising}")
            };

            // Get pricing from new InfluencerPrice model
            $influencerPrice = InfluencerPrice::where('influencer_id', $influencer->id)
                ->where('campaign_type', $campaignType)
                ->first();

            if (!$influencerPrice) {
                \Log::error('InfluencerPrice record not found', [
                    'influencer_id' => $influencer->id,
                    'campaign_type' => $campaignType,
                    'user_id' => $user->id,
                    'campaign_id' => $campaignId
                ]);
                throw new \Exception("InfluencerPrice not found for influencer ID: {$influencer->id}, campaign type: {$campaignType}");
            }

            $totalInfluencerAmount += $influencerPrice->price;

            // Add VAT if user is not a small business owner
            if ($user->is_small_business_owner == 0) {
                $totalInfluencerAmount += ($influencerPrice->price * 0.19);
            }
        }

        // Calculate platform commission (5% minimum €2) with VAT
        $platformCommission = ($totalInfluencerAmount * 5) / 100;
        if ($platformCommission < 2) {
            $platformCommission = 2;
        }
        $platformCommissionWithVat = $platformCommission + ($platformCommission * 0.19);
        $newCampaignTotal = $totalInfluencerAmount + $platformCommissionWithVat;

        // Update total amount for all influencer requests in this campaign
        InfluencerRequestDetail::where('compaign_id', $campaignId)
            ->update(['total_amount' => $newCampaignTotal]);

        // Base amount for calculating new influencer additions
        $baseInfluencerAmount = $newCampaignTotal - $platformCommissionWithVat;
        // Process newly selected influencers
        if ($request->selectInfluencer != '') {
            if ($existingInfluencerRequests->count() >= 50) {
                return redirect()->back()->with('error', 'Maximum number of influencer limit reached.');
            }

            $selectedInfluencerIds = $request->selectInfluencer;
            $updatedTotalPrice = 0;

            foreach ($selectedInfluencerIds as $influencerDetailId) {
                $influencerDetail = InfluencerDetail::where('id', $influencerDetailId)->first();
                $user = User::whereId($influencerDetail->user_id)->first();

                $existingInfluencerRequest = InfluencerRequestDetail::where('compaign_id', $campaignId)
                    ->where('influencer_detail_id', $influencerDetailId)
                    ->first();

                if (!$existingInfluencerRequest) {
                    $existingInfluencerRequest = $firstRequest;
                }

                // Only process influencers that are not already in the campaign
                if (!in_array($influencerDetailId, $remainingInfluencerIds)) {
                    if ($influencerDetailId != '') {
                        // Get the influencer record for pricing
                        $influencer = $user->influencer;
                        if (!$influencer) {
                            \Log::error('Influencer record not found for user when adding new influencer', [
                                'user_id' => $user->id,
                                'influencer_detail_id' => $influencerDetail->id,
                                'campaign_id' => $campaignId
                            ]);
                            throw new \Exception("Influencer record not found for user ID: {$user->id}");
                        }

                        // Map legacy advertising types to proper campaign types
                        $campaignType = match ($existingInfluencerRequest->advertising) {
                            CampaignType::BOOST_ME, 'Boost me', 'boost me', 'Story', 'Story - Picture', 'Story - Video' => CampaignType::BOOST_ME,
                            CampaignType::REACTION_VIDEO, 'Reaction video', 'reaction video', 'Reel' => CampaignType::REACTION_VIDEO,
                            CampaignType::SURVEY, 'survey', 'Post - Picture' => CampaignType::SURVEY,
                            default => throw new \Exception("Unknown advertising type: {$existingInfluencerRequest->advertising}")
                        };

                        // Get pricing from new InfluencerPrice model
                        $newInfluencerPrice = InfluencerPrice::where('influencer_id', $influencer->id)
                            ->where('campaign_type', $campaignType)
                            ->first();

                        if (!$newInfluencerPrice) {
                            \Log::error('InfluencerPrice record not found when adding new influencer', [
                                'influencer_id' => $influencer->id,
                                'campaign_type' => $campaignType,
                                'user_id' => $user->id,
                                'campaign_id' => $campaignId
                            ]);
                            throw new \Exception("InfluencerPrice not found for influencer ID: {$influencer->id}, campaign type: {$campaignType}");
                        }

                        $updatedTotalPrice = $baseInfluencerAmount + $newInfluencerPrice->price;

                        // Add VAT if user is not a small business owner
                        if ($user->is_small_business_owner == 0) {
                            $updatedTotalPrice += ($newInfluencerPrice->price * 0.19);
                        }

                        // Recalculate platform commission with new total
                        $newPlatformCommission = ($updatedTotalPrice * 5) / 100;
                        if ($newPlatformCommission < 2) {
                            $newPlatformCommission = 2;
                        }
                        $newPlatformCommissionWithVat = $newPlatformCommission + ($newPlatformCommission * 0.19);
                        $finalCampaignTotal = $updatedTotalPrice + $newPlatformCommissionWithVat;

                        // Create new influencer request detail
                        InfluencerRequestDetail::create([
                            'user_id' => $influencerDetail->user_id,
                            'influencer_detail_id' => $influencerDetail->id,
                            'advertising' => $existingInfluencerRequest->advertising,
                            'media' => $existingInfluencerRequest->media,
                            'name' => $existingInfluencerRequest->name,
                            'hashtags' => $existingInfluencerRequest->hashtags,
                            'mentions' => $existingInfluencerRequest->mentions,
                            'social' => $existingInfluencerRequest->social,
                            'site' => $existingInfluencerRequest->site,
                            'time' => '7',
                            'total_amount' => $finalCampaignTotal,
                            'compaign_id' => $existingInfluencerRequest->compaign_id,
                            'compaign_title' => $existingInfluencerRequest->compaign_title,
                            'influencer_price' => $newInfluencerPrice->price, // Use new pricing system
                            'file' => $existingInfluencerRequest->file,
                            'task' => $existingInfluencerRequest->task,
                            'post_type' => $existingInfluencerRequest->post_type,
                            'post_content_type' => $existingInfluencerRequest->post_content_type
                        ]);

                        \Log::info('New influencer added to campaign', [
                            'campaign_id' => $campaignId,
                            'final_campaign_total' => $finalCampaignTotal,
                            'influencer_price' => $newInfluencerPrice->price
                        ]);

                        $customer = User::whereId($existingInfluencerRequest->user_id)->first();
                        $formData['compaign_title'] = $existingInfluencerRequest->compaign_title;
                        dispatch(new NewRequestInfluencer($user, $customer, $formData));
                        $user->notify(new RequestInfluencer($user, $customer, $formData));
                    }
                }
            }
        }

        // Calculate final campaign totals for all active influencers
        $campaignInfluencers = InfluencerRequestDetail::where('compaign_id', $campaignId)
            ->where('status', null)
            ->get();

        $totalVatAmount = 0;
        $totalInfluencerPrices = 0;

        foreach ($campaignInfluencers as $campaignInfluencer) {
            // Only include accepted or pending requests
            if (
                (isset($campaignInfluencer->influencer_request_accepts->request) &&
                    $campaignInfluencer->influencer_request_accepts->request == 1) ||
                !isset($campaignInfluencer->influencer_request_accepts->request)
            ) {
                if (empty($campaignInfluencer->influencer_price)) {
                    throw new \Exception('Influencer price is missing for campaign influencer.');
                }

                $currentInfluencerPrice = $campaignInfluencer->influencer_price;
                $totalInfluencerPrices += $currentInfluencerPrice;

                $influencerUser = User::find($campaignInfluencer->influencerdetails->user->id);

                // Calculate VAT for non-small business owners
                $vatAmount = $influencerUser->is_small_business_owner
                    ? 0
                    : $currentInfluencerPrice * 0.19;

                $totalVatAmount += $vatAmount;
            }
        }

        // Calculate platform fee (5% minimum €2) and add VAT on platform fee
        $platformFee = ($totalInfluencerPrices * 5) / 100;
        if ($platformFee < 2) {
            $platformFee = 2;
        }
        $totalVatAmount += ($platformFee * 0.19);

        $finalCampaignAmount = $totalInfluencerPrices + $platformFee + $totalVatAmount;

        // Update the campaign total amount
        Campaign::where('campaign_id', $campaignId)->update(['total_amount' => $finalCampaignAmount]);

        return back()->with('success', 'Request updated successfully.');
    }



    public function requestForm(Request $request)
    {

        $formData = request()->except(['_token']);
        $influencer_request_accept = InfluencerRequestAccept::where('influencer_request_detail_id', $formData['influencer_request_detail_id'])->first();
        if (!isset($influencer_request_accept)) {

            $request = InfluencerRequestAccept::create(
                [
                    'user_id' => Auth::id(),
                    'influencer_request_detail_id' => $formData['influencer_request_detail_id'],
                    'status' => 1
                ]
            );
        }
        $influencer_request_accept = InfluencerRequestAccept::where('influencer_request_detail_id', $formData['influencer_request_detail_id'])->first();
        $influencerDetail = InfluencerRequestDetail::where('id', $influencer_request_accept->influencer_request_detail_id)->first();
        $status = '';
        if (isset($formData['accept'])) {
            $status = 'Accepted';
            $influencer_request_accept->update(['request' => 1]);
        } else {
            $status = 'Rejected';
            $influencer_request_accept->update(['request' => 0]);
            //  $influencerDetail->update(['review' => 0, 'finish' => 0, 'refund_reason' => $status]);
            $influencerDetail->update(['review' => 0, 'refund_reason' => $status]);
        }
        $customer = User::whereId($influencerDetail->user_id)->first();




        $results = AdminGamification::where('select_type', 'Point-Rules')->first();
        // <!-- quick_response -->
        $created_date = $influencerDetail->created_at;
        $influencer_request_accepts = InfluencerRequestAccept::where('influencer_request_detail_id', $influencerDetail->id)->where('user_id', Auth::id())->first();
        if ($influencer_request_accepts != '') {
            $created_at = strtotime($influencerDetail->created_at);
            $updated_at = strtotime($influencer_request_accepts->created_at);
            $datediff = $updated_at - $created_at;
            $days =  round($datediff / (60 * 60 * 24));
            if (isset($influencer_request_accepts) && $days <= 1     && $influencerDetail->refund_reason == null) {
                // $points =$points + $results->quick_response ;
                Statistic::create([
                    'user_id' => Auth::id(),
                    'points' => $results->quick_response,
                    'type' => '1',
                    'title' =>    '[' . $influencerDetail->compaign_id . ']</br>' . $results->quick_response . ' points gained for responding to a request very fast',
                    'date' => date('Y-m-d H:i:s'),
                ]);
            }
            // <!-- points_deadlines -->
            if (isset($influencer_request_accepts) && $days > 2   && $influencerDetail->refund_reason == null) {
                // $points =$points - $results->points_deadlines ;
                Statistic::create([
                    'user_id' => Auth::id(),
                    'points' => $results->points_deadlines,
                    'type' => '0',
                    'title' => '[' . $influencerDetail->compaign_id . ']</br>' . $results->points_deadlines . ' points lost for not submitting on time',
                    'date' => date('Y-m-d H:i:s'),
                ]);
            }
        }




        $influencerDetailAll = InfluencerRequestDetail::where('compaign_id', $influencerDetail->compaign_id)->get();
        $count = 0;
        foreach ($influencerDetailAll as $all) {
            $influencerAcceptAll = InfluencerRequestAccept::where('influencer_request_detail_id', $all->id)->where('request', '1')->first();
            if (isset($influencerAcceptAll)) {
                $count++;
            }
        }

        if ($influencerDetailAll->count() == $count) {

            dispatch(new NewallInfluencersHaveCheckedYourRequest($customer, $influencer_request_accept, $influencerDetail));
            $customer->notify(new allInfluencersHaveCheckedYourRequest($customer, $influencer_request_accept, $influencerDetail));
        // } else if ($influencerDetailAll->count() / 2 <= $count) {
        //     $influencer = User::whereId($influencerDetail->influencerdetails->user_id)->first();
        //     dispatch(new NewyourCampaignisMovingForward($customer, $influencerDetail, $influencer));
        } else if ($count == 1) {

            dispatch(new NewRequestAcceptStatusInfluencer($customer, $influencer_request_accept, $influencerDetail, $status));
            $customer->notify(new RequestAcceptStatusInfluencer($customer, $influencer_request_accept, $influencerDetail, $status));
        }

        $CampaignInfluencers =  InfluencerRequestDetail::where('compaign_id', $influencerDetail->compaign_id)->where('status', null)->get();
        $total_VAT = 0;
        $total_amount = 0;
        foreach ($CampaignInfluencers as $CampaignInfluencer) {
            if (
                (isset($CampaignInfluencer->influencer_request_accepts->request) &&
                    $CampaignInfluencer->influencer_request_accepts->request == 1) ||
                !isset($CampaignInfluencer->influencer_request_accepts->request)
            ) {
                if (empty($CampaignInfluencer->influencer_price)) {
                    throw new Exception('Influencer price is missing.');
                }

                $influencerPrice = $CampaignInfluencer->influencer_price;
                $total_amount = $total_amount + $influencerPrice;

                $user = User::find(
                    $CampaignInfluencer->influencerdetails->user->id,
                );

                $VAT_value = $user->is_small_business_owner
                    ? 0
                    : $influencerPrice * 0.19;

                $total_VAT += $VAT_value;
            }
        }
        $platform_fee = ($total_amount * 5) / 100;
        if ($platform_fee < 2) {
            $platform_fee = 2;
        }
        $total_VAT = $total_VAT + $platform_fee * 0.19;

        $total_campaign_amount = $total_amount + $platform_fee + $total_VAT;
        Campaign::where('campaign_id', $influencerDetail->compaign_id)->update(['total_amount' => $total_campaign_amount]);

        return back()->with('success', ' Request ' . $status . ' Successfully.');
    }

    public function campaignCancel($campaign_id)
    {
        $influencerRequestDetails = InfluencerRequestDetail::whereCompaignId($campaign_id)->get();
        $successCount = 0;
        $errorCount = 0;

        foreach ($influencerRequestDetails as $influencerRequestDetail) {
            // Use the new centralized cancelCampaign method
            $result = $influencerRequestDetail->cancelCampaign('cancelled_by_customer', [
                'process_refund' => true,
                'adjust_price' => true,
                'remove_pause' => true,
                'send_notifications' => false, // We'll send custom notifications below
                'metadata' => [
                    'cancelled_by' => 'customer',
                    'controller' => 'OpenCampaignsController',
                    'method' => 'campaignCancel',
                    'campaign_id' => $campaign_id
                ]
            ]);

            if ($result['success']) {
                $successCount++;

                // Send custom notifications for campaign cancellation
                $InfluencerDetail = InfluencerDetail::where('id', $influencerRequestDetail->influencer_detail_id)->first();
                $influencer = User::whereId($InfluencerDetail->user_id)->first();

                dispatch(new NewRequestCancelInfluencer($influencer, Auth::user(), $influencerRequestDetail));
                $influencer->notify(new RequestCancelInfluencer($influencer, Auth::user(), $influencerRequestDetail));

                \Log::info('Campaign cancelled successfully via cancelCampaign', [
                    'campaign_id' => $campaign_id,
                    'influencer_request_detail_id' => $influencerRequestDetail->id,
                    'refund_processed' => $result['refund_processed'] ?? null,
                    'refund_id' => $result['refund_id'] ?? null,
                    'note' => !empty($result['refund_processed']) ?
                        'Refund processed successfully' :
                        'Refund status unknown, probably cancelled by brand before the payment phase',
                    'result' => $result,
                ]);
            } else {
                $errorCount++;

                \Log::error('Campaign cancellation failed via cancelCampaign', [
                    'result' => $result,
                    'campaign_id' => $campaign_id,
                    'influencer_request_detail_id' => $influencerRequestDetail->id,
                    'error_code' => $result['error_code'] ?? null,
                    'error_message' => $result['message'] ?? null
                ]);
            }
        }

        if ($errorCount > 0) {
            return response()->json([
                'status' => 'Partial success',
                'message' => "Campaign cancellation completed with {$successCount} successes and {$errorCount} errors. Check logs for details."
            ], 207); // 207 Multi-Status
        }

        return response()->json(['status' => 'Request cancelled successfully']);
    }
}
