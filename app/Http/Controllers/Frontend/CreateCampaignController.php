<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Jobs\NewRequestInfluencer;
use App\Models\AdminComission;
use App\Models\AdminGamification;
use App\Models\Campaign;
use App\Models\CampaignRequestTime;
use App\Models\Category;
use App\Models\Hashtag;
use App\Models\Influencer;
use App\Models\InfluencerDetail;
use App\Models\InfluencerRequestDetail;
use App\Models\InfluencerRequestTime;
use App\Models\InfluencerRequest;
use App\Models\RequestTask;
use App\Models\SmCampaign;
use App\Models\Statistic;
use App\Services\PricingCalculator;
use App\Models\Task;
use App\Models\User;
use App\Notifications\RequestInfluencer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Support\CampaignHelpers;
use App\Models\AdminHashtag;
use App\Constants\CampaignType;


class CreateCampaignController extends Controller
{
    public function createCampaignSteps()
    {
        if (Auth::user()->user_type == 'influencer') {
            return response()->view('errors.' . '404', [], 404);
        }

        $influencerData =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
            ->leftjoin('campaigns','campaigns.campaign_id','=','influencer_request_details.compaign_id')
            ->select('influencer_request_details.*', 'campaigns.has_started', 'influencer_details.id as i_id', 'influencer_details.user_id as i_user_id', 'influencer_request_accepts.request', 'influencer_request_accepts.id as influencer_request_accept_id', DB::raw('COUNT(influencer_request_accepts.id) AS total_count'), DB::raw('SUM(influencer_request_accepts.request) AS accept_request'), DB::raw('COUNT(influencer_request_details.id) AS total_influencer_count'))
            ->where('influencer_request_details.user_id', Auth::id())
            ->where('influencer_request_details.status', NULL)
            ->where('campaigns.has_started', false)
            ->groupBy('influencer_request_details.compaign_id')
            ->orderBy('influencer_request_details.id', 'desc')->get();

        $open_campaigns = 0;
        if (isset($influencerData)) {
            foreach ($influencerData as $row) {
                if ($row->accept_request > 0 || $row->review != '2' && $row->finish != '0') {
                    $open_campaigns++;
                }
            }
        }

        $influencerDataActive = InfluencerRequestDetail::join(
            'influencer_details',
            'influencer_request_details.influencer_detail_id', '=',
            'influencer_details.id'
        )
        ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
        ->leftjoin(
            'influencer_request_accepts',
            'influencer_request_accepts.influencer_request_detail_id', '=',
            'influencer_request_details.id'
        )
        ->select(
            'influencer_request_details.*',
            'influencer_details.id as i_id',
            'influencer_details.user_id as i_user_id',
            'influencer_request_accepts.request',
            'influencer_request_accepts.id as influencer_request_accept_id',
            DB::raw('COUNT(influencer_request_accepts.id) AS total_count'),
            DB::raw('SUM(influencer_request_accepts.request) AS accept_request'),
            DB::raw('COUNT(influencer_request_details.id) AS total_influencer_count')
        )
        ->where('influencer_request_details.user_id', Auth::id())
        ->groupBy('influencer_request_details.compaign_id')
        ->orderBy('influencer_request_details.id', 'desc')->get();

        $active_campaigns = 0;
        foreach ($influencerDataActive as $row) {
            if ($row->invoice_id != NULL && $row->finish != '1') {
                $active_campaigns++;
            }
        }

        $total_campaigns  = $open_campaigns + $active_campaigns;
        if ($total_campaigns >= 15) {
            return redirect()->back()->with('info', 'Maximum number of campaign creation limit reached.');
        }

        return view('front-user.pages.create-campaign-main');
    }

    public function createCampaignSave(Request $request)
    {
        $formData = (object) request()->except(['_token']);

        $selectedInfluencers = json_decode($formData->selectedInfluencers, true);

        $generatedCampaignId = null;

        $lastCampaign = Campaign::orderBy('id', 'desc')->first();
        if ($lastCampaign && $lastCampaign->id) {
            $nextId = $lastCampaign->id + 1;
            $generatedCampaignId = 'C-' . str_pad($nextId, 8, '0', STR_PAD_LEFT);
        }

        if ($generatedCampaignId == null) {
            throw new \Exception('Failed to create new campaign (id generation failed).');
        }

        // $social_media_name = $media = $request->social_media_name;

        // possible values: Story, Story - Picture, Story - Video, Reel, Story - Picture, Story - Video
        // $select_type = $request->post_type;
        // possible values: Boost me, Reaction video, Survey
        // $type = $request->campaign_type;
        
        // possible values: content, photo, video
        // $post_type_content = $ptc = $request->boost_me_content_type ?? null;

        foreach ($selectedInfluencers as $influencerDetailId => $influencerData) {
            // $influencerData has the following array keys:
            // influencerId, price, vat, followers, isSmallBusinessOwner
            $influencerDetail = InfluencerDetail::where('id', $influencerDetailId)->first();

            $influencerUser = User::whereId($influencerDetail->user_id)->first();
            if (empty($influencerUser)) {
                continue;
            }

            $influencerRequestDetail = InfluencerRequestDetail::create([
                'user_id' => Auth::id(),
                'influencer_detail_id' => $influencerDetail->id,
                'advertising' => $formData->post_type,
                'media' => $formData->social_media_name,
                'name' => $formData->name, // This is the brand name
                'social' => $formData->social_media_name,
                'time' => '10', // in days
                'total_amount' => number_format($influencerData['price'], 2) + number_format($influencerData['vat'], 2), // [example] 5.00 + 0.95 = 5.95
                'influencer_price' => $influencerData['price'], // [example] $influencerData['price'] = 5.00
                'compaign_id' => $generatedCampaignId,
                'compaign_title' => $formData->campaign_title,
                'task' => isset($formData->task_additional) ? implode(',', $formData->task_additional) : '',
                'post_type' => $formData->campaign_type, // This is actually the campaign type
                'post_content_type' => isset($formData->boost_me_content_type) ? $formData->boost_me_content_type : null
            ]);

            $brandUser = User::whereId(Auth::id())->first();

            $adminGamification = AdminGamification::where('select_type', 'Point-Rules')->first();

            Statistic::create([
                'user_id' => $influencerDetail->user_id,
                'points' => $adminGamification->repeat_bookings,
                'type' => '1',
                'title' =>  '[' . $generatedCampaignId . ']</br>' .
                    $adminGamification->repeat_bookings .
                    ' points gained for repeated booking from ' .
                    $influencerRequestDetail->user->first_name,
                'date' => date('Y-m-d H:i:s'),
            ]);

            dispatch(new NewRequestInfluencer($influencerUser, $brandUser, $influencerRequestDetail));

            $influencerUser->notify(new RequestInfluencer($influencerUser, $brandUser, $influencerRequestDetail));
        }

        Campaign::create([
            'campaign_id' => $generatedCampaignId,
            'campaign_title' => $formData->campaign_title,
            'total_amount' => $formData->total_amount
        ]);

        $tasksToBeCreated = [];

        $tasks = CampaignHelpers::getCampaignTasks(
            $formData->social_media_name,
            $formData->campaign_type,
            $formData->post_type,
            isset($formData->boost_me_content_type) ? $formData->boost_me_content_type : null
        );

        if ($tasks) {
            foreach ($tasks as $task) {
                if ($task->task_type == 'required' &&  $task->input_type == 'SocialMediaName') {
                    $mentions = 'mentions' . $task->id;
                    $tasksToBeCreated[] = [
                        'user_id' => Auth::id(),
                        'influencer_request_detail_id' => $generatedCampaignId,
                        'task_id' => $task->id,
                        'value' => $request->$mentions,
                        'type' => $task->input_type
                    ];
                } elseif ($task->task_type == 'required' &&  $task->input_type == 'Link') {
                    $site = 'site' . $task->id;
                    $tasksToBeCreated[] = [
                        'user_id' => Auth::id(),
                        'influencer_request_detail_id' => $generatedCampaignId,
                        'task_id' => $task->id,
                        'value' => $request->$site,
                        'type' => $task->input_type
                    ];
                } elseif ($task->task_type == 'required' &&  $task->input_type == 'UploadContent') {
                    $filepond = 'filepond' . $task->id;
                    if ($request->hasFile($filepond)) {
                        $allowedfileExtension = [
                            'jpeg', 'jpg', 'png', 'svg',
                            'webp', 'heic', 'mp4', 'mov',
                            'avi', 'mkv', 'webm', 'm4v', 'wmv'
                        ];
                        $filepond = $request->file($filepond);
                        $extension = $filepond->getClientOriginalExtension();

                        if (!in_array($extension, $allowedfileExtension)) {
                            \Log::warning('Invalid file extension', [
                                'extension' => $extension,
                                'allowed' => $allowedfileExtension
                            ]);
                            continue;
                        }
                        
                        \Log::info('File upload attempt', [
                            'field_name' => 'filepond' . $task->id,
                            'original_name' => $filepond->getClientOriginalName(),
                            'extension' => $extension,
                            'size' => $filepond->getSize(),
                            'mime_type' => $filepond->getMimeType()
                        ]);
                        
                        $filename = $filepond->store('files', 'public');

                        $tasksToBeCreated[] = [
                            'user_id' => Auth::id(),
                            'influencer_request_detail_id' => $generatedCampaignId,
                            'task_id' => $task->id,
                            'value' => $filename,
                            'type' => $task->input_type
                        ];

                        \Log::info('File uploaded successfully', [
                            'stored_path' => $filename,
                            'full_path' => storage_path('app/' . $filename)
                        ]);
                    } else {
                        \Log::warning('No file found for field: filepond' . $task->id);
                    }
                } elseif ($task->task_type == 'required' &&  $task->input_type == 'Hashtag') {
                    $hashtag = 'hashtags' . $task->id;
                    $hashtags = [];
                    if (isset($request->$hashtag)) {
                        foreach ($request->$hashtag as $hash) {
                            if ($hash != '') {
                                $hasht = json_decode($hash);
                                foreach ($hasht as $tag) {
                                    array_push($hashtags, $tag->value);
                                }
                            }
                        }
                    }
                    $tasksToBeCreated[] = [
                        'user_id' => Auth::id(),
                        'influencer_request_detail_id' => $generatedCampaignId,
                        'task_id' => $task->id,
                        'value' => implode(',', $hashtags),
                        'type' => 'Hashtag'
                    ];
                } elseif ($task->task_type == 'required' &&  $task->input_type == 'Info') {
                    $tasksToBeCreated[] = [
                        'user_id' => Auth::id(),
                        'influencer_request_detail_id' => $generatedCampaignId,
                        'task_id' => $task->id,
                        'type' => $task->input_type
                    ];
                }
            }

            $task_additional = $request->task_additional;
            if (isset($task_additional)) {
                foreach ($task_additional as $additionalTaskId) {
                    foreach ($tasks as $task) {
                        if ($task->task_type == 'additional' &&  $task->input_type == 'SocialMediaName' && $task->id == $additionalTaskId) {
                            $mentions = 'mentions' . $task->id;
                            $tasksToBeCreated[] = [
                                'user_id' => Auth::id(),
                                'influencer_request_detail_id' => $generatedCampaignId,
                                'task_id' => $task->id,
                                'value' => $request->$mentions,
                                'type' => $task->input_type
                            ];
                        } elseif ($task->task_type == 'additional' &&  $task->input_type == 'Link' && $task->id == $additionalTaskId) {
                            $site = 'site' . $task->id;
                            $tasksToBeCreated[] = [
                                'user_id' => Auth::id(),
                                'influencer_request_detail_id' => $generatedCampaignId,
                                'task_id' => $task->id,
                                'value' => $request->$site,
                                'type' => $task->input_type
                            ];
                        } elseif ($task->task_type == 'additional' &&  $task->input_type == 'UploadContent' && $task->id == $additionalTaskId) {
                            $filepond = 'filepond' . $task->id;
                            if ($request->hasFile($filepond)) {
                                $filepond = $request->file($filepond);
                                $extension = $filepond->getClientOriginalExtension();
                                $filename = $filepond->store('files', 'public');
                            }
                            $tasksToBeCreated[] = [
                                'user_id' => Auth::id(),
                                'influencer_request_detail_id' => $generatedCampaignId,
                                'task_id' => $task->id,
                                'value' => $filename,
                                'type' => $task->input_type
                            ];
                        } elseif ($task->task_type == 'additional' &&  $task->input_type == 'Hashtag' && $task->id == $additionalTaskId) {
                            $hashtag = 'hashtags' . $task->id;
                            $hashtags = [];
                            if (isset($request->$hashtag)) {
                                foreach ($request->$hashtag as $hash) {
                                    if ($hash != '') {
                                        $hasht = json_decode($hash);
                                        foreach ($hasht as $tag) {
                                            array_push($hashtags, $tag->value);
                                        }
                                    }
                                }
                            }

                            $tasksToBeCreated[] = [
                                'user_id' => Auth::id(),
                                'influencer_request_detail_id' => $generatedCampaignId,
                                'task_id' => $task->id,
                                'value' => implode(',', $hashtags),
                                'type' => 'Hashtag'
                            ];
                        } elseif ($task->task_type == 'additional' &&  $task->input_type == 'Info' && $task->id == $additionalTaskId) {
                            $tasksToBeCreated[] = [
                                'user_id' => Auth::id(),
                                'influencer_request_detail_id' => $generatedCampaignId,
                                'task_id' => $task->id,
                                'type' => $task->input_type
                            ];
                        }
                    }
                }
            }
        }

        if (count($tasksToBeCreated) > 0) {
            foreach ($tasksToBeCreated as $taskToCreate) {
                RequestTask::create($taskToCreate);
            }
        }

        return redirect('/open-campaigns')->with('success', 'Request sent successfully.');
    }

    public function getInfluencers(Request $request)
    {
        $formData = request()->except(['_token']);

        $social_media_name = $request->social_media ?? $request->social_media_name;
        
        // Can be one of Reel, Story - Picture, Story - Video...(?!)
        // This is not being used in this method :shrug:
        // $select_type = $request->post_type;

        // This is not being used in this method
        // $post_type_content = $request->boost_me_content_type;

        // Get influencers using the new pricing system
        $influencerDetails = InfluencerDetail::whereHas('user.stripeAccount')
            ->leftjoin('social_connects', 'social_connects.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('influencers', 'influencers.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('hashtags', 'hashtags.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('users', 'users.id', '=', 'influencer_details.user_id')
            ->leftjoin('influencer_requests', function ($join) {
                $join->on('influencer_details.id', '=', 'influencer_requests.influencer_id')->where('influencer_requests.user_id', '=', Auth::id());
            })
            ->select(
                'influencer_details.*',
                'influencer_details.id as i_id',
                'influencer_details.user_id as i_user_id',
                'social_connects.url',
                'social_connects.picture',
                'social_connects.name as username',
                'social_connects.followers',
                'social_connects.media',
                'influencers.id as influencer_id',
                'influencers.followers as influencer_followers',
                'influencers.gamification_percentage',
                'influencers.flag_story_insight_missing',
                'influencers.flag_reel_insight_missing',
                'influencers.flag_feed_insight_missing',
                'influencers.admin_override_show_hidden',
                'hashtags.tags',
                'influencer_requests.request as request',
                'users.trophy'
            );

        // Filter by social media platform
        if (!empty($social_media_name)) {
            $influencerDetails->where('social_connects.media', $social_media_name);
        }

        // Filter by campaign type - only show influencers with sufficient data for the campaign type
        if (!empty($request->campaign_type)) {
            $campaignType = $request->campaign_type;

            // Map campaign types to their corresponding flag columns based on requirements
            // Note: This mapping differs from CampaignPostType helper for Survey campaigns
            // CampaignPostType maps Survey->story, but marketplace filtering requires Survey->feed
            $flagColumn = match ($campaignType) {
                CampaignType::BOOST_ME, 'Boost me', 'boost me' => 'flag_story_insight_missing',
                CampaignType::REACTION_VIDEO, 'Reaction video', 'reaction video' => 'flag_reel_insight_missing',
                CampaignType::SURVEY, 'survey' => 'flag_feed_insight_missing',  // Per requirements: Survey uses feed posts
                default => null
            };

            // Debug logging
            \Log::info('CreateCampaignController: Filtering influencers by campaign type', [
                'campaign_type' => $campaignType,
                'flag_column' => $flagColumn,
                'user_id' => Auth::id()
            ]);

            // Only apply filtering if we have a valid campaign type mapping
            if ($flagColumn) {
                $influencerDetails->where(function ($query) use ($flagColumn) {
                    // Show influencers where the flag is NOT true (false, null, or 0)
                    // OR where admin_override_show_hidden is true (admin override)
                    $query->where('influencers.admin_override_show_hidden', true)
                          ->orWhere('influencers.' . $flagColumn, false)
                          ->orWhereNull('influencers.' . $flagColumn);
                });
            }
        }

        $influencerDetails = $influencerDetails->groupBy('influencer_details.id')->get();

        $hashtags = '';
        $category = '';
        $pricing = '';
        $pricingCalculator = new PricingCalculator();

        foreach ($influencerDetails as &$influencerDetail) {
            $hashtags = Hashtag::where('user_id', $influencerDetail->i_user_id)->get('tags');
            $influencerDetail->tags = $hashtags;

            $category = Category::where('id', $influencerDetail->category_id)->get('name');
            $influencerDetail->category = $category->implode('name', ',');

            $pricing = AdminGamification::where('type', $influencerDetail->trophy)->get('type');
            $influencerDetail->pricing = $pricing->implode('type', ',');

            // Use stored pricing first, then fall back to real-time calculation
            if ($influencerDetail->influencer_id && $request->campaign_type) {
                try {
                    $influencer = Influencer::find($influencerDetail->influencer_id);
                    if ($influencer) {
                        // First, try to get stored price
                        $storedPrice = $influencer->prices()
                            ->where('campaign_type', $request->campaign_type)
                            ->first();

                        if ($storedPrice) {
                            // Use stored price from influencer_prices table
                            $influencerDetail->type_price = $storedPrice->price;
                            $influencerDetail->calculated_price = $storedPrice->price;
                            $influencerDetail->post_type = $storedPrice->breakdown['post_type'] ?? null;
                            $influencerDetail->price_breakdown = $storedPrice->breakdown ?? null;
                            $influencerDetail->price_source = 'stored';
                        } else {
                            // Fall back to real-time calculation
                            $priceResult = $pricingCalculator->calculatePrice($influencer, $request->campaign_type);
                            if (isset($priceResult['price'])) {
                                $influencerDetail->type_price = $priceResult['price'];
                                $influencerDetail->calculated_price = $priceResult['price'];
                                $influencerDetail->post_type = $priceResult['breakdown']['post_type'] ?? null;
                                $influencerDetail->price_breakdown = $priceResult['breakdown'] ?? null;
                                $influencerDetail->price_source = 'calculated';
                            } else {
                                $influencerDetail->type_price = $this->getFallbackPrice($influencerDetail, $request->campaign_type);
                                $influencerDetail->calculated_price = null;
                                $influencerDetail->price_source = 'fallback';
                            }
                        }
                    } else {
                        $influencerDetail->type_price = $this->getFallbackPrice($influencerDetail, $request->campaign_type);
                        $influencerDetail->calculated_price = null;
                        $influencerDetail->price_source = 'fallback';
                    }
                } catch (\Exception $e) {
                    $influencerDetail->type_price = $this->getFallbackPrice($influencerDetail, $request->campaign_type);
                    $influencerDetail->calculated_price = null;
                    $influencerDetail->price_error = 'Pricing calculation failed: ' . $e->getMessage();
                    $influencerDetail->price_source = 'error';
                }
            } else {
                // No influencer record or campaign type - use fallback pricing
                $influencerDetail->type_price = $this->getFallbackPrice($influencerDetail, $request->campaign_type);
                $influencerDetail->calculated_price = null;
                $influencerDetail->price_source = 'fallback';
            }
        }

        $totalCount = InfluencerRequest::where('user_id', Auth::id())->count();

        $totalFollowers = InfluencerRequest::leftjoin('social_connects', 'social_connects.user_id', '=', 'influencer_requests.user_id')
            ->where('influencer_requests.user_id', Auth::id())
            ->count('social_connects.followers');

        // Calculate total price using new pricing system
        $totalPrice = 0;
        $requestedInfluencers = InfluencerRequest::where('influencer_requests.user_id', Auth::id())
            ->leftjoin('influencer_details', 'influencer_details.id', '=', 'influencer_requests.influencer_id')
            ->leftjoin('influencers', 'influencers.user_id', '=', 'influencer_details.user_id')
            ->select('influencers.*', 'influencer_requests.*')
            ->get();

        foreach ($requestedInfluencers as $requestedInfluencer) {
            if ($requestedInfluencer->id && $request->campaign_type) {
                try {
                    $influencer = Influencer::find($requestedInfluencer->id);
                    if ($influencer) {
                        // First try stored price, then calculate
                        $storedPrice = $influencer->prices()
                            ->where('campaign_type', $request->campaign_type)
                            ->first();

                        if ($storedPrice) {
                            $totalPrice += $storedPrice->price;
                        } else {
                            // Fall back to real-time calculation
                            $priceResult = $pricingCalculator->calculatePrice($influencer, $request->campaign_type);
                            if (isset($priceResult['price'])) {
                                $totalPrice += $priceResult['price'];
                            }
                        }
                    }
                } catch (\Exception $e) {
                    // Skip this influencer if pricing fails
                }
            }
        }

        return \Response::json(\View::make(
            'front-user.pages.create-campaign.campaign-marketplace-influencers',
            compact(
                'influencerDetails', 'formData', 'totalCount', 'totalFollowers',
                'totalPrice', 'hashtags', 'category', 'pricing', 'social_media_name'
            )
        )->render());
    }

    /**
     * Get fallback pricing for influencers without pricing records
     */
    private function getFallbackPrice($influencerDetail, $campaignType)
    {
        // Use follower count from social_connects as basis for fallback pricing
        $followers = $influencerDetail->followers ?? 10000; // Default to 10k if no follower data

        // Simple fallback pricing based on campaign type and followers
        switch($campaignType) {
            case 'Boost me':
            case 'Boost Me':
                $basePricePerThousand = 0.50;
                break;
            case 'Survey':
                $basePricePerThousand = 0.50;
                break;
            case 'Reaction Video':
                $basePricePerThousand = 1.50;
                break;
            default:
                $basePricePerThousand = 0.50;
                break;
        }

        // Calculate price: (followers / 1000) * base_price
        $price = ($followers / 1000) * $basePricePerThousand;

        // Minimum price of €5
        return max($price, 5.0);
    }

    public function filterInfluencers(Request $request)
    {
        $category = '';
        $hashtags = '';

        $influencerDetails = InfluencerDetail::leftjoin('social_connects', 'social_connects.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('influencers', 'influencers.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('hashtags', 'hashtags.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('users', 'users.id', '=', 'influencer_details.user_id')
            ->leftjoin('influencer_requests', function ($join) {
                $join->on('influencer_details.id', '=', 'influencer_requests.influencer_id')
                    ->where('influencer_requests.user_id', '=', Auth::id());
            })
            ->select(
                'influencer_details.id as i_id',
                'influencer_details.user_id as i_user_id',
                'influencer_details.*',
                'social_connects.media',
                'social_connects.url',
                'social_connects.picture',
                'social_connects.name as username',
                'social_connects.followers',
                'influencers.id as influencer_id',
                'influencers.followers as influencer_followers',
                'influencers.gamification_percentage',
                'hashtags.tags',
                'influencer_requests.request as request',
                'users.trophy'
            )
            ->where('social_connects.media', $request->social_media_name);
            // Don't filter by influencer records - handle missing pricing gracefully

        if (isset($request->rank) && $request->rank != '') {
            $influencerDetails->where('users.trophy', $request->rank);
        }

        if (isset($request->target_age) && $request->target_age != 'Select') {
            $influencerDetails->where('influencer_details.ages', $request->target_age);
        }
        
        if (isset($request->language) && $request->language != 'Select') {
            $influencerDetails->where('influencer_details.content_language', $request->language);
        }
        
        if (isset($request->content_attracts) && $request->content_attracts != 'Select') {
            $influencerDetails->where('influencer_details.content_attracts', $request->content_attracts);
        }

        if (isset($request->gender) && $request->gender != 'Select') {
            $influencerDetails->where('influencer_details.gender', $request->gender);
        }

        if (isset($request->hashtag) && $request->hashtag != 'Select') {
            $condition = " 1 = 1 ";
            $subject_con_arr = [];

            foreach ($request->hashtag as $row) {
                if ($row != '' && $row != 'Select') {
                    $subject_con_arr[] = " FIND_IN_SET('" . $row . "', hashtags.tags) ";
                }
            }

            if (count($subject_con_arr) > 0) {
                $condition .= ' and (' . implode(' or ', $subject_con_arr) . ')';
            }

            $influencerDetails->whereRaw($condition);
        }

        if (isset($request->influencer_type) && $request->influencer_type != 'Select') {
            $influencerDetails->where('influencer_details.influencer_type', $request->influencer_type);
        }

        // Note: Price filtering will be done after calculating prices
        $priceFilter = null;
        if (isset($request->price) && $request->price > 0) {
            $priceFilter = (int)$request->price;
        }

        if (isset($request->followers)) {
            if ((int)$request->followers > 0) {
                $influencerDetails->where('social_connects.followers', '>', (int)$request->followers);
            }
        }

        if (isset($request->mp_socialmedia)) {
            $influencerDetails->where('social_connects.media', $request->mp_socialmedia);
        }

        if (isset($request->category_id)) {
            $condition = " 1 = 1 ";
            $subject_con_arr = [];

            foreach ($request->category_id as $cat) {
                if ($cat != '') {
                    $subject_con_arr[] = " FIND_IN_SET('" . $cat . "', influencer_details.category_id) ";
                }
            }

            if (count($subject_con_arr) > 0) {
                $condition .= ' and (' . implode(' or ', $subject_con_arr) . ')';
            }

            $influencerDetails->whereRaw($condition);
        }

        $influencerDetails = $influencerDetails->where('influencer_details.publish', 'Publish')->where('users.status', 1);

        // Get the data first, then we'll calculate prices and apply filters/sorting
        $influencerDetails = $influencerDetails->groupBy('influencer_details.id')->get();

        // Calculate prices for each influencer
        $pricingCalculator = new PricingCalculator();
        $campaignType = $request->campaign_type;

        foreach ($influencerDetails as &$influencerDetail) {
            if ($influencerDetail->influencer_id && $campaignType) {
                try {
                    $influencer = Influencer::find($influencerDetail->influencer_id);
                    if ($influencer) {
                        $priceResult = $pricingCalculator->calculatePrice($influencer, $campaignType);
                        if (isset($priceResult['price'])) {
                            $influencerDetail->type_price = $priceResult['price'];
                            $influencerDetail->calculated_price = $priceResult['price'];
                        } else {
                            $influencerDetail->type_price = $this->getFallbackPrice($influencerDetail, $campaignType);
                            $influencerDetail->calculated_price = null;
                        }
                    } else {
                        $influencerDetail->type_price = $this->getFallbackPrice($influencerDetail, $campaignType);
                        $influencerDetail->calculated_price = null;
                    }
                } catch (\Exception $e) {
                    $influencerDetail->type_price = $this->getFallbackPrice($influencerDetail, $campaignType);
                    $influencerDetail->calculated_price = null;
                }
            } else {
                $influencerDetail->type_price = $this->getFallbackPrice($influencerDetail, $campaignType);
                $influencerDetail->calculated_price = null;
            }
        }

        // Apply price filter if specified
        if ($priceFilter) {
            $influencerDetails = $influencerDetails->filter(function ($influencer) use ($priceFilter) {
                return $influencer->type_price <= $priceFilter;
            });
        }

        // Apply sorting
        if (isset($request->sort_by) && $request->sort_by != 'Select') {
            $sort = explode('-', $request->sort_by);
            $param = $sort[0];
            $order = $sort[1];

            if ($sort[0] == 'price') {
                $influencerDetails = $influencerDetails->sortBy('type_price', SORT_REGULAR, $order === 'desc');
            } else {
                $influencerDetails = $influencerDetails->sortBy($param, SORT_REGULAR, $order === 'desc');
            }
        } else {
            $influencerDetails = $influencerDetails->sortByDesc('i_id');
        }

        // Convert back to collection values (remove keys)
        $influencerDetails = $influencerDetails->values();
        foreach ($influencerDetails as &$row) {
            $hashtags = Hashtag::where('user_id', $row->i_user_id)->get('tags');
            $row->tags = $hashtags;

            $category = Category::where('id', $row->category_id)->get('name');
            $row->category = $category->implode('name', ',');

            $pricing = AdminGamification::where('type', $row->trophy)->get('type');
            $row->pricing = $pricing->implode('type', ',');
        }

        $formData = request()->except(['_token']);

        $totalCount = InfluencerRequest::where('user_id', Auth::id())->count();

        $totalFollowers = InfluencerRequest::leftjoin('social_connects',  'social_connects.user_id', '=', 'influencer_requests.user_id')
            ->where('influencer_requests.user_id', Auth::id())
            ->count('social_connects.followers');

        // Calculate total price using new pricing system
        $totalPrice = 0;
        $requestedInfluencers = InfluencerRequest::where('influencer_requests.user_id', Auth::id())
            ->leftjoin('influencer_details', 'influencer_details.id', '=', 'influencer_requests.influencer_id')
            ->leftjoin('influencers', 'influencers.user_id', '=', 'influencer_details.user_id')
            ->select('influencers.*', 'influencer_requests.*')
            ->get();

        foreach ($requestedInfluencers as $requestedInfluencer) {
            if ($requestedInfluencer->id && $campaignType) {
                try {
                    $influencer = Influencer::find($requestedInfluencer->id);
                    if ($influencer) {
                        $priceResult = $pricingCalculator->calculatePrice($influencer, $campaignType);
                        if (isset($priceResult['price'])) {
                            $totalPrice += $priceResult['price'];
                        }
                    }
                } catch (\Exception $e) {
                    // Skip this influencer if pricing fails
                }
            }
        }

        return \Response::json(
            \View::make('front-user.pages.create-campaign.campaign-marketplace-influencers',
            compact('influencerDetails', 'category', 'hashtags', 'formData', 'totalCount', 'totalFollowers', 'totalPrice', 'pricing')
        )->render());
    }

    public function getTasksInput(Request $request)
    {
        $media = $request->media;

        // possible values: Boost me, Reaction video, Survey
        $type = $request->type;

        // possible values: Story, Story - Picture, Story - Video, Reel, Story - Picture, Story - Video
        $select_type = $request->select_type;

        // possible values: content, photo, video
        $post_type_content = $request->ptc;

        $tasks = CampaignHelpers::getCampaignTasks($media, $type, $select_type, $post_type_content);

        $hashtag_arr = [];
        $count = Hashtag::groupBy('tags')->get();
        foreach ($count as $row) {
            array_push($hashtag_arr, $row['tags']);
        }

        $task_additional = [];
        $task_additional = $request->task_additional;

        $adminHashtag = AdminHashtag::first();

        return view('front-user.pages.create-campaign.campaign-info-tasks-input', compact('tasks', 'hashtag_arr', 'media', 'task_additional', 'adminHashtag'));
    }

    /**
     * During the create-campaign phase, these is the method called to get list of tasks
     * available for each type of campaign, including required and additional.
     * 
     * Here is a sample http request details:
     * Boost me
     * --------
     * Share Content
     *     o Instagram o Story
     *     http://127.0.0.1:8000/get-tasks?media=instagram&type=Boost%20me&select_type=Story&ptc=content
     * 
     * Photo
     *     o Instagram o Story - Picture
     *     http://127.0.0.1:8000/get-tasks?media=instagram&type=Boost%20me&select_type=Story%20-%20Picture&ptc=photo
     * 
     * Video
     *     o Instagram o Story - Video
     *     http://127.0.0.1:8000/get-tasks?media=instagram&type=Boost%20me&select_type=Story%20-%20Video&ptc=video
     * 
     * Reaction video
     * --------------
     *     o Instagram o Reel
     *     http://127.0.0.1:8000/get-tasks?media=instagram&type=Reaction%20video&select_type=Reel&ptc=video
     * 
     * 
     * Survey
     * ------
     *     o Instagram o Story - Picture
     *     http://127.0.0.1:8000/get-tasks?media=instagram&type=Survey&select_type=Story%20-%20Picture&ptc=video
     * 
     *     o Instagram o Story - Video
     *     http://127.0.0.1:8000/get-tasks?media=instagram&type=Survey&select_type=Story%20-%20Video&ptc=video
     */
    public function getTasks(Request $request)
    {
        $media = $request->media;

        // possible values: Boost me, Reaction video, Survey
        $type = $request->type;

        // possible values: Story, Story - Picture, Story - Video, Reel, Story - Picture, Story - Video
        $select_type = $request->select_type;

        // possible values: content, photo, video
        $post_type_content = $request->ptc;

        $tasks = CampaignHelpers::getCampaignTasks($media, $type, $select_type, $post_type_content);
        return view('front-user.pages.create-campaign.campaign-additional-tasks-select', compact('tasks'));
    }
}
