<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Influencer;
use App\Models\InfluencerPrice;
use App\Jobs\RepriceInfluencerJob;
use App\Services\PricingCalculator;
use App\Constants\CampaignType;
use App\Constants\PostType;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class AdminInfluencerPricingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    /**
     * Show pricing details for a specific influencer
     */
    public function show(int $userId): View
    {
        // Get the user and their influencer record
        $user = User::with(['influencer.prices'])->findOrFail($userId);
        
        if (!$user->influencer) {
            abort(404, 'Influencer performance data not found');
        }

        $influencer = $user->influencer;
        
        // Get all pricing data organized by campaign type
        $pricingData = [];
        foreach (CampaignType::all() as $campaignType) {
            $price = $influencer->prices()
                ->where('campaign_type', $campaignType)
                ->first();
                
            $pricingData[$campaignType] = $price;
        }

        return view('admin.influencers.pricing', compact('user', 'influencer', 'pricingData'));
    }

    /**
     * Reprice a specific influencer via AJAX
     */
    public function reprice(Request $request, int $userId): JsonResponse
    {
        $user = User::with('influencer')->findOrFail($userId);

        if (!$user->influencer) {
            return response()->json([
                'success' => false,
                'message' => 'Influencer performance data not found'
            ], 404);
        }

        try {
            // Run the reprice job synchronously for immediate results
            (new RepriceInfluencerJob($user->influencer->id))->handle(app(PricingCalculator::class));

            return response()->json([
                'success' => true,
                'message' => 'Repricing completed successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Repricing failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
