<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Influencer;
use App\Models\IgPost;
use App\Models\SocialConnect;
use App\Models\User;
use App\Services\PricingCalculator;
use App\Jobs\ComputeAverageReachJob;
use App\Jobs\UpdateInfluencerDataFromSocialJob;
use App\Models\InfluencerPrice;
use App\Constants\CampaignType;
use App\Constants\PostType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class InfluencerTestingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    /**
     * Display list of all influencers for testing
     */
    public function index()
    {
        $influencers = Influencer::with('user')->paginate(20);

        return view('admin.influencer-testing.index', compact('influencers'));
    }

    /**
     * Create influencer records from users with user_type = 'influencer'
     */
    public function createFromUsers()
    {
        try {
            // Get all users with user_type = 'influencer' who don't have influencer records yet
            $influencerUsers = User::where('user_type', 'influencer')
                ->whereNotExists(function ($query) {
                    $query->select(DB::raw(1))
                        ->from('influencers')
                        ->whereColumn('influencers.user_id', 'users.id');
                })
                ->get();

            if ($influencerUsers->isEmpty()) {
                return redirect()
                    ->route('influencer-testing.index')
                    ->with('info', 'No new influencer users found to create records for.');
            }

            $created = 0;
            foreach ($influencerUsers as $user) {
                Influencer::create([
                    'user_id' => $user->id,
                    'followers' => rand(5000, 100000), // Random for testing
                    'gamification_percentage' => rand(5, 25) / 100, // 5-25%
                ]);
                $created++;
            }

            return redirect()
                ->route('influencer-testing.index')
                ->with('success', "Successfully created {$created} influencer records from user accounts!");

        } catch (\Exception $e) {
            return redirect()
                ->route('influencer-testing.index')
                ->with('error', 'Error creating influencer records: ' . $e->getMessage());
        }
    }

    /**
     * Show detailed testing page for specific influencer
     */
    public function show(Influencer $influencer)
    {
        // Get existing IG posts for this influencer
        $igPosts = IgPost::where('influencer_id', $influencer->id)
            ->orderBy('posted_at', 'desc')
            ->get()
            ->groupBy('post_type');

        // Get post counts by type
        $postCounts = [
            'story' => $igPosts->get('story', collect())->count(),
            'reel' => $igPosts->get('reel', collect())->count(),
            'feed' => $igPosts->get(PostType::FEED_POST, collect())->count(), // Use correct post type
        ];

        // Get current pricing for all campaign types
        // First try to load stored prices, then fall back to real-time calculation
        $pricingCalculator = new PricingCalculator();
        $currentPrices = [];

        foreach (['Boost Me', 'Survey', 'Reaction Video'] as $campaignType) {
            // Try to load stored price first
            $storedPrice = $influencer->prices()
                ->where('campaign_type', $campaignType)
                ->first();

            // Debug logging
            \Log::info('InfluencerTestingController: Checking stored price', [
                'influencer_id' => $influencer->id,
                'campaign_type' => $campaignType,
                'stored_price_found' => $storedPrice ? true : false,
                'stored_price_id' => $storedPrice ? $storedPrice->id : null,
                'stored_price_value' => $storedPrice ? $storedPrice->price : null,
            ]);

            if ($storedPrice) {
                // Use stored price data
                $currentPrices[$campaignType] = [
                    'price' => $storedPrice->price,
                    'breakdown' => $storedPrice->breakdown,
                    'stored' => true,
                    'priced_at' => $storedPrice->priced_at,
                    'record_id' => $storedPrice->id // Add this for debugging
                ];
            } else {
                // Fall back to real-time calculation
                try {
                    $calculated = $pricingCalculator->calculatePrice($influencer, $campaignType);
                    $currentPrices[$campaignType] = array_merge($calculated, ['stored' => false]);
                } catch (\Exception $e) {
                    $currentPrices[$campaignType] = ['error' => $e->getMessage(), 'stored' => false];
                }
            }
        }

        return view('admin.influencer-testing.show', compact(
            'influencer', 
            'igPosts', 
            'postCounts', 
            'currentPrices'
        ));
    }

    /**
     * Generate fake Instagram posts for testing
     */
    public function generateFakePosts(Request $request, Influencer $influencer)
    {
        $request->validate([
            'post_type' => 'required|in:story,reel,post', // Use 'post' instead of 'feed'
            'count' => 'required|integer|min:1|max:100',
            'min_reach' => 'required|integer|min:0',
            'max_reach' => 'required|integer|min:0',
            'days_back' => 'required|integer|min:1|max:365',
        ]);

        $postType = $request->post_type;
        $count = (int) $request->count;
        $minReach = (int) $request->min_reach;
        $maxReach = (int) $request->max_reach;
        $daysBack = (int) $request->days_back;

        // Ensure min_reach <= max_reach
        if ($minReach > $maxReach) {
            [$minReach, $maxReach] = [$maxReach, $minReach];
        }

        $posts = [];
        for ($i = 0; $i < $count; $i++) {
            $posts[] = [
                'influencer_id' => $influencer->id,
                'post_type' => $postType,
                'reach' => rand($minReach, $maxReach),
                'posted_at' => Carbon::now()->subDays(rand(1, $daysBack)),
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        IgPost::insert($posts);

        return redirect()
            ->route('influencer-testing.show', $influencer)
            ->with('success', "Generated {$count} fake {$postType} posts with reach between {$minReach} and {$maxReach}");
    }

    /**
     * Clear all IG posts for an influencer and recalculate pricing with real data
     */
    public function clearPosts(Influencer $influencer)
    {
        try {
            $deletedCount = IgPost::where('influencer_id', $influencer->id)->delete();

            // Sync followers count from social_connects (source of truth)
            $realFollowersCount = $this->syncFollowersFromSocialConnect($influencer);

            // Reset average reach data
            $influencer->update([
                'ig_story_avg_reach' => null,
                'ig_reel_avg_reach' => null,
                'ig_feed_avg_reach' => null,
                'flag_story_insight_missing' => false,
                'flag_reel_insight_missing' => false,
                'flag_feed_insight_missing' => false,
                'avg_reach_computed_at' => null,
            ]);

            // Clear existing stored prices since they're based on test data
            $clearedPrices = $influencer->prices()->delete();

            // Immediately recalculate with real data
            $repricingResult = $this->recalculateInfluencerPricing($influencer);

            $message = "Successfully cleared {$deletedCount} IG posts, reset reach data";
            if ($realFollowersCount !== null) {
                $message .= ", synced followers count ({$realFollowersCount})";
            }
            if ($clearedPrices > 0) {
                $message .= ", cleared {$clearedPrices} stored prices";
            }
            $message .= ", and recalculated pricing with real data";
            if ($repricingResult['success_count'] > 0) {
                $message .= " ({$repricingResult['success_count']} campaign types repriced)";
            }

            return redirect()
                ->route('influencer-testing.show', $influencer)
                ->with('success', $message);

        } catch (\Exception $e) {
            return redirect()
                ->route('influencer-testing.show', $influencer)
                ->with('error', 'Error clearing test data: ' . $e->getMessage());
        }
    }

    /**
     * Sync followers count from social_connects table (source of truth)
     */
    private function syncFollowersFromSocialConnect(Influencer $influencer): ?int
    {
        try {
            // Get the user's social connect data (Instagram is primary)
            $socialConnect = SocialConnect::where('user_id', $influencer->user_id)
                ->where('media', 'instagram')
                ->first();

            if (!$socialConnect) {
                // Fallback to any social media if Instagram not found
                $socialConnect = SocialConnect::where('user_id', $influencer->user_id)
                    ->orderBy('followers', 'desc')
                    ->first();
            }

            if ($socialConnect && $socialConnect->followers) {
                $realFollowersCount = (int) $socialConnect->followers;

                // Update the influencer's followers count with real data
                $influencer->update(['followers' => $realFollowersCount]);

                \Log::info('Synced followers count from social_connects', [
                    'influencer_id' => $influencer->id,
                    'user_id' => $influencer->user_id,
                    'old_followers' => $influencer->getOriginal('followers'),
                    'new_followers' => $realFollowersCount,
                    'social_media' => $socialConnect->media
                ]);

                return $realFollowersCount;
            }

            \Log::warning('No social connect data found for follower sync', [
                'influencer_id' => $influencer->id,
                'user_id' => $influencer->user_id
            ]);

            return null;

        } catch (\Exception $e) {
            \Log::error('Failed to sync followers from social_connects', [
                'influencer_id' => $influencer->id,
                'user_id' => $influencer->user_id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Recalculate influencer pricing with real data
     */
    private function recalculateInfluencerPricing(Influencer $influencer): array
    {
        $successCount = 0;
        $errorCount = 0;

        try {
            // Step 1: Update social performance data from real social media data
            $updateSocialJob = new UpdateInfluencerDataFromSocialJob($influencer->id);
            $updateSocialJob->handle();

            // Step 2: Compute average reach from real posts
            $reachJob = new ComputeAverageReachJob($influencer->id);
            $reachJob->handle();

            // Step 3: Calculate and store new prices for all campaign types
            $pricingCalculator = new PricingCalculator();

            foreach (CampaignType::all() as $campaignType) {
                try {
                    $priceData = $pricingCalculator->calculatePrice($influencer, $campaignType);

                    // Store the calculated price
                    InfluencerPrice::updateOrCreate(
                        [
                            'influencer_id' => $influencer->id,
                            'campaign_type' => $campaignType
                        ],
                        [
                            'post_type' => $priceData['breakdown']['post_type'],
                            'price' => $priceData['price'],
                            'breakdown' => $priceData['breakdown'],
                            'priced_at' => now(),
                        ]
                    );
                    $successCount++;
                } catch (\Exception $e) {
                    $errorCount++;
                    \Log::warning("Failed to calculate price for campaign type {$campaignType}", [
                        'influencer_id' => $influencer->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            \Log::info('Successfully recalculated pricing after clearing test data', [
                'influencer_id' => $influencer->id,
                'user_id' => $influencer->user_id,
                'success_count' => $successCount,
                'error_count' => $errorCount
            ]);

            return [
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'total_count' => count(CampaignType::all())
            ];

        } catch (\Exception $e) {
            \Log::error('Failed to recalculate influencer pricing after clearing test data', [
                'influencer_id' => $influencer->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Recalculate average reach for influencer
     */
    public function recalculateReach(Influencer $influencer)
    {
        try {
            // Get post counts before calculation
            $postCounts = [
                'story' => IgPost::where('influencer_id', $influencer->id)->where('post_type', 'story')->count(),
                'reel' => IgPost::where('influencer_id', $influencer->id)->where('post_type', 'reel')->count(),
                'feed' => IgPost::where('influencer_id', $influencer->id)->where('post_type', PostType::FEED_POST)->count(), // Instagram feed posts
            ];

            // Store old values for comparison
            $oldValues = [
                'story' => $influencer->ig_story_avg_reach,
                'reel' => $influencer->ig_reel_avg_reach,
                'feed' => $influencer->ig_feed_avg_reach,
            ];

            // Run the job synchronously for immediate results
            $job = new ComputeAverageReachJob($influencer->id);
            $job->handle();

            // Refresh the model to get updated values
            $influencer->refresh();

            // Build detailed success message
            $message = "Reach recalculated successfully! ";
            $message .= "Posts processed: {$postCounts['story']} stories, {$postCounts['reel']} reels, {$postCounts['feed']} feed. ";

            $changes = [];
            if ($oldValues['story'] != $influencer->ig_story_avg_reach) {
                $changes[] = "Stories: " . number_format($oldValues['story'] ?? 0) . " → " . number_format($influencer->ig_story_avg_reach ?? 0);
            }
            if ($oldValues['reel'] != $influencer->ig_reel_avg_reach) {
                $changes[] = "Reels: " . number_format($oldValues['reel'] ?? 0) . " → " . number_format($influencer->ig_reel_avg_reach ?? 0);
            }
            if ($oldValues['feed'] != $influencer->ig_feed_avg_reach) {
                $changes[] = "Feed: " . number_format($oldValues['feed'] ?? 0) . " → " . number_format($influencer->ig_feed_avg_reach ?? 0);
            }

            if (!empty($changes)) {
                $message .= "Changes: " . implode(', ', $changes);
            } else {
                $message .= "No changes (averages remained the same).";
            }

            return redirect()
                ->route('influencer-testing.show', $influencer)
                ->with('success', $message);
        } catch (\Exception $e) {
            return redirect()
                ->route('influencer-testing.show', $influencer)
                ->with('error', 'Error recalculating reach: ' . $e->getMessage());
        }
    }

    /**
     * Calculate current price for specific campaign type and save it to database
     */
    public function calculatePrice(Request $request, Influencer $influencer)
    {
        $request->validate([
            'campaign_type' => 'required|in:Boost Me,Survey,Reaction Video',
        ]);

        $campaignType = $request->campaign_type;
        $pricingCalculator = new PricingCalculator();

        try {
            $result = $pricingCalculator->calculatePrice($influencer, $campaignType);

            // Save the calculated price to database
            InfluencerPrice::updateOrCreate(
                [
                    'influencer_id' => $influencer->id,
                    'campaign_type' => $campaignType
                ],
                [
                    'post_type' => $result['breakdown']['post_type'],
                    'price' => $result['price'],
                    'breakdown' => $result['breakdown'],
                    'priced_at' => now(),
                ]
            );

            \Log::info('InfluencerTestingController: Calculated and saved price', [
                'influencer_id' => $influencer->id,
                'campaign_type' => $campaignType,
                'price' => $result['price'],
                'post_type' => $result['breakdown']['post_type'],
                'saved_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'campaign_type' => $campaignType,
                'price' => $result['price'],
                'breakdown' => $result['breakdown'],
                'saved' => true, // Indicate that price was saved
                'influencer' => [
                    'id' => $influencer->id,
                    'followers' => $influencer->followers,
                    'gamification_percentage' => $influencer->gamification_percentage,
                    'ig_story_avg_reach' => $influencer->ig_story_avg_reach,
                    'ig_reel_avg_reach' => $influencer->ig_reel_avg_reach,
                    'ig_feed_avg_reach' => $influencer->ig_feed_avg_reach,
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('InfluencerTestingController: Failed to calculate/save price', [
                'influencer_id' => $influencer->id,
                'campaign_type' => $campaignType,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Update influencer basic data for testing
     */
    public function updateInfluencer(Request $request, Influencer $influencer)
    {
        $request->validate([
            'followers' => 'required|integer|min:0',
            'gamification_percentage' => 'required|numeric|min:0|max:1',
        ]);

        $influencer->update([
            'followers' => $request->followers,
            'gamification_percentage' => $request->gamification_percentage,
        ]);

        return redirect()
            ->route('influencer-testing.show', $influencer)
            ->with('success', 'Influencer data updated successfully');
    }

    /**
     * Create a test scenario with predefined data
     */
    public function createTestScenario(Request $request, Influencer $influencer)
    {
        $request->validate([
            'scenario' => 'required|in:high_performer,low_performer,mixed_performance,insufficient_data',
        ]);

        $scenario = $request->scenario;

        // Clear existing posts first
        IgPost::where('influencer_id', $influencer->id)->delete();

        switch ($scenario) {
            case 'high_performer':
                $this->createHighPerformerScenario($influencer);
                break;
            case 'low_performer':
                $this->createLowPerformerScenario($influencer);
                break;
            case 'mixed_performance':
                $this->createMixedPerformanceScenario($influencer);
                break;
            case 'insufficient_data':
                $this->createInsufficientDataScenario($influencer);
                break;
        }

        // Recalculate reach
        $job = new ComputeAverageReachJob($influencer->id);
        $job->handle();

        return redirect()
            ->route('influencer-testing.show', $influencer)
            ->with('success', "Created '{$scenario}' test scenario");
    }

    private function createHighPerformerScenario(Influencer $influencer)
    {
        $estimatedStoryReach = $influencer->followers * 0.05; // 5% estimated
        $estimatedReelReach = $influencer->followers * 0.10; // 10% estimated
        $estimatedFeedReach = $influencer->followers * 0.08; // 8% estimated

        // Create stories with 150% of estimated reach
        for ($i = 0; $i < 20; $i++) {
            IgPost::create([
                'influencer_id' => $influencer->id,
                'post_type' => 'story',
                'reach' => rand((int)($estimatedStoryReach * 1.3), (int)($estimatedStoryReach * 1.7)),
                'posted_at' => Carbon::now()->subDays(rand(1, 60)),
            ]);
        }

        // Create reels with 150% of estimated reach
        for ($i = 0; $i < 10; $i++) {
            IgPost::create([
                'influencer_id' => $influencer->id,
                'post_type' => 'reel',
                'reach' => rand((int)($estimatedReelReach * 1.3), (int)($estimatedReelReach * 1.7)),
                'posted_at' => Carbon::now()->subDays(rand(1, 60)),
            ]);
        }

        // Create feed posts with 150% of estimated reach
        for ($i = 0; $i < 8; $i++) {
            IgPost::create([
                'influencer_id' => $influencer->id,
                'post_type' => PostType::FEED_POST, // Instagram feed post (appears in main feed)
                'reach' => rand((int)($estimatedFeedReach * 1.3), (int)($estimatedFeedReach * 1.7)),
                'posted_at' => Carbon::now()->subDays(rand(1, 60)),
            ]);
        }
    }

    private function createLowPerformerScenario(Influencer $influencer)
    {
        $estimatedStoryReach = $influencer->followers * 0.05;
        $estimatedReelReach = $influencer->followers * 0.10;
        $estimatedFeedReach = $influencer->followers * 0.08;

        // Create stories with 50% of estimated reach
        for ($i = 0; $i < 20; $i++) {
            IgPost::create([
                'influencer_id' => $influencer->id,
                'post_type' => 'story',
                'reach' => rand((int)($estimatedStoryReach * 0.3), (int)($estimatedStoryReach * 0.7)),
                'posted_at' => Carbon::now()->subDays(rand(1, 60)),
            ]);
        }

        // Create reels with 50% of estimated reach
        for ($i = 0; $i < 10; $i++) {
            IgPost::create([
                'influencer_id' => $influencer->id,
                'post_type' => 'reel',
                'reach' => rand((int)($estimatedReelReach * 0.3), (int)($estimatedReelReach * 0.7)),
                'posted_at' => Carbon::now()->subDays(rand(1, 60)),
            ]);
        }

        // Create feed posts with 50% of estimated reach
        for ($i = 0; $i < 8; $i++) {
            IgPost::create([
                'influencer_id' => $influencer->id,
                'post_type' => PostType::FEED_POST, // Instagram feed post (appears in main feed)
                'reach' => rand((int)($estimatedFeedReach * 0.3), (int)($estimatedFeedReach * 0.7)),
                'posted_at' => Carbon::now()->subDays(rand(1, 60)),
            ]);
        }
    }

    private function createMixedPerformanceScenario(Influencer $influencer)
    {
        $estimatedStoryReach = $influencer->followers * 0.05;
        $estimatedReelReach = $influencer->followers * 0.10;
        $estimatedFeedReach = $influencer->followers * 0.08;

        // Create stories with mixed performance
        for ($i = 0; $i < 20; $i++) {
            $multiplier = ($i % 2 === 0) ? rand(130, 170) / 100 : rand(30, 70) / 100;
            IgPost::create([
                'influencer_id' => $influencer->id,
                'post_type' => 'story',
                'reach' => (int)($estimatedStoryReach * $multiplier),
                'posted_at' => Carbon::now()->subDays(rand(1, 60)),
            ]);
        }

        // Create reels with mixed performance
        for ($i = 0; $i < 10; $i++) {
            $multiplier = ($i % 2 === 0) ? rand(130, 170) / 100 : rand(30, 70) / 100;
            IgPost::create([
                'influencer_id' => $influencer->id,
                'post_type' => 'reel',
                'reach' => (int)($estimatedReelReach * $multiplier),
                'posted_at' => Carbon::now()->subDays(rand(1, 60)),
            ]);
        }

        // Create feed posts with mixed performance
        for ($i = 0; $i < 8; $i++) {
            $multiplier = ($i % 2 === 0) ? rand(130, 170) / 100 : rand(30, 70) / 100;
            IgPost::create([
                'influencer_id' => $influencer->id,
                'post_type' => PostType::FEED_POST, // Instagram feed post (appears in main feed)
                'reach' => (int)($estimatedFeedReach * $multiplier),
                'posted_at' => Carbon::now()->subDays(rand(1, 60)),
            ]);
        }
    }

    private function createInsufficientDataScenario(Influencer $influencer)
    {
        // Create only 5 stories (insufficient - needs 15)
        for ($i = 0; $i < 5; $i++) {
            IgPost::create([
                'influencer_id' => $influencer->id,
                'post_type' => 'story',
                'reach' => rand(500, 1500),
                'posted_at' => Carbon::now()->subDays(rand(1, 60)),
            ]);
        }

        // Create only 2 reels (insufficient - needs 5)
        for ($i = 0; $i < 2; $i++) {
            IgPost::create([
                'influencer_id' => $influencer->id,
                'post_type' => 'reel',
                'reach' => rand(1000, 3000),
                'posted_at' => Carbon::now()->subDays(rand(1, 60)),
            ]);
        }
    }
}
