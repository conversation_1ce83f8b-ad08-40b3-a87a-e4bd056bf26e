<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Influencer;
use Illuminate\Http\RedirectResponse;

class AdminInfluencerController extends Controller
{
    public function toggleOverride(Influencer $influencer): RedirectResponse
    {
        $influencer->admin_override_show_hidden = !$influencer->admin_override_show_hidden;
        $influencer->save();
        return redirect()->back();
    }
}
