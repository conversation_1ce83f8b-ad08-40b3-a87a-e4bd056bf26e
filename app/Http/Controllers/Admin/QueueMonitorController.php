<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Jobs\RepriceAllJob;
use romanzipp\QueueMonitor\Models\Monitor;
use romanzipp\QueueMonitor\Enums\MonitorStatus;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class QueueMonitorController extends Controller
{
    public function __construct()
    {
        // Admin access is handled by route middleware
    }

    public function index(Request $request)
    {
        // Check if queue monitor has any data
        $hasMonitorData = Monitor::count() > 0;

        if ($hasMonitorData) {
            $query = Monitor::query()
                ->orderBy('id', 'desc');

            // Filter by status
            if ($request->filled('status')) {
                $query->where('status', (int) $request->status);
            }

            // Filter by job name
            if ($request->filled('job')) {
                $query->where('name', 'like', '%' . $request->job . '%');
            }

            // Filter by date range
            if ($request->filled('date_from')) {
                $query->where('started_at', '>=', $request->date_from);
            }
            if ($request->filled('date_to')) {
                $query->where('started_at', '<=', $request->date_to . ' 23:59:59');
            }

            $monitors = $query->paginate(25);
            $stats = $this->getStatistics();
        } else {
            // No monitor data available, show empty state
            $monitors = collect();
            $stats = [
                'total' => 0,
                'running' => 0,
                'succeeded' => 0,
                'failed' => 0,
                'success_rate' => 0,
                'recent_total' => 0,
                'recent_succeeded' => 0,
                'recent_failed' => 0,
                'recent_success_rate' => 0,
                'avg_execution_time' => 0,
            ];
        }

        return view('admin.queue-monitor.index', compact('monitors', 'stats', 'hasMonitorData'));
    }

    public function show(Monitor $monitor)
    {
        return view('admin.queue-monitor.show', compact('monitor'));
    }

    public function dashboard()
    {
        $hasMonitorData = Monitor::count() > 0;

        if ($hasMonitorData) {
            $stats = $this->getStatistics();
            $recentJobs = Monitor::orderBy('id', 'desc')->limit(10)->get();
            $chartData = $this->getChartData();
        } else {
            $stats = [
                'total' => 0,
                'running' => 0,
                'succeeded' => 0,
                'failed' => 0,
                'success_rate' => 0,
                'recent_total' => 0,
                'recent_succeeded' => 0,
                'recent_failed' => 0,
                'recent_success_rate' => 0,
                'avg_execution_time' => 0,
            ];
            $recentJobs = collect();
            $chartData = [
                'labels' => [],
                'total' => [],
                'succeeded' => [],
                'failed' => [],
            ];
        }

        return view('admin.queue-monitor.dashboard', compact('stats', 'recentJobs', 'chartData', 'hasMonitorData'));
    }

    /**
     * Get human-readable status label
     */
    public static function getStatusLabel($status)
    {
        switch ($status) {
            case MonitorStatus::RUNNING:
                return 'Running';
            case MonitorStatus::SUCCEEDED:
                return 'Succeeded';
            case MonitorStatus::FAILED:
                return 'Failed';
            case MonitorStatus::STALE:
                return 'Stale';
            case MonitorStatus::QUEUED:
                return 'Queued';
            default:
                return 'Unknown';
        }
    }

    public function startRepricing()
    {
        try {
            RepriceAllJob::dispatch();
            
            return response()->json([
                'success' => true,
                'message' => 'Repricing job has been queued successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to queue repricing job: ' . $e->getMessage()
            ], 500);
        }
    }

    public function retry(Monitor $monitor)
    {
        if ($monitor->status !== MonitorStatus::FAILED) {
            return back()->with('error', 'Only failed jobs can be retried');
        }

        try {
            // For RepriceAllJob, just dispatch a new instance
            if (str_contains($monitor->name, 'RepriceAllJob')) {
                RepriceAllJob::dispatch();
                return back()->with('success', 'Repricing job has been re-queued successfully');
            }

            // For other jobs, you might need more specific logic
            return back()->with('error', 'Retry not implemented for this job type');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to retry job: ' . $e->getMessage());
        }
    }

    public function delete(Monitor $monitor)
    {
        try {
            $monitor->delete();
            return back()->with('success', 'Job record deleted successfully');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to delete job record: ' . $e->getMessage());
        }
    }

    public function clearCompleted()
    {
        try {
            $count = Monitor::where('status', 'succeeded')
                ->where('started_at', '<', Carbon::now()->subDays(7))
                ->delete();
            
            return back()->with('success', "Cleared {$count} completed job records older than 7 days");
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to clear completed jobs: ' . $e->getMessage());
        }
    }

    private function getStatistics()
    {
        $total = Monitor::count();
        // Include both RUNNING and QUEUED jobs as "running" from user perspective
        $running = Monitor::whereIn('status', [MonitorStatus::RUNNING, MonitorStatus::QUEUED])->count();
        $succeeded = Monitor::where('status', MonitorStatus::SUCCEEDED)->count();
        $failed = Monitor::where('status', MonitorStatus::FAILED)->count();
        
        // Recent stats (last 24 hours)
        $recent = Monitor::where('started_at', '>=', Carbon::now()->subDay());
        $recentTotal = $recent->count();
        $recentSucceeded = $recent->where('status', MonitorStatus::SUCCEEDED)->count();
        $recentFailed = $recent->where('status', MonitorStatus::FAILED)->count();

        // Average execution time for succeeded jobs
        $avgExecutionTime = Monitor::where('status', MonitorStatus::SUCCEEDED)
            ->whereNotNull('finished_at')
            ->whereNotNull('started_at')
            ->selectRaw('AVG(TIMESTAMPDIFF(SECOND, started_at, finished_at)) as avg_time')
            ->value('avg_time');

        return [
            'total' => $total,
            'running' => $running,
            'succeeded' => $succeeded,
            'failed' => $failed,
            'success_rate' => $total > 0 ? round(($succeeded / $total) * 100, 1) : 0,
            'recent_total' => $recentTotal,
            'recent_succeeded' => $recentSucceeded,
            'recent_failed' => $recentFailed,
            'recent_success_rate' => $recentTotal > 0 ? round(($recentSucceeded / $recentTotal) * 100, 1) : 0,
            'avg_execution_time' => $avgExecutionTime ? round($avgExecutionTime, 2) : 0,
        ];
    }

    private function getChartData()
    {
        // Get job counts by day for the last 7 days
        $data = Monitor::select(
                DB::raw('DATE(started_at) as date'),
                DB::raw('COUNT(*) as total'),
                DB::raw('SUM(CASE WHEN status = ' . MonitorStatus::SUCCEEDED . ' THEN 1 ELSE 0 END) as succeeded'),
                DB::raw('SUM(CASE WHEN status = ' . MonitorStatus::FAILED . ' THEN 1 ELSE 0 END) as failed')
            )
            ->where('started_at', '>=', Carbon::now()->subDays(7))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $labels = [];
        $totalData = [];
        $succeededData = [];
        $failedData = [];

        // Fill in missing days with zeros
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i)->format('Y-m-d');
            $labels[] = Carbon::now()->subDays($i)->format('M j');
            
            $dayData = $data->firstWhere('date', $date);
            $totalData[] = $dayData ? $dayData->total : 0;
            $succeededData[] = $dayData ? $dayData->succeeded : 0;
            $failedData[] = $dayData ? $dayData->failed : 0;
        }

        return [
            'labels' => $labels,
            'total' => $totalData,
            'succeeded' => $succeededData,
            'failed' => $failedData,
        ];
    }
}
