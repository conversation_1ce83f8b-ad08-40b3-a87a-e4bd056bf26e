<?php

use App\Jobs\UpdateInfluencerDataFromSocialJob;
use App\Models\Influencer;

class NeedsToBeRefactored {
    /**
     * Influencer submission handler for facebook post
     *
     * @param  mixed $record
     * @param  mixed $row
     * @return void
     */
    private function influencerSubmitFacebook($record, $row) {
        $type = '';
        $result = SocialKeys::first();
        $appId = $result->facebook_app_id;
        $secret = $result->facebook_app_secret;
        $redirectUri = config('app.url') . $result->facebook_callback_url;

        $url = 'https://graph.facebook.com/v18.0/' . $record->token_secret .
            '/posts?access_token=' . $record->social_id .
            '&fields=message,created_time,full_picture,from,place,' .
            'attachments{media_type,media,type,title,description,target,subattachments}';
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $data = json_decode(curl_exec($ch));
        curl_close($ch);

        if (isset($data->data)) {
            foreach ($data->data as $key => $value) {
                $url = 'https://graph.facebook.com/v18.0/' . $value->id . '?access_token=' . $record->social_id;
                $ch = curl_init($url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                $data_story = json_decode(curl_exec($ch));
                curl_close($ch);

                if (isset($data_story->story) && $data_story->story != '') {
                    $mode = 'livestream';
                } else {
                    $mode = '';
                }

                $url = 'https://graph.facebook.com/' . $value->id .
                    '/insights?metric=page_posts_impressions,post_impressions,post_reactions_like_total&' .
                    'access_token=' . $record->social_id;
                $ch = curl_init($url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                $data = json_decode(curl_exec($ch));
                curl_close($ch);

                $view = $data->data[0]->values[0]->value ? $data->data[0]->values[0]->value : 0;
                $like = $data->data[1]->values[0]->value ? $data->data[1]->values[0]->value : 0;
                $link = $value->attachments->data[0]->media->source ?
                    $value->attachments->data[0]->media->source :
                    $value->attachments->data[0]->media->image->src;

                if ($link != '') {
                    $fileContents = file_get_contents($link);
                    $filename = $value->id . '_facebook';
                    Storage::disk('public')->put('social_pics/' . $filename . ($value->attachments->data[0]->media_type == 'photo' ? '.jpg' : '.mp4'), $fileContents);
                    $file = 'social_pics/' . $filename . ($value->attachments->data[0]->media_type == 'photo' ? '.jpg' : '.mp4');
                }
                $type = (isset($value->attachments->data[0]->media_type)) ? $value->attachments->data[0]->media_type : "";

                if ($value->attachments->data[0]->type == 'question') {
                    $mode = 'polls';
                    $file = $value->attachments->data[0]->target->url ? $value->attachments->data[0]->target->url : '';
                    $type = '';
                }

                if (
                    date('Y-m-d', strtotime($value->created_time)) >= date('Y-m-d', strtotime($row->created_at)) &&
                    date('Y-m-d', strtotime($value->created_time)) <= date('Y-m-d')
                ) {
                    $social = SocialPost::where('user_id', $record->user_id)->where('media', 'facebook')->where('post_id', $value->id)->first();
                    if (isset($social)) {
                        $social->update([
                            'influencer_request_accept_id' => null,  // ✅ No longer using string values
                            'post_category' => strtolower($mode),    // ✅ Use post_category instead
                            'campaign_deliverable' => false,         // ✅ Not a campaign deliverable
                            'text' => (isset($value->message)) ? $value->message : "",
                            'link' => (isset($file)) ? $file : "",
                            'thumbnail' => 'https://www.facebook.com/' . $value->id,
                            'type' => $type,
                            'published_at' => date('Y-m-d H:i:s', strtotime($value->created_time)),
                            'view' => $view,
                            'like' => $like,
                        ]);
                    } else {
                        SocialPost::create([
                            'influencer_request_accept_id' => null,  // ✅ No longer using string values
                            'post_category' => strtolower($mode),    // ✅ Use post_category instead
                            'campaign_deliverable' => false,         // ✅ Not a campaign deliverable
                            'user_id' => $record->user_id,
                            'media' =>  'facebook',
                            'post_id' => $value->id,
                            'text' => (isset($value->message)) ? $value->message : "",
                            'link' => (isset($file)) ? $file : "",
                            'thumbnail' => 'https://www.facebook.com/' . $value->id,
                            'type' => $type,
                            'published_at' => date('Y-m-d H:i:s', strtotime($value->created_time)),
                            'view' => $view,
                            'like' => $like,
                        ]);
                    }

                    // Dispatch job to update influencer data after processing Facebook post
                    $this->dispatchInfluencerDataUpdate($record->user_id);
                }
            }
        }

        // Dispatch final update after processing all posts
        $this->dispatchInfluencerDataUpdate($record->user_id);
    }

    /**
     * Dispatch job to update influencer data from social posts.
     *
     * @param int $userId
     * @return void
     */
    private function dispatchInfluencerDataUpdate(int $userId): void
    {
        try {
            // Find the influencer record for this user
            $influencer = Influencer::where('user_id', $userId)->first();

            if ($influencer) {
                UpdateInfluencerDataFromSocialJob::dispatch($influencer->id);

                \Log::info('Dispatched influencer data update job', [
                    'user_id' => $userId,
                    'influencer_id' => $influencer->id,
                    'trigger' => 'facebook_post_processed'
                ]);
            }
        } catch (\Exception $e) {
            \Log::error('Failed to dispatch influencer data update job', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trigger' => 'facebook_post_processed'
            ]);
        }
    }
}