<?php

class NeedsToBeRefactored {
    /**
     * Influencer submission handler for twitter post
     *
     * @param  mixed $record
     * @param  mixed $row
     * @return void
     */
    private function influencerSubmitTwitter($record, $row) {
        $type = '';
        $socialKeys = SocialKeys::first();
        $accessToken = $record->token;
        $tw_user_id = $record->social_id;
        $tw_username = $record->name;
        $redirect = config('app.url') . $socialKeys->twitter_callback_url;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.twitter.com/oauth2/token");
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt(
            $ch,
            CURLOPT_POSTFIELDS,
            http_build_query([
                'client_id' => $socialKeys->twitter_app_id,
                'client_secret' => $socialKeys->twitter_app_secret,
                'grant_type' => 'client_credentials'
            ])
        );
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        curl_close($ch);
        $content = json_decode($response);

        $url = 'https://api.twitter.com/1.1/statuses/user_timeline.json?screen_name=' . $tw_username . '&tweet_mode=extended';
        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_HTTPHEADER => array(
                "Authorization: Bearer " . $content->access_token
            ),
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_URL => $url
        ));
        $response = curl_exec($ch);
        curl_close($ch);
        $data_twitter = json_decode($response);

        foreach ($data_twitter as $data_row) {
            $social = SocialPost::where('user_id', $record->user_id)->where('media', 'twitter')->where('post_id', $data_row->id)->first();

            if (date('Y-m-d', strtotime($data_row->created_at)) >= date('Y-m-d', strtotime($row->created_at)) &&  date('Y-m-d', strtotime($data_row->created_at)) <= date('Y-m-d')) {
                $url = 'https://api.twitter.com/1.1/videos/tweet/config/' . $data_row->id . '.json';
                $ch = curl_init();
                curl_setopt_array($ch, array(
                    CURLOPT_HTTPHEADER => array(
                        "Authorization: Bearer " . $content->access_token
                    ),
                    CURLOPT_SSL_VERIFYPEER => false,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_URL => $url
                ));
                $response = curl_exec($ch);
                curl_close($ch);
                $data_twitter1 = json_decode($response);

                $views = isset($data_twitter1->track->viewCount) ? $data_twitter1->track->viewCount : '';

                $file = '';
                $type = '';
                if (isset($data_row->extended_entities->media[0]->media_url)) {
                    $link = '';
                    if ($data_row->extended_entities->media[0]->type == 'video') {
                        $type = 'video';
                        $link = $data_row->extended_entities->media[0]->video_info->variants[1]->url;
                    } elseif ($data_row->extended_entities->media[0]->type == 'photo' || $data_row->extended_entities->media[0]->type == 'animated_gif') {
                        $type = 'photo';
                        $link = $data_row->extended_entities->media[0]->media_url;
                    }
                    $fileContents = file_get_contents($link);
                    $filename = $data_row->id . '_twitter';
                    Storage::disk('public')->put('social_pics/' . $filename . ($data_row->extended_entities->media[0]->type == 'photo' ? '.jpg' : '.mp4'), $fileContents);
                    $file = 'social_pics/' . $filename . ($data_row->extended_entities->media[0]->type == 'photo' ? '.jpg' : '.mp4');
                } else {
                    $file = 'https://twitter.com/' . $record->name . '/status/' . $data_row->id;
                }

                if (isset($social)) {
                    $social->update([
                        'text' => $data_row->full_text,
                        'link' => $file,
                        'type' => $type,
                        'published_at' => date('Y-m-d H:i:s', strtotime($data_row->created_at)),
                        'thumbnail' => 'https://twitter.com/' . $record->name . '/status/' . $data_row->id,
                        'like' => $data_row->favorite_count,
                        'comment' => $data_row->retweet_count,
                        'view' => ($views != '') ? $views : $social->view,
                    ]);
                } else {
                    SocialPost::create([
                        'user_id' => $record->user_id,
                        'media' =>  'twitter',
                        'post_id' => $data_row->id,
                        'text' => $data_row->full_text,
                        'link' => $file,
                        'type' => $type,
                        'published_at' => date('Y-m-d H:i:s', strtotime($data_row->created_at)),
                        'thumbnail' => 'https://twitter.com/' . $record->name . '/status/' . $data_row->id,
                        'like' => $data_row->favorite_count,
                        'comment' => $data_row->retweet_count,
                        'view' => $views,
                    ]);
                }
            }
        }
    }
}