<?php

use App\Jobs\UpdateInfluencerDataFromSocialJob;
use App\Models\Influencer;

class NeedsToBeRefactored {
    /**
     * Influencer submission handler for tiktok post
     *
     * @param  mixed $record
     * @param  mixed $row
     * @return void
     */
    private function influencerSubmitTiktok($record, $row) {
        $type = '';
        $result =  SocialKeys::first();
        $appId = $result->tiktok_app_id;
        $secret = $result->tiktok_app_secret;

        $videosApi = "https://open.tiktokapis.com/v2/user/info/?fields=open_id,union_id,avatar_url,follower_count";
        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_HTTPHEADER => array(
                "Authorization: Bearer " . $record->token
            ),
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_URL => $videosApi
        ));

        $response = curl_exec($ch);
        curl_close($ch);
        $data_tiktok = json_decode($response);

        $videos  = "https://open.tiktokapis.com/v2/video/list/?fields=" .
            "id,title,video_description,duration,cover_image_url,share_url,embed_link," .
            "create_time,like_count,view_count,share_count,comment_count";

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt_array($ch, [
            CURLOPT_HTTPHEADER => [
                "Authorization: Bearer " . $record->token
            ],
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_URL => $videos
        ]);
        $response = curl_exec($ch);
        curl_close($ch);
        $content = json_decode($response);

        if (isset($content->data->videos)) {
            foreach ($content->data->videos as $data_row) {
                $social = SocialPost::where('user_id', $record->user_id)->where('media', 'tiktok')->where('post_id', $data_row->id)->first();

                if (
                    date('Y-m-d', $data_row->create_time) >= date('Y-m-d', strtotime($row->created_at)) &&
                    date('Y-m-d', $data_row->create_time) <= date('Y-m-d')
                ) {
                    if (isset($social)) {
                        $social->update([
                            'text' => $data_row->title,
                            'link' => (isset($data_row->share_url)) ? $data_row->share_url : "",
                            'type' => 'video',
                            'published_at' => date('Y-m-d H:i:s', $data_row->create_time),
                            'like' => $data_row->like_count,
                            'view' => $data_row->view_count,
                            'share' => $data_row->share_count,
                            'comment' => $data_row->comment_count,
                        ]);
                    } else {
                        SocialPost::create([
                            'influencer_request_accept_id' => null,  // ✅ No longer using string values
                            'post_category' => 'video',              // ✅ Use post_category instead
                            'campaign_deliverable' => false,         // ✅ Not a campaign deliverable
                            'user_id' => $record->user_id,
                            'media' =>  'tiktok',
                            'post_id' => $data_row->id,
                            'text' => $data_row->title,
                            'link' => (isset($data_row->share_url)) ? $data_row->share_url : "",
                            'type' => 'video',
                            'published_at' => date('Y-m-d H:i:s', $data_row->create_time),
                            'like' => $data_row->like_count,
                            'view' => $data_row->view_count,
                            'share' => $data_row->share_count,
                            'comment' => $data_row->comment_count,
                        ]);
                    }

                    // Dispatch job to update influencer data after processing TikTok video
                    $this->dispatchInfluencerDataUpdate($record->user_id);
                }
            }
        }

        // Dispatch final update after processing all videos
        $this->dispatchInfluencerDataUpdate($record->user_id);
    }

    /**
     * Dispatch job to update influencer data from social posts.
     *
     * @param int $userId
     * @return void
     */
    private function dispatchInfluencerDataUpdate(int $userId): void
    {
        try {
            // Find the influencer record for this user
            $influencer = Influencer::where('user_id', $userId)->first();

            if ($influencer) {
                UpdateInfluencerDataFromSocialJob::dispatch($influencer->id);

                \Log::info('Dispatched influencer data update job', [
                    'user_id' => $userId,
                    'influencer_id' => $influencer->id,
                    'trigger' => 'tiktok_video_processed'
                ]);
            }
        } catch (\Exception $e) {
            \Log::error('Failed to dispatch influencer data update job', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trigger' => 'tiktok_video_processed'
            ]);
        }
    }
}