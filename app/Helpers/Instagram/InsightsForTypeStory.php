<?php

/**
 * @see https://github.com/InnoManic-Org/ClickItFame/issues/570
 */

namespace App\Helpers\Instagram;

use App\Models\SocialPost;
use App\Models\SocialConnect;
use App\Models\InfluencerRequestDetail;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class InsightsForTypeStory
{
    protected SocialConnect $socialConnect;
    protected InfluencerRequestDetail $influencerRequestDetail;

    const FIELDS = ['id', 'caption', 'media_type', 'thumbnail_url', 'media_url', 'permalink', 'timestamp'];
    const METRICS = ['views', 'reach', 'shares', 'total_interactions'];
    // const METRICS = [
    //     'follows',
    //     // 'impressions',
    //     'navigation',
    //     'profile_activity',
    //     'profile_visits',
    //     'reach',
    //     'replies',
    //     'shares',
    //     'total_interactions',
    //     'views'
    // ];

    public function __construct(SocialConnect $socialConnect, InfluencerRequestDetail $influencerRequestDetail)
    {
        $this->socialConnect = $socialConnect;
        $this->influencerRequestDetail = $influencerRequestDetail;
    }

    public function importSocialMediaStory(): void
    {
        $instagramUserId = $this->socialConnect->token_secret;
        $accessToken = $this->socialConnect->token;

        // Add time restrictions to fetch stories from campaign creation date onwards
        $since = Carbon::parse($this->influencerRequestDetail->created_at)->timestamp; // Campaign start
        $until = Carbon::now()->timestamp; // Now

        Log::info('Instagram API: Fetching stories with dynamic time range', [
            'campaign_id' => $this->influencerRequestDetail->compaign_id,
            'user_id' => $this->socialConnect->user_id,
            'since_timestamp' => $since,
            'until_timestamp' => $until,
            'since_date' => Carbon::createFromTimestamp($since)->toDateTimeString(),
            'until_date' => Carbon::createFromTimestamp($until)->toDateTimeString(),
            'campaign_created_at' => $this->influencerRequestDetail->created_at
        ]);

        $storiesUrl = 'https://graph.facebook.com/v18.0/' . $instagramUserId . '/stories?fields=' .
            implode(',', self::FIELDS) . '&since=' . $since . '&until=' . $until . '&access_token=' . $accessToken;

        try {
            $response = Http::get($storiesUrl);

            if (!$response->successful()) {
                Log::error('Failed to fetch Instagram stories', [
                    'user_id' => $this->socialConnect->user_id,
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                return;
            }

            $storiesData = $response->json();
        } catch (\Exception $e) {
            Log::error('Exception while fetching Instagram stories', [
                'user_id' => $this->socialConnect->user_id,
                'error' => $e->getMessage()
            ]);
            return;
        }

        if (isset($storiesData['data'])) {
            foreach ($storiesData['data'] as $storyItem) {
                $this->processStoryItem($storyItem);
            }
        }
    }

    private function processStoryItem(array $storyItem): void
    {
        if (empty($storyItem['id'])) {
            Log::error('Instagram story missing ID during processing', [
                'user_id' => $this->socialConnect->user_id,
                'influencer_request_id' => $this->influencerRequestDetail->id,
                'story_data' => $storyItem,
                'permalink' => $storyItem['permalink'] ?? 'N/A',
                'media_type' => $storyItem['media_type'] ?? 'N/A',
                'timestamp' => $storyItem['timestamp'] ?? 'N/A'
            ]);
            return;
        }
        // Define unique fields for updateOrCreate
        $uniqueFields = [
            'user_id' => $this->socialConnect->user_id,
            'media' => 'instagram',
            'post_id' => $storyItem['id']
        ];

        $insightsUrl = 'https://graph.facebook.com/v18.0/' . $storyItem['id'] . '/insights?metric=' .
            implode(',', self::METRICS) . '&access_token=' . $this->socialConnect->token;

        try {
            $insightsResponse = Http::get($insightsUrl);

            if (!$insightsResponse->successful()) {
                Log::warning('Not enough activities, no Instagram story insights will be saved', [
                    'story_id' => $storyItem['id'],
                    'user_id' => $this->socialConnect->user_id,
                    'status' => $insightsResponse->status(),
                    'response' => $insightsResponse->json(),
                    'insightUrl' => $insightsUrl
                ]);
                $insightsData = [];
            } else {
                $insightsData = $insightsResponse->json();
            }
        } catch (\Exception $e) {
            Log::error('Exception while fetching Instagram story insights', [
                'story_id' => $storyItem['id'],
                'user_id' => $this->socialConnect->user_id,
                'error' => $e->getMessage()
            ]);
            $insightsData = [];
        }

        $preparedMetricsData = [];

        if (isset($insightsData['data'])) {
            $preparedMetricsData['complete__' . date('Y_m_d_H_i_s')] = $insightsData;

            foreach ($insightsData['data'] as $insightItem) {
                $metricName = $insightItem['name'] ?? '';
                $metricValue = $insightItem['values'][0]['value'] ?? 0;
                $preparedMetricsData[$metricName] = $metricValue;
            }
        }

        $storyTimestamp = Carbon::parse($storyItem['timestamp']);
        $requestCreatedAt = Carbon::parse($this->influencerRequestDetail->created_at);
        $now = Carbon::now(); // FIX: Use current time instead of today at midnight

        if ($storyTimestamp->greaterThanOrEqualTo($requestCreatedAt) && $storyTimestamp->lessThanOrEqualTo($now)) {
            $filepath = '';
            $contentType = 'video';

            if (isset($storyItem['media_url'])) {
                try {
                    $fileContents = file_get_contents($storyItem['media_url']);
                    $filename = $storyItem['id'] . '_instagram';

                    $fileExt = '.mp4';
                    if (
                        ($storyItem['media_type'] ?? '') == 'IMAGE' ||
                        ($storyItem['media_type'] ?? '') == 'CAROUSEL_ALBUM'
                    ) {
                        $fileExt = '.jpg';
                        $contentType = 'photo';
                    }

                    $filenameWithExt = $filename . $fileExt;
                    $filepath = 'social_pics/' . $filenameWithExt;

                    Storage::disk('public')->put($filepath, $fileContents);
                } catch (\Exception $e) {
                    Log::error('Failed to download Instagram story media file', [
                        'story_id' => $storyItem['id'],
                        'media_url' => $storyItem['media_url'] ?? 'N/A',
                        'error' => $e->getMessage()
                    ]);
                }
            }

            $permalink = !empty($storyItem['permalink'])
                ? $storyItem['permalink']
                : 'https://www.instagram.com/stories/' . $this->socialConnect->name;

            // FIX: Use updateOrCreate to handle both create and update scenarios
            $updateData = [
                'influencer_request_accept_id' => null,  // ✅ No longer using string values
                'post_category' => 'story',              // ✅ Proper categorization
                'campaign_deliverable' => false,         // ✅ Not a campaign deliverable
                'text' => $storyItem['caption'] ?? null,
                'link' => $filepath,
                'type' => $contentType,
                'published_at' => $storyTimestamp->format('Y-m-d H:i:s'),
                'thumbnail' => $permalink,
                // Store complete insights data from Instagram API for flexible future use
                'insights' => $preparedMetricsData
            ];

            // Safety measure: Check if we should preserve existing insights data
            $hasNewInsights = !empty($preparedMetricsData);
            $existingRecord = SocialPost::where($uniqueFields)->first();

            if (!$hasNewInsights && $existingRecord && $this->hasExistingInsights($existingRecord)) {
                // Remove insights from update data to preserve existing insights
                unset($updateData['insights']);

                Log::info('Preserving existing insights data - skipping insights update', [
                    'story_id' => $storyItem['id'],
                    'user_id' => $this->socialConnect->user_id,
                    'existing_insights_count' => is_array($existingRecord->insights) ? count($existingRecord->insights) : 0,
                    'new_insights_count' => count($preparedMetricsData)
                ]);
            }

            // Use updateOrCreate for proper upsert behavior
            $socialPost = SocialPost::updateOrCreate($uniqueFields, $updateData);

            Log::info('Instagram story processed successfully', [
                'campaign_id' => $this->influencerRequestDetail->compaign_id,
                'user_id' => $this->socialConnect->user_id,
                'story_id' => $storyItem['id'],
                'action' => $socialPost->wasRecentlyCreated ? 'created' : 'updated',
                'insights_count' => count($preparedMetricsData)
            ]);
        }
    }

    /**
     * Check if the existing record has meaningful insights data that should be preserved
     *
     * @param SocialPost $record
     * @return bool
     */
    private function hasExistingInsights(SocialPost $record): bool
    {
        // Check if insights column is null
        if (is_null($record->insights)) {
            return false;
        }

        // Check if insights is an empty array (either [] or "[]")
        if (is_array($record->insights) && empty($record->insights)) {
            return false;
        }

        // Check if insights is a JSON string representing an empty array
        if (is_string($record->insights)) {
            $decoded = json_decode($record->insights, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded) && empty($decoded)) {
                return false;
            }
        }

        // If we reach here, the record has meaningful insights data
        return true;
    }
}
