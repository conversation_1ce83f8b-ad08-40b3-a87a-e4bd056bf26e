<?php

namespace App\Models;

use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class InfluencerRequestDetail extends Model
{
    use Notifiable;

    // Define constants for payment statuses
    const STATUS_NEW = 'new';
    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_REFUNDED = 'refunded';
    const STATUS_CASHED_OUT = 'cashed_out';

    protected $fillable = [
        'user_id',
        'insight',
        'influencer_detail_id',
        'request_id',
        'categories',
        'advertising',
        'media',
        'name',
        'link',
        'publish',
        'short_product',
        'hashtags',
        'mentions',
        'social',
        'subcategory',
        'time',
        'support',
        'site',
        'current_price',
        'discount_price',
        'vat_included',
        'payment_status',
        'cash_out_amount',
        'platform_amount',
        'influencer_amount',
        'completed_at',
        'is_cashed_out',
        'cashed_out_at',
        'has_stripe_issue',
        'response',
        'payout_batch',
        'status',
        'invoice_id',
        'total_amount',
        'social_post_id',
        'compaign_id',
        'compaign_title',
        'influencer_price',
        'review',
        'read_status',
        'read_at',
        'type',
        'refund_txn_id',
        'refund_reason',
        'refund_payment_date',
        'finish',
        'is_complained',
        'is_paused',
        'file',
        'task',
        'post_type',
        'post_content_type',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'phase_duration',
        'campaign_id',
        'campaign_title',
        'is_reviewable',
        'is_cancelled',
        'is_onhold',
        'is_completed',
        'is_waiting'
    ];

    /**
     * Get the phase duration (alias for the time column).
     */
    protected function phaseDuration(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->time,
        );
    }

    /**
     * Determine if the influencer request detail is reviewable.
     */
    protected function isReviewable(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->review != 1 &&
                         $this->is_complained != 1 &&
                         $this->invoice_id != null &&
                         $this->refund_reason == null &&
                         isset($this->social_posts) &&
                         $this->social_posts != null,
        );
    }

    /**
     * Determine if the influencer request detail is cancelled.
     */
    protected function isCancelled(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->refund_reason == 'Cancelled' ||
                         $this->refund_reason == 'Cancelled By Influencer' ||
                         $this->refund_reason == 'Cancelled By Customer' ||
                         $this->refund_reason == 'Cancelled By Admin' ||
                         $this->refund_reason == 'Complaint Confirmed' ||
                         $this->refund_reason == 'Refunded On Time Expired' ||
                         $this->refund_reason == 'Refunded On Submit Timeout' ||
                         $this->refund_reason == 'Refunded On Dispute' ||
                         (isset($this->influencer_request_accepts->complaints) &&
                          $this->influencer_request_accepts->complaints->status == 'Confirmed'),
        );
    }

    /**
     * Determine if the influencer request detail is on hold.
     */
    protected function isOnhold(): Attribute
    {
        return Attribute::make(
            get: fn () => ($this->is_complained == 1 &&
                $this->refund_reason != 'Complaint Confirmed' &&
                $this->refund_reason != 'Cancelled' &&
                $this->review != '1' &&
                (isset($this->influencer_request_accepts->complaints) &&
                $this->influencer_request_accepts->complaints->status != 'Cancelled'))
        );
    }

    /**
     * Determine if the influencer request detail is completed.
     */
    protected function isCompleted(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->finish == 1 ||
                $this->review == 1 ||
                (isset($this->influencer_request_accepts->complaints) &&
                $this->influencer_request_accepts->complaints->status == 'Cancelled')
        );
    }

    /**
     * Determine if the influencer request detail is waiting.
     */
    protected function isWaiting(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->updated_at->diffInDays(now()) < 10 &&
                         $this->refund_reason != 'Cancelled' &&
                         !isset($this->social_posts) &&
                         $this->review != '1',
        );
    }

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function influencerdetails()
    {
        return $this->hasOne(InfluencerDetail::class, 'id', 'influencer_detail_id');
    }

    public function social_posts()
    {
        return $this->hasOne(SocialPost::class, 'id', 'social_post_id');
    }

    public function influencer_request_accepts()
    {
        return $this->hasOne(InfluencerRequestAccept::class, 'influencer_request_detail_id', 'id');
    }

    public function category()
    {
        return $this->belongsTo(Category::class, 'categories');
    }

    public function invoices()
    {
        return $this->hasOne(Invoice::class, 'id', 'invoice_id');
    }

    public function getCampaignIdAttribute()
    {
        return $this->compaign_id;
    }

    public function getCampaignTitleAttribute()
    {
        return $this->compaign_title;
    }

    /**
     * Cancel a campaign with comprehensive handling of refunds, notifications, and status updates.
     *
     * This method provides centralized campaign cancellation logic that handles:
     * - Stripe refund processing for separate charges and transfer system
     * - Database status updates across related models
     * - Price adjustments for influencer profiles
     * - Campaign pause removal
     * - Notification dispatching
     * - Comprehensive error logging
     *
     * @param string $reason The cancellation reason. Supported values:
     *   - 'time_expired': Campaign expired due to time limits
     *   - 'submit_timeout': Influencer failed to submit content on time
     *   - 'cancelled_by_customer': Customer cancelled the campaign
     *   - 'cancelled_by_admin': Admin cancelled the campaign
     *   - 'dispute_confirmed': Dispute was confirmed against the campaign
     *   - 'cancelled_by_influencer': Influencer cancelled the campaign
     *   - 'payment_failed': Payment processing failed
     *   - 'content_rejected': Submitted content was rejected
     *
     * @param array $options Configuration options for cancellation behavior:
     *   - 'process_refund' (bool, default: true): Whether to process Stripe refund
     *   - 'adjust_price' (bool, default: true): Whether to adjust influencer's current_price
     *   - 'remove_pause' (bool, default: true): Whether to remove campaign pause status
     *   - 'send_notifications' (bool, default: true): Whether to send cancellation notifications
     *   - 'metadata' (array, default: []): Additional metadata to include in Stripe refund and logs
     *     Example: ['admin_user_id' => 123, 'dispute_id' => 'dp_xxx', 'automated' => true]
     *
     * @return array Result array containing:
     *   - 'success' (bool): Whether the cancellation was successful
     *   - 'message' (string): Success or error message
     *   - 'refund_processed' (bool): Whether Stripe refund was processed
     *   - 'refund_id' (string|null): Stripe refund ID if refund was processed
     *   - 'price_adjusted' (bool): Whether influencer price was adjusted
     *   - 'price_adjustment_amount' (float|null): Amount deducted from influencer price
     *   - 'notifications_sent' (bool): Whether notifications were dispatched
     *   - 'error_code' (string|null): Error code if cancellation failed
     *   - 'error_details' (array|null): Detailed error information for debugging
     *
     * @throws \Exception If database transaction fails or critical error occurs
     *
     * @example
     * // Basic cancellation with default options
     * $result = $detail->cancelCampaign('cancelled_by_customer');
     *
     * @example
     * // Advanced cancellation with custom options
     * $result = $detail->cancelCampaign('dispute_confirmed', [
     *     'process_refund' => true,
     *     'adjust_price' => true,
     *     'remove_pause' => false,
     *     'send_notifications' => false,
     *     'metadata' => [
     *         'dispute_id' => 'dp_1234567890',
     *         'admin_user_id' => 42,
     *         'automated' => false
     *     ]
     * ]);
     *
     * if ($result['success']) {
     *     Log::info('Campaign cancelled successfully', [
     *         'refund_id' => $result['refund_id'],
     *         'amount_adjusted' => $result['price_adjustment_amount']
     *     ]);
     * } else {
     *     Log::error('Campaign cancellation failed', [
     *         'error_code' => $result['error_code'],
     *         'error_message' => $result['message']
     *     ]);
     * }
     */
    public function cancelCampaign(string $reason, array $options = []): array
    {
        // Define valid cancellation reasons
        $validReasons = [
            'cancelled_by_influencer' => 'Cancelled By Influencer',
            'cancelled_by_customer' => 'Cancelled By Customer',
            'cancelled_by_admin' => 'Cancelled By Admin',
            'time_expired' => 'Refunded On Time Expired',
            'submit_timeout' => 'Refunded On Submit Timeout',
            'dispute_confirmed' => 'Refunded On Dispute',
            'complaint_confirmed' => 'Complaint Confirmed',
            'general_cancellation' => 'Cancelled'
        ];

        // Validate reason
        if (!array_key_exists($reason, $validReasons)) {
            return [
                'success' => false,
                'message' => 'Invalid cancellation reason provided',
                'error_code' => 'INVALID_REASON'
            ];
        }

        // Check if already cancelled
        if ($this->refund_reason !== null) {
            return [
                'success' => true,
                'message' => 'Campaign is already cancelled',
                'error_code' => 'ALREADY_CANCELLED',
                'current_reason' => $this->refund_reason
            ];
        }

        // Extract options with defaults
        $processRefund = $options['process_refund'] ?? true;
        $adjustPrice = $options['adjust_price'] ?? true;
        $removePause = $options['remove_pause'] ?? true;
        $sendNotifications = $options['send_notifications'] ?? true;
        $priceAdjustmentAmount = $options['price_adjustment_amount'] ?? null;
        $additionalMetadata = $options['metadata'] ?? [];

        $result = [
            'success' => true,
            'message' => 'Campaign cancelled successfully',
            'refund_processed' => false,
            'price_adjusted' => false,
            'pause_removed' => false,
            'notifications_sent' => false
        ];

        try {
            \DB::beginTransaction();

            // Determine refund reason text
            $refundReasonText = $validReasons[$reason];

            // Calculate price adjustment amount if not provided
            if ($adjustPrice && $priceAdjustmentAmount === null) {
                $priceAdjustmentAmount = $this->calculatePriceAdjustment();
            }

            // Process Stripe refund if requested and conditions are met
            if ($processRefund && $this->shouldProcessRefund()) {
                $refundResult = $this->processStripeRefund($refundReasonText, $additionalMetadata);

                if ($refundResult['success']) {
                    // Update with refund status
                    $this->update([
                        'refund_reason' => $refundReasonText,
                        'payment_status' => self::STATUS_REFUNDED,
                        'refund_payment_date' => now()
                    ]);

                    // Update invoice
                    if ($this->invoices) {
                        $this->invoices->update([
                            'is_refunded' => 1,
                            'payment_status' => 'Refunded',
                            'description' => "Influencer Payment Refunded - {$refundReasonText}"
                        ]);
                    }

                    $result['refund_processed'] = true;
                    $result['refund_id'] = $refundResult['refund_id'] ?? null;
                } else {
                    // Refund failed, but still mark as cancelled
                    $this->update([
                        'refund_reason' => $refundReasonText,
                        'payment_status' => self::STATUS_CANCELLED,
                        'refund_payment_date' => now()
                    ]);

                    $result['refund_error'] = $refundResult['error'] ?? 'Unknown refund error';
                }
            } else {
                // No refund processing - mark as cancelled
                $this->update([
                    'refund_reason' => $refundReasonText,
                    'payment_status' => self::STATUS_CANCELLED,
                    'refund_payment_date' => now()
                ]);
            }

            // Adjust price if requested
            if ($adjustPrice && $priceAdjustmentAmount > 0) {
                $this->current_price = max(0, $this->current_price - $priceAdjustmentAmount);
                $this->save();
                $result['price_adjusted'] = true;
                $result['price_adjustment_amount'] = $priceAdjustmentAmount;
            }

            // Remove pause status if requested
            if ($removePause && $this->is_paused == 1) {
                $this->is_paused = 0;
                $this->save();
                $result['pause_removed'] = true;
            }

            \DB::commit();

            // Send notifications after successful database commit
            if ($sendNotifications) {
                $this->sendCancellationNotifications();
                $result['notifications_sent'] = true;
            }

            // Log successful cancellation
            \Log::info('Campaign cancelled successfully via cancelCampaign method', [
                'influencer_request_detail_id' => $this->id,
                'campaign_id' => $this->compaign_id,
                'reason' => $reason,
                'refund_reason_text' => $refundReasonText,
                'refund_processed' => $result['refund_processed'],
                'price_adjusted' => $result['price_adjusted'],
                'options' => $options
            ]);

        } catch (\Exception $e) {
            \DB::rollBack();

            \Log::error('Failed to cancel campaign via cancelCampaign method', [
                'influencer_request_detail_id' => $this->id,
                'campaign_id' => $this->compaign_id,
                'reason' => $reason,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to cancel campaign due to system error',
                'error_code' => 'SYSTEM_ERROR',
                'error_details' => $e->getMessage()
            ];
        }

        return $result;
    }

    /**
     * Check if Stripe refund should be processed
     *
     * @return bool
     */
    private function shouldProcessRefund(): bool
    {
        return $this->invoices &&
               $this->invoices->is_refunded == 0 &&
               !empty($this->refund_txn_id);
    }

    /**
     * Calculate price adjustment amount based on advertising type
     *
     * @return float
     */
    private function calculatePriceAdjustment(): float
    {
        if (!$this->influencerdetails || !$this->influencerdetails->user) {
            return 0;
        }

        $user = $this->influencerdetails->user;
        if (!$user->advertisingMethodPrice) {
            return 0;
        }

        $fieldName = $this->advertising . '_price';
        return $user->advertisingMethodPrice->$fieldName ?? 0;
    }

    /**
     * Process Stripe refund for the campaign
     *
     * @param string $refundReason
     * @param array $additionalMetadata
     * @return array
     */
    private function processStripeRefund(string $refundReason, array $additionalMetadata = []): array
    {
        try {
            \Stripe\Stripe::setApiKey(config('settings.env.STRIPE_SECRET'));

            // Debug logging
            \Log::info('Starting Stripe refund process', [
                'influencer_request_detail_id' => $this->id,
                'refund_txn_id' => $this->refund_txn_id,
                'refund_reason' => $refundReason,
                'invoice_id' => $this->invoice_id
            ]);

            // Check if refund_txn_id contains a transfer ID (new architecture) or charge ID (old architecture)
            $isTransferId = strpos($this->refund_txn_id, 'tr_') === 0;

            \Log::info('Refund architecture detected', [
                'is_transfer_id' => $isTransferId,
                'refund_txn_id' => $this->refund_txn_id
            ]);

            if ($isTransferId) {
                // NEW ARCHITECTURE: Single Payment + Transfers
                // Reverse the specific transfer for this influencer
                // Sample transfer object:
                // Stripe\Transfer Object
                // (
                //     [id] => tr_3Rs5pJFv5DX420hy0rtuStlZ
                //     [object] => transfer
                //     [amount] => 47600
                //     [amount_reversed] => 0
                //     [balance_transaction] => txn_3Rs5pJFv5DX420hy0Dk4xL0v
                //     [created] => 1754242573
                //     [currency] => eur
                //     [description] => Transfer to Moshfiqur Rahman for Campaign C-00000303
                //     [destination] => acct_1R0PcDCQU9TzuCcq
                //     [destination_payment] => py_1Rs5pRCQU9TzuCcqfh1B1at7
                //     [livemode] => 
                //     [metadata] => Stripe\StripeObject Object
                //         (
                //             [architecture] => single_payment_transfers
                //             [campaign_id] => C-00000303
                //             [influencer_id] => 568
                //             [influencer_user_id] => 105
                //             [original_charge_id] => ch_3Rs5pJFv5DX420hy0d7qXnIP
                //             [type] => influencer_transfer
                //         )

                //     [reversals] => Stripe\Collection Object
                //         (
                //             [object] => list
                //             [data] => Array
                //                 (
                //                 )

                //             [has_more] => 
                //             [total_count] => 0
                //             [url] => /v1/transfers/tr_3Rs5pJFv5DX420hy0rtuStlZ/reversals
                //         )

                //     [reversed] => 
                //     [source_transaction] => ch_3Rs5pJFv5DX420hy0d7qXnIP
                //     [source_type] => card
                //     [transfer_group] => campaign_C-00000303
                // )
                $transfer = \Stripe\Transfer::retrieve($this->refund_txn_id);

                // Prepare metadata
                $metadata = array_merge([
                    'refund_reason' => $refundReason,
                    'influencer_request_detail_id' => $this->id,
                    'campaign_id' => $this->compaign_id,
                    'refund_method' => 'cancelCampaign_transfer_reversal',
                    'architecture' => 'single_payment_transfers'
                ], $additionalMetadata);

                // Reverse the transfer (refund this influencer only)
                // This returns a Stripe\TransferReversal object, here is a sample
                // Stripe\TransferReversal Object
                // (
                //     [id] => trr_1Rs5wEFv5DX420hyiAP7TpXg
                //     [object] => transfer_reversal
                //     [amount] => 47600
                //     [balance_transaction] => txn_1Rs5wEFv5DX420hyS5A40MrL
                //     [created] => 1754242994
                //     [currency] => eur
                //     [destination_payment_refund] => pyr_1Rs5wDCQU9TzuCcqFj2SawjI
                //     [metadata] => Stripe\StripeObject Object
                //         (
                //             [architecture] => single_payment_transfers
                //             [campaign_id] => C-00000303
                //             [controller_method] => cancelCampaignByInfluencer
                //             [influencer_request_detail_id] => 568
                //             [refund_method] => cancelCampaign_transfer_reversal
                //             [refund_reason] => Cancelled By Influencer
                //             [request_id] => 568
                //             [user_initiated] => true
                //         )

                //     [source_refund] => 
                //     [transfer] => tr_3Rs5pJFv5DX420hy0rtuStlZ
                // )
                $result = $transfer->reversals->create([
                    'metadata' => $metadata
                ]);

                if ($result && $result->id) {
                    // Transfer reversal succeeded, now refund the customer
                    $originalChargeId = $this->invoices->charge_id ?? null;

                    // Debug logging
                    \Log::info('Transfer reversal succeeded', [
                        'transfer_reversal_id' => $result->id,
                        'amount' => $result->amount,
                        'original_charge_id' => $originalChargeId,
                        'influencer_request_detail_id' => $this->id
                    ]);

                    if (!$originalChargeId) {
                        \Log::error('Original charge ID not found', [
                            'influencer_request_detail_id' => $this->id,
                            'invoice_id' => $this->invoice_id,
                            'invoice_exists' => $this->invoices ? 'yes' : 'no'
                        ]);
                        return [
                            'success' => false,
                            'error' => 'Original charge ID not found in invoice. Cannot refund customer.',
                            'debug_info' => [
                                'invoice_id' => $this->invoice_id,
                                'invoice_exists' => $this->invoices ? 'yes' : 'no'
                            ]
                        ];
                    }

                    try {
                        \Log::info('Attempting customer refund', [
                            'charge_id' => $originalChargeId,
                            'amount' => $result->amount,
                            'transfer_reversal_id' => $result->id
                        ]);

                        // Create refund for the customer using the original charge
                        $customerRefund = \Stripe\Refund::create([
                            'charge' => $originalChargeId,
                            'amount' => $result->amount, // Use the same amount as the transfer reversal
                            'reason' => 'requested_by_customer',
                            'metadata' => array_merge($metadata, [
                                'refund_step' => 'customer_refund',
                                'transfer_reversal_id' => $result->id,
                                'original_charge_id' => $originalChargeId
                            ])
                        ]);

                        \Log::info('Customer refund created', [
                            'refund_id' => $customerRefund->id,
                            'status' => $customerRefund->status,
                            'amount' => $customerRefund->amount,
                            'charge_id' => $originalChargeId
                        ]);

                        if ($customerRefund->status === 'succeeded') {
                            return [
                                'success' => true,
                                'transfer_reversal_id' => $result->id,
                                'customer_refund_id' => $customerRefund->id,
                                'transfer_id' => $this->refund_txn_id,
                                'original_charge_id' => $originalChargeId,
                                'amount' => $result->amount,
                                'refund_type' => 'complete_refund'
                            ];
                        } else {
                            \Log::warning('Customer refund status not succeeded', [
                                'refund_id' => $customerRefund->id,
                                'status' => $customerRefund->status,
                                'charge_id' => $originalChargeId
                            ]);
                            return [
                                'success' => false,
                                'error' => 'Transfer reversal succeeded but customer refund failed: ' . $customerRefund->status,
                                'transfer_reversal_id' => $result->id,
                                'customer_refund_id' => $customerRefund->id,
                                'partial_success' => true
                            ];
                        }
                    } catch (\Stripe\Exception\ApiErrorException $e) {
                        \Log::error('Stripe API error during customer refund', [
                            'error_message' => $e->getMessage(),
                            'error_code' => $e->getStripeCode(),
                            'charge_id' => $originalChargeId,
                            'amount' => $result->amount
                        ]);
                        return [
                            'success' => false,
                            'error' => 'Customer refund failed: ' . $e->getMessage(),
                            'stripe_error_code' => $e->getStripeCode(),
                            'transfer_reversal_id' => $result->id,
                            'partial_success' => true
                        ];
                    }
                } else {
                    \Log::error('Transfer reversal failed', [
                        'result' => $result,
                        'transfer_id' => $this->refund_txn_id
                    ]);
                    return [
                        'success' => false,
                        'error' => 'Transfer reversal failed or returned invalid result'
                    ];
                }

            } else {
                // OLD ARCHITECTURE: Individual Charges (fallback for existing data)
                // Retrieve the charge to verify it exists
                $charge = \Stripe\Charge::retrieve($this->refund_txn_id);

                // Prepare metadata
                $metadata = array_merge([
                    'refund_reason' => $refundReason,
                    'influencer_request_detail_id' => $this->id,
                    'campaign_id' => $this->compaign_id,
                    'refund_method' => 'cancelCampaign_charge_refund',
                    'architecture' => 'individual_charges'
                ], $additionalMetadata);

                // Create refund for individual charge
                $result = \Stripe\Refund::create([
                    'charge' => $this->refund_txn_id,
                    'reason' => 'requested_by_customer',
                    'metadata' => $metadata
                ]);

                if ($result->status === 'succeeded') {
                    return [
                        'success' => true,
                        'refund_id' => $result->id,
                        'charge_id' => $this->refund_txn_id,
                        'amount' => $result->amount,
                        'refund_type' => 'charge_refund'
                    ];
                } else {
                    return [
                        'success' => false,
                        'error' => 'Refund status not succeeded: ' . $result->status
                    ];
                }
            }

        } catch (\Stripe\Exception\ApiErrorException $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'stripe_error_code' => $e->getStripeCode(),
                'stripe_error_type' => $e->getError()->type ?? null
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Send cancellation notifications to relevant parties
     *
     * @return void
     */
    private function sendCancellationNotifications(): void
    {
        try {
            // Get customer user
            $customer = \App\Models\User::find($this->user_id);

            if ($customer) {
                // Dispatch job and send notification
                dispatch(new \App\Jobs\NewCancelRefund($customer, $this));
                $customer->notify(new \App\Notifications\CancelRefund($customer, $this));
            }

        } catch (\Exception $e) {
            \Log::warning('Failed to send cancellation notifications', [
                'influencer_request_detail_id' => $this->id,
                'campaign_id' => $this->compaign_id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
