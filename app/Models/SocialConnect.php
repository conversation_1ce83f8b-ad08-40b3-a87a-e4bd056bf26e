<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SocialConnect extends Model
{
    protected $fillable = ['user_id', 'media', 'name', 'picture', 'url', 'followers','token','token_secret','social_id'];

    protected $casts = [
        'followers' => 'integer',
    ];

    /**
     * Get the full URL for the picture
     */
    public function getPictureUrlAttribute()
    {
        if (empty($this->picture)) {
            return null;
        }

        // If it's already a full URL, return as is
        if (stripos($this->picture, 'http') === 0) {
            return $this->picture;
        }

        // Generate the proper storage URL
        return asset('storage/' . $this->picture);
    }

    /**
     * Check if the social connection has a valid token
     */
    public function hasValidToken(): bool
    {
        return !empty($this->token);
    }

    /**
     * Check if this is an OAuth 1.0 connection (has token_secret)
     */
    public function isOAuth1(): bool
    {
        return !empty($this->token_secret);
    }

    /**
     * Check if this is an OAuth 2.0 connection (no token_secret)
     */
    public function isOAuth2(): bool
    {
        return !empty($this->token) && empty($this->token_secret);
    }

    /**
     * Check if the profile URL is valid
     */
    public function hasValidUrl(): bool
    {
        return !empty($this->url) && filter_var($this->url, FILTER_VALIDATE_URL) !== false;
    }

    /**
     * Check if the picture URL is valid
     */
    public function hasValidPicture(): bool
    {
        return !empty($this->picture) && filter_var($this->picture, FILTER_VALIDATE_URL) !== false;
    }

    /**
     * Get the domain from the profile URL
     */
    public function getUrlDomain(): ?string
    {
        if (!$this->hasValidUrl()) {
            return null;
        }

        return parse_url($this->url, PHP_URL_HOST);
    }

    /**
     * Check if the social ID is valid (not empty)
     */
    public function hasValidSocialId(): bool
    {
        return !empty($this->social_id);
    }

    /**
     * Get a truncated version of social ID for display purposes
     */
    public function getTruncatedSocialId(int $length = 20): string
    {
        if (empty($this->social_id)) {
            return '';
        }

        if (strlen($this->social_id) <= $length) {
            return $this->social_id;
        }

        return substr($this->social_id, 0, $length - 3) . '...';
    }
}
