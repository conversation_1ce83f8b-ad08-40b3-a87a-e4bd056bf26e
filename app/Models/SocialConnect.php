<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SocialConnect extends Model
{
    protected $fillable = ['user_id', 'media', 'name', 'picture', 'url', 'followers','token','token_secret','social_id'];

    /**
     * Get the full URL for the picture
     */
    public function getPictureUrlAttribute()
    {
        if (empty($this->picture)) {
            return null;
        }

        // If it's already a full URL, return as is
        if (stripos($this->picture, 'http') === 0) {
            return $this->picture;
        }

        // Generate the proper storage URL
        return asset('storage/' . $this->picture);
    }
}
