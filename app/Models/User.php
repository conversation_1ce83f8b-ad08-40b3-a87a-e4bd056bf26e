<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Cashier\Billable;
use Lab404\Impersonate\Models\Impersonate;

class User extends Authenticatable
{
    use Notifiable;
    use Billable;
    use Impersonate;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'user_type',
        'phone',
        'password',
        'profile_pic',
        'provider_id',
        'provider',
        'email_verified_at',
        'verify_token',
        'remember_token',
        'created_at',
        'updated_at',
        'company_name',
        'street',
        'city',
        'zip_code',
        'country',
        'state',
        'flag',
        'status',
        'paypal_email',
        'stripe_id',
        'pm_type',
        'pm_last_four',
        'trial_ends_at',
        'trophy',
        'instagram_name',
        'twitter_name',
        'facebook_name',
        'youtube_name',
        'tiktok_name',
        'twitch_name',
        'vat_id',
        'receive_mails',
        'activate',
        'category_id',
        'is_email_sent',
        'lang',
        'set_password',
        'identification_no',
        'is_small_business_owner'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    public function advertisingMethodDetail()
    {
        return $this->hasOne(AdvertisingMethodDetail::class);
    }

    public function advertisingMethodPrice()
    {
        return $this->hasOne(AdvertisingMethodPrice::class);
    }


    public function influencerDetail()
    {
        return $this->hasOne(InfluencerDetail::class);
    }

    public function hashtags()
    {
        return $this->hasMany(Hashtag::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class, 'categories');
    }


    public function countries()
    {
        return $this->hasOne(Country::class, 'id', 'country');
    }

    public function stripeAccount()
    {
        return $this->hasOne(StripeAccount::class);
    }

    /**
     * Determine if the user can impersonate other users.
     * Only admin users can impersonate others.
     *
     * @return bool
     */
    public function canImpersonate()
    {
        return $this->user_type === 'admin';
    }

    /**
     * Determine if the user can be impersonated.
     * Admin users cannot be impersonated.
     *
     * @return bool
     */
    public function canBeImpersonated()
    {
        return $this->user_type !== 'admin';
    }

    /**
     * Check if the current user is being impersonated
     *
     * @return bool
     */
    public function isImpersonated()
    {
        return session()->has('impersonated_by');
    }

    /**
     * Get the original impersonator user
     *
     * @return User|null
     */
    public function getImpersonator()
    {
        if (!$this->isImpersonated()) {
            return null;
        }

        $impersonatorId = session('impersonated_by');
        return User::find($impersonatorId);
    }
}
