<?php

/**
 * Purpose: New Algorithmic Pricing System (Recently implemented)

What it stores: Calculated prices using the new PricingCalculator service
Structure:
influencer_id → Links to influencers table
campaign_type → Campaign type (Boost Me, Reaction Video, Survey)
post_type → Post type (story, reel, feed)
price → Calculated price
breakdown → JSON with calculation details
priced_at → When price was calculated
Used for:

✅ New pricing system (the one we just implemented)
✅ Admin pricing analysis and debugging
✅ Future marketplace integration
 */

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InfluencerPrice extends Model
{
    use HasFactory;

    protected $fillable = [
        'influencer_id',
        'campaign_type',
        'post_type',
        'price',
        'breakdown',
        'priced_at',
    ];

    protected $casts = [
        'breakdown' => 'array',
        'priced_at' => 'datetime',
        'price' => 'float',
    ];

    public function influencer(): BelongsTo
    {
        return $this->belongsTo(Influencer::class);
    }
}
