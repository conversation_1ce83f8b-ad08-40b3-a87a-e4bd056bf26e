<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Models\Influencer;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use romanzipp\QueueMonitor\Traits\IsMonitored;

/**
 * Job to update all influencers' data from social media sources.
 * 
 * This job dispatches individual UpdateInfluencerDataFromSocialJob jobs
 * for each influencer in the system to update their social media data.
 * 
 * Process:
 * 1. Fetch all influencers from the database
 * 2. Dispatch UpdateInfluencerDataFromSocialJob for each influencer
 * 3. Log progress and completion statistics
 * 
 * Usage:
 * - Can be run manually via artisan command
 * - Can be scheduled to run periodically (daily/weekly)
 * - Useful for bulk data synchronization
 * 
 * @package App\Jobs
 * <AUTHOR> Development Team
 */
class UpdateAllInfluencersDataFromSocialJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, IsMonitored;

    /**
     * Execute the job to update all influencers' data from social sources.
     * 
     * @return void
     */
    public function handle(): void
    {
        Log::info('Starting bulk update of all influencers data from social sources');

        $influencers = Influencer::all();
        $totalInfluencers = $influencers->count();

        Log::info('Found influencers to update', [
            'total_count' => $totalInfluencers
        ]);

        $jobsDispatched = 0;

        foreach ($influencers as $influencer) {
            try {
                // Dispatch individual job for each influencer
                UpdateInfluencerDataFromSocialJob::dispatch($influencer->id);
                $jobsDispatched++;

                Log::debug('Dispatched social data update job', [
                    'influencer_id' => $influencer->id,
                    'user_id' => $influencer->user_id,
                    'jobs_dispatched' => $jobsDispatched,
                    'total_influencers' => $totalInfluencers
                ]);

            } catch (\Exception $e) {
                Log::error('Failed to dispatch social data update job', [
                    'influencer_id' => $influencer->id,
                    'user_id' => $influencer->user_id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }

        Log::info('Completed dispatching social data update jobs', [
            'total_influencers' => $totalInfluencers,
            'jobs_dispatched' => $jobsDispatched,
            'jobs_failed' => $totalInfluencers - $jobsDispatched
        ]);
    }
}
