<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Constants\PostType;
use App\Models\Influencer;
use App\Models\SocialConnect;
use App\Models\SocialPost;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use romanzipp\QueueMonitor\Traits\IsMonitored;

/**
 * Job to update influencer data from social media connections and posts.
 * 
 * This job synchronizes data from social_connects and social_posts tables
 * into the influencers table for performance and analytics purposes.
 * 
 * Data Sources:
 * - social_connects.followers → influencers.followers
 * - social_posts.insights (JSON) → influencers reach averages and flags
 * 
 * Insights Processing:
 * - Aggregates reach data from all social posts with insights
 * - Calculates averages by post type (story, reel, feed)
 * - Sets flags when insufficient data is available
 * - Uses statistical trimming to remove outliers (same as ComputeAverageReachJob)
 * 
 * @package App\Jobs
 * <AUTHOR> Development Team
 */
class UpdateInfluencerDataFromSocialJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, IsMonitored;

    /**
     * Minimum number of story posts required to calculate average reach
     */
    const STORY_THRESHOLD = 15;
    
    /**
     * Minimum number of reel posts required to calculate average reach
     */
    const REEL_THRESHOLD = 5;
    
    /**
     * Minimum number of feed posts required to calculate average reach
     */
    const FEED_THRESHOLD = 5;
    
    /**
     * Number of days to look back for post analysis
     */
    const ANALYSIS_PERIOD_DAYS = 60;
    
    /**
     * Trimming percentage for outlier removal (0.2 = 20% total, 10% from each end)
     */
    const TRIM_PERCENTAGE = 0.2;
    
    /**
     * Multiplier for calculating total trimmed values (both ends)
     */
    const TRIM_MULTIPLIER = 2;

    /**
     * Create a new job instance.
     * 
     * @param int $influencerId The ID of the influencer to update data for
     */
    public function __construct(private int $influencerId)
    {
    }

    /**
     * Execute the job to update influencer data from social media sources.
     * 
     * Process:
     * 1. Update follower count from social_connects table
     * 2. Aggregate insights data from social_posts table
     * 3. Calculate average reach by post type with statistical trimming
     * 4. Set flags for insufficient data scenarios
     * 5. Update influencers table with calculated values
     * 
     * @return void
     */
    public function handle(): void
    {
        $influencer = Influencer::find($this->influencerId);
        
        if (!$influencer) {
            Log::warning('UpdateInfluencerDataFromSocialJob: Influencer not found', [
                'influencer_id' => $this->influencerId
            ]);
            return;
        }

        Log::info('Starting influencer data update from social sources', [
            'influencer_id' => $this->influencerId
        ]);

        // Step 1: Update follower count from social_connects
        $this->updateFollowerCount($influencer);

        // Step 2: Update reach averages and flags from social_posts insights
        $this->updateReachAveragesFromInsights($influencer);

        Log::info('Completed influencer data update from social sources', [
            'influencer_id' => $this->influencerId
        ]);
    }

    /**
     * Update follower count from social_connects table.
     * 
     * @param Influencer $influencer
     * @return void
     */
    private function updateFollowerCount(Influencer $influencer): void
    {
        // Get the most recent follower count from social_connects
        // Assuming we want Instagram followers (most common for influencers)
        $socialConnect = SocialConnect::where('user_id', $influencer->user_id)
            ->where('media', 'instagram')
            ->orderBy('updated_at', 'desc')
            ->first();

        if ($socialConnect && $socialConnect->followers > 0) {
            $influencer->followers = $socialConnect->followers;
            
            Log::info('Updated follower count from social_connects', [
                'influencer_id' => $this->influencerId,
                'user_id' => $influencer->user_id,
                'followers' => $socialConnect->followers
            ]);
        } else {
            Log::info('No valid social_connects data found for follower update', [
                'influencer_id' => $this->influencerId,
                'user_id' => $influencer->user_id
            ]);
        }
    }

    /**
     * Update reach averages and flags from social_posts insights data.
     * 
     * @param Influencer $influencer
     * @return void
     */
    private function updateReachAveragesFromInsights(Influencer $influencer): void
    {
        $since = Carbon::now()->subDays(self::ANALYSIS_PERIOD_DAYS);

        // Define post type mappings with thresholds and database fields
        $postTypes = [
            PostType::STORY => [
                'threshold' => self::STORY_THRESHOLD, 
                'field' => 'ig_story_avg_reach', 
                'flag' => 'flag_story_insight_missing'
            ],
            PostType::REEL => [
                'threshold' => self::REEL_THRESHOLD, 
                'field' => 'ig_reel_avg_reach', 
                'flag' => 'flag_reel_insight_missing'
            ],
            PostType::FEED_POST => [
                'threshold' => self::FEED_THRESHOLD, 
                'field' => 'ig_feed_avg_reach', 
                'flag' => 'flag_feed_insight_missing'
            ],
        ];

        foreach ($postTypes as $postType => $config) {
            // Objects in php are passed by handle
            $this->processPostTypeInsights($influencer, $postType, $config, $since);
        }

        // Save all changes to the influencer record
        $influencer->save();
    }

    /**
     * Process insights for a specific post type and update influencer data.
     * 
     * @param Influencer $influencer
     * @param string $postType
     * @param array $config
     * @param Carbon $since
     * @return void
     */
    private function processPostTypeInsights(Influencer $influencer, string $postType, array $config, Carbon $since): void
    {
        // Get all social posts for this user and post type with insights data
        $socialPosts = SocialPost::where('user_id', $influencer->user_id)
            ->where('media', 'instagram') // Focus on Instagram for now
            ->where('post_category', $postType)
            ->where('published_at', '>=', $since)
            ->whereNotNull('insights')
            ->where('insights', '!=', '')
            ->where('insights', '!=', '[]')
            ->get();

        Log::info('Processing post type insights', [
            'influencer_id' => $this->influencerId,
            'post_type' => $postType,
            'posts_found' => $socialPosts->count(),
            'threshold' => $config['threshold']
        ]);

        // Extract reach values from insights JSON
        $reachValues = [];
        
        foreach ($socialPosts as $post) {
            $insights = $post->insights;
            
            // Handle both array and JSON string formats
            if (is_string($insights)) {
                $insights = json_decode($insights, true);
            }
            
            if (is_array($insights) && isset($insights['reach']) && $insights['reach'] > 0) {
                $reachValues[] = (int) $insights['reach'];
            }
        }

        $count = count($reachValues);
        
        if ($count >= $config['threshold']) {
            // Sufficient data: calculate trimmed average
            $reaches = collect($reachValues)->sort()->values();
            
            // Statistical trimming: Remove outliers (20% total: 10% from each end)
            $trim = (int) floor($count * self::TRIM_PERCENTAGE);
            if ($trim > 0) {
                // slice($start, $length): Skip first $trim, take middle portion
                $reaches = $reaches->slice($trim, $count - (self::TRIM_MULTIPLIER * $trim));
            }
            
            // Calculate average from trimmed data
            $average = (int) round($reaches->avg());
            
            // Update influencer record
            // Objects in php are passed by handle, so updating $influencer
            // object here will also update the original object passed
            // to this method.
            $influencer->{$config['field']} = $average;
            $influencer->{$config['flag']} = false; // Clear the missing flag
            
            Log::info('Updated reach average from insights', [
                'influencer_id' => $this->influencerId,
                'post_type' => $postType,
                'total_posts' => $count,
                'trimmed_posts' => $reaches->count(),
                'average_reach' => $average,
                'field' => $config['field']
            ]);
            
        } else {
            // Insufficient data: set flag
            // object here will also update the original object passed
            // to this method.
            $influencer->{$config['field']} = 0;
            $influencer->{$config['flag']} = true; // Set the missing flag
            
            Log::info('Insufficient insights data for reach calculation', [
                'influencer_id' => $this->influencerId,
                'post_type' => $postType,
                'posts_found' => $count,
                'threshold_required' => $config['threshold'],
                'flag_set' => $config['flag']
            ]);
        }
    }
}
