<?php

namespace App\Jobs;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail; 

use Illuminate\Support\Facades\Log;


use App\Notifications\sendPasswordLink;
use App\Mail\NewSendyourCampaignisMovingForward;

class NewyourCampaignisMovingForward implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
 
 
    /**
     * Create a new job instance.
     *
     * @return void
     */
 
    public $customer;
    public $influencerDetail;
    public $influencer;

    public function __construct($customer,$influencerDetail,$influencer)
    {
        $this->customer = $customer;
        $this->influencerDetail = $influencerDetail;
        $this->influencer = $influencer;
    }


    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \Log::info('Mail dispatch to yourCampaignisMovingForward '.$this->customer->email);   
        Mail::to($this->customer->email)->send(new NewSendyourCampaignisMovingForward($this->customer,$this->influencerDetail,$this->influencer));
        return 1;
    }
}
