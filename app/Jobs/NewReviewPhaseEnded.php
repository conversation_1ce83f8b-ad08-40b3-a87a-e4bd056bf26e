<?php

namespace App\Jobs;

use App\Mail\NewReviewPhaseEnded as MailNewReviewPhaseEnded;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class NewReviewPhaseEnded implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $customer;
    public $row;
    public function __construct($customer, $row)
    {
        $this->customer = $customer;
        $this->row = $row;
    }

    public function handle()
    {
        \Log::info('Mail dispatch to review phase ended '.$this->customer->email);   
        Mail::to($this->customer->email)->send(new MailNewReviewPhaseEnded($this->customer,$this->row));
        return 1;
    }
}
