<?php

namespace App\Jobs;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail; 

use Illuminate\Support\Facades\Log;


use App\Notifications\sendPasswordLink;
use App\Mail\NewSendsocialAlreadyUsed;

class NewsocialAlreadyUsed implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
 
 
    /**
     * Create a new job instance.
     *
     * @return void
     */
 
    public $user;
    public $sender_user;

    public function __construct($sender_user,$user)
    {
        $this->user = $user;
        $this->sender_user = $sender_user;
    }


    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \Log::info('Mail dispatch to socialAlreadyUsed '.$this->sender_user->email);   
        Mail::to($this->sender_user->email)->send(new NewSendsocialAlreadyUsed($this->user,$this->sender_user));
        return 1;
    }
}
