<?php

namespace App\Jobs;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail; 

use Illuminate\Support\Facades\Log;


use App\Notifications\sendPasswordLink;
use App\Mail\NewSendStartCampaignInfluencer;

class NewStartCampaignInfluencer implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
 
 
    /**
     * Create a new job instance.
     *
     * @return void
     */
 
    public $influencer;
    public $influencerDetail;

    public function __construct($influencer,$influencerDetail)
    {
        $this->influencer = $influencer;
        $this->influencerDetail = $influencerDetail;
    }


    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \Log::info('Mail dispatch to StartCampaignInfluencer '.$this->influencer->email);   
        Mail::to($this->influencer->email)->send(new NewSendStartCampaignInfluencer($this->influencer,$this->influencerDetail));
        return 1;
    }
}
