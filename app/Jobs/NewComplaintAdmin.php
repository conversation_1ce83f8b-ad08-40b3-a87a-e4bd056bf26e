<?php

namespace App\Jobs;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail; 

use Illuminate\Support\Facades\Log;


use App\Notifications\sendPasswordLink;
use App\Mail\NewSendComplaintAdmin;

class NewComplaintAdmin implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
 
 
    /**
     * Create a new job instance.
     *
     * @return void
     */
 
    public $admin;
    public $influencer;
    public $user;
    public $complaint;
    public $invoice; 

    public function __construct($admin,$influencer,$user,$invoice, $complaint)
    {
        $this->admin = $admin;
        $this->influencer = $influencer;
        $this->user = $user;
        $this->complaint = $complaint;
        $this->invoice = $invoice; 
    }


    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \Log::info('Mail dispatch to ComplaintAdmin '.$this->admin->email);   
        Mail::to($this->admin->email)->send(new NewSendComplaintAdmin($this->admin,$this->influencer,$this->user,$this->invoice,$this->complaint));
        return 1;
    }
}
