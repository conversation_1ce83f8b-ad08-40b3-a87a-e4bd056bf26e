<?php

namespace App\Jobs;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail; 

use Illuminate\Support\Facades\Log;


use App\Notifications\sendPasswordLink;
use App\Mail\NewSendRequestInfluencer;

class NewRequestInfluencer implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
 
 
    /**
     * Create a new job instance.
     *
     * @return void
     */
 
    public $user;
    public $influencer;
    public $detail;

    public function __construct($influencer,$user,$detail)
    {
        $this->user = $user;
        $this->influencer = $influencer;
        $this->detail = $detail;
    }


    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \Log::info('Mail dispatch to RequestInfluencer '.$this->influencer->email);   
        Mail::to($this->influencer->email)->send(new NewSendRequestInfluencer($this->user,$this->influencer,$this->detail));
        return 1;
    }
}
