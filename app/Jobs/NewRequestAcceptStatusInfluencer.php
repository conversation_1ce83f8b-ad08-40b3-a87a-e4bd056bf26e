<?php

namespace App\Jobs;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail; 

use Illuminate\Support\Facades\Log;


use App\Notifications\sendPasswordLink;
use App\Mail\NewSendRequestAcceptStatusInfluencer;

class NewRequestAcceptStatusInfluencer implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
 
 
    /**
     * Create a new job instance.
     *
     * @return void
     */
 
    public $customer;
    public $influencer_request_accept;
    public $influencerDetail;
    public $status;

    public function __construct($customer,$influencer_request_accept,$influencerDetail,$status)
    {
        $this->customer = $customer;
        $this->influencer_request_accept = $influencer_request_accept;
        $this->influencerDetail = $influencerDetail;
        $this->status = $status;
    }


    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \Log::info('Mail dispatch to RequestAcceptStatusInfluencer '.$this->customer->email);   
        Mail::to($this->customer->email)->send(new NewSendRequestAcceptStatusInfluencer($this->customer,$this->influencer_request_accept,$this->influencerDetail,$this->status));
        return 1;
    }
}
