<?php

namespace App\Jobs;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use romanzipp\QueueMonitor\Traits\IsMonitored;
use Illuminate\Support\Facades\Mail; 

use Illuminate\Support\Facades\Log;


use App\Notifications\sendPasswordLink;
use App\Mail\NewSendnoInfluencerHasAcceptedYourRequest;

class NewnoInfluencerHasAcceptedYourRequest implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, IsMonitored;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public $customer;
    public $influencerData;

    public function __construct($customer, $influencerData)
    {
        $this->customer = $customer;
        $this->influencerData = $influencerData;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Mail::to($this->customer->email)
            ->send(
                new NewSendnoInfluencerHasAcceptedYourRequest($this->customer, $this->influencerData)
            );
        \Log::info('Mail dispatch to noInfluencerHasAcceptedYourRequest ' . $this->customer->email);
        return 1;
    }
}
