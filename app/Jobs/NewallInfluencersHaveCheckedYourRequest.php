<?php

namespace App\Jobs;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail; 

use Illuminate\Support\Facades\Log;


use App\Notifications\sendPasswordLink;
use App\Mail\NewSendallInfluencersHaveCheckedYourRequest;

class NewallInfluencersHaveCheckedYourRequest implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
 
 
    /**
     * Create a new job instance.
     *
     * @return void
     */
 
    public $customer;
    public $request;
    public $influencerDetail;

    public function __construct($customer,$request,$influencerDetail)
    {
        $this->customer = $customer;
        $this->request = $request;
        $this->influencerDetail = $influencerDetail;
    }


    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \Log::info('Mail dispatch to allInfluencersHaveCheckedYourRequest '.$this->customer->email);   
        Mail::to($this->customer->email)->send(new NewSendallInfluencersHaveCheckedYourRequest($this->customer,$this->request,$this->influencerDetail));
        return 1;
    }
}
