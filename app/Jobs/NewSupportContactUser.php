<?php

namespace App\Jobs;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail; 

use Illuminate\Support\Facades\Log;


use App\Notifications\sendPasswordLink;
use App\Mail\NewSendSupportContactUser;

class NewSupportContactUser implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
 
 
    /**
     * Create a new job instance.
     *
     * @return void
     */
 
    public $user;
    public $influencer;
    public $support;

    public function __construct($influencer,$user,$support)
    {
        $this->user = $user;
        $this->influencer = $influencer;
        $this->support = $support;
    }


    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \Log::info('Mail dispatch to SupportContactUser '.$this->user->email);   
        Mail::to($this->user->email)->send(new NewSendSupportContactUser($this->influencer,$this->user,$this->support));
        return 1;
    }
}
