<?php

namespace App\Jobs;

use App\Models\InfluencerRequestDetail;
use App\Models\Invoice;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use romanzipp\QueueMonitor\Traits\IsMonitored;
use Illuminate\Support\Facades\Log;

class ChargeInfluencers implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, IsMonitored;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $influencerRequestDetails = InfluencerRequestDetail::with('invoices')
            ->where('payment_status', 'pending')
            ->whereNotNull('invoice_id')
            ->whereNull('refund_reason')
            ->whereNotNull('completed_at')
            ->where('is_cashed_out', 0)
            ->whereNull('cashed_out_at')
            ->where('review', 1)
            ->when(config('app.env') === 'local', function ($query) {
                $query->whereDate('completed_at', '<=', Carbon::now()->subMinutes(15));
            }, function ($query) {
                $query->whereDate('completed_at', '<=', Carbon::now()->subDays(14));
            })
            ->whereHas('invoices', function ($query) {
                $query->where('payment_status', 'paid');
                $query->where('is_refunded', 0);
            })
            ->orderBy('completed_at', 'desc')
            ->get();

        \Stripe\Stripe::setApiKey(config('settings.env.STRIPE_SECRET'));

        foreach ($influencerRequestDetails as $influencerRequestDetail) {
            try {
                // TODO A temporary solution to broken data in the database.
                if (empty($influencerRequestDetail->influencer_request_accepts->user->stripeAccount->stripe_user_id)) {
                    continue;
                }

                $connectAccountId = $influencerRequestDetail->influencer_request_accepts->user->stripeAccount->stripe_user_id;

                $originalInvoice = $influencerRequestDetail->invoices;
                if (!$originalInvoice) {
                    $influencerRequestDetail->update([
                        'has_stripe_issue' => 1,
                        'response' => 'original invoice issue for campaign' .
                            $influencerRequestDetail->compaign_id .
                            ' for influencer:' .$influencerRequestDetail->id,
                    ]);

                    continue;
                }

                $account = \Stripe\Account::retrieve();
                $platformAccountId = $account->id;

                if ($influencerRequestDetail->platform_amount > 0) {
                    $commissionAmount = (int) round($influencerRequestDetail->platform_amount * 100);
                } else {
                    $originalAmountCents = (int) round($influencerRequestDetail->influencer_price * 100);
                    $commissionAmountCents = (int) round($originalAmountCents * 0.20);

                    // Calculate 19% VAT on the commission amount
                    $commissionVatAmountCents = (int) round($commissionAmountCents * 0.19);

                    // Total commission amount including VAT
                    $commissionAmount = $commissionAmountCents + $commissionVatAmountCents;
                }

                // Check connected account balance first
                $balance = \Stripe\Balance::retrieve([
                    'stripe_account' => $connectAccountId
                ]);

                $beforeBalance = $balance->available[0]->amount;

                if ($beforeBalance < $commissionAmount) {
                    $influencerRequestDetail->update([
                        'has_stripe_issue' => 1,
                        'response' => 'balance low for campaign' . $influencerRequestDetail->compaign_id . ' for influencer:' .$influencerRequestDetail->id,
                    ]);

                    continue;
                }

                // Create a charge on the connected account
                $transfer = \Stripe\Transfer::create([
                    'amount' => $commissionAmount,    // e.g. 20% + VAT in cents
                    'currency' => 'eur',
                    'destination' => $platformAccountId,  // Funds will go to your platform's Stripe account
                    'description' => 'Influencer Commission collection for Campaign ' . $influencerRequestDetail->compaign_id,
                    'metadata' => [
                        'influencer_request_detail' => $influencerRequestDetail->id,
                        'campaign_id' => $influencerRequestDetail->compaign_id,
                        'fee_type' => 'influencer_campaign_commission_for_'.$influencerRequestDetail->compaign_id,
                        'original_invoice_id' => $originalInvoice->id,
                        'connected_account_id' => $connectAccountId,
                    ],
                ], [
                    'stripe_account' => $connectAccountId  // Source: the connected account
                ]);

                $admin = User::where('user_type','admin')->select('id')->first();

                // Check connected account balance after transfer
                $balance = \Stripe\Balance::retrieve([
                    'stripe_account' => $connectAccountId
                ]);

                $afterBalance = $balance->available[0]->amount;

                Invoice::create([
                    'charge_id' => $transfer->id, // or rename to 'transfer_id'
                    'user_id' => $admin->id ?? null,
                    'campaign_id' => $influencerRequestDetail->compaign_id,
                    'influencer_request_detail_id' => $influencerRequestDetail->id,
                    'influencer_id' => $influencerRequestDetail->influencer_request_accepts->user->id,
                    'payment_type' => 'Influencer_Commission_Transfer',
                    'payment_amount' =>  number_format($commissionAmount / 100, 2), // or $transfer->amount
                    'payment_details' => json_encode([
                        'transfer_id' => $transfer->id,
                        'connect_account_id' => $connectAccountId,
                        'fee_rate' => '20%',
                        'fee_amount' => number_format($commissionAmount / 100, 2), // or $transfer->amount,
                        'original_invoice_id' => $originalInvoice->id,
                        'balance_before_transfer' => $beforeBalance,
                        'balance_after_transfer' => $afterBalance,
                        'created_at' => Carbon::createFromTimestamp($transfer->created),
                    ]),
                    'receipt' => null, // Transfers won't have a typical 'receipt_url'
                    'payment_status' => 'transferred',
                    'paid_amount' => number_format($commissionAmount / 100, 2),
                    'description' => 'Influencer_Commission_Transfer 20% for Campaign: ' . $influencerRequestDetail->compaign_id
                ]);

                $retrievedTransfer = \Stripe\Transfer::retrieve($transfer->id, [
                    'stripe_account' => $connectAccountId, // Specify the connected account context
                ]);

                if (isset($retrievedTransfer->id) && $retrievedTransfer) {
                    $influencerRequestDetail->update([
                        'payment_status' => InfluencerRequestDetail::STATUS_COMPLETED,
                        'response' => 'commission charged successfully and ready for payout'
                    ]);

                } else {
                    $influencerRequestDetail->update([
                        'has_stripe_issue' => 1,
                        'response' => 'stripe transfer failed for campaign:' . ($influencerRequestDetail->compaign_id ?? 'N/A') . ' for influencer: ' . ($influencerRequestDetail->id ?? 'N/A'),
                    ]);
                }

            } catch (\Exception $e) {
                $influencerRequestDetail->update([
                    'has_stripe_issue' => 1,
                    'response' => $e->getMessage() . ' issue for campaign: ' . ($influencerRequestDetail->compaign_id ?? 'N/A') . ' for influencer: ' . ($influencerRequestDetail->id ?? 'N/A'),
                ]);

                // Limit log to 10 times per day using cache
                $logKey = 'charge_influencers_log_count_' . date('Ymd');
                $logCount = cache()->get($logKey, 0);

                if ($logCount < 10) {
                    Log::error($e->getMessage(), [
                        'exception_trace' => $e->getTraceAsString(),
                        'paid_id' => $influencerRequestDetail->id ?? null,
                        'campaign_id' => $influencerRequestDetail->compaign_id ?? null,
                        'connect_account_id' => $connectAccountId ?? null,
                        'commissionAmount' => $commissionAmount ?? null,
                        'originalInvoice' => isset($originalInvoice) ? $originalInvoice->toArray() : null,
                        'class' => 'ChargeInfluencers',
                    ]);

                    // Increment counter and store in cache until end of day
                    cache()->put($logKey, $logCount + 1, Carbon::now()->endOfDay());
                }
            }
        }
    }
}
