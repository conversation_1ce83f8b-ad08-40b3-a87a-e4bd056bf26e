<?php

namespace App\Jobs;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail; 

use Illuminate\Support\Facades\Log;


use App\Notifications\sendPasswordLink;
use App\Mail\NewSendSupportContactAdmin;

class NewSupportContactAdmin implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
 
 
    /**
     * Create a new job instance.
     *
     * @return void
     */
 
    public $user;
    public $influencer;
    public $support;
    public $admin;

    public function __construct($admin,$influencer,$user,$support)
    {
        $this->admin = $admin;
        $this->user = $user;
        $this->influencer = $influencer;
        $this->support = $support;
    }


    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \Log::info('Mail dispatch to SupportContactAdmin '.$this->admin->email);   
        Mail::to($this->admin->email)->send(new NewSendSupportContactAdmin($this->admin,$this->influencer,$this->user,$this->support));
        return 1;
    }
}
