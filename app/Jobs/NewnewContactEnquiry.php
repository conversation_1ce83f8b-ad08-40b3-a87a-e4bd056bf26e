<?php

namespace App\Jobs;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail; 

use Illuminate\Support\Facades\Log;
use App\Mail\MailnewContactEnquiry;


class NewnewContactEnquiry implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
 
 
    /**
     * Create a new job instance.
     *
     * @return void
     */
    

    public $admin;
    public $details;

    public function __construct($admin,$details)
    {
        $this->admin = $admin;
        $this->details = $details;
    }


    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \Log::info('Mail dispatch to newContact '.$this->admin->email);   
        Mail::to($this->admin->email)->send(new MailnewContactEnquiry($this->admin,$this->details));
        return 1;
    }
}
