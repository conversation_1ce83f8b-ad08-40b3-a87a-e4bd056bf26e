<?php

namespace App\Jobs;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail; 

use Illuminate\Support\Facades\Log;


use App\Notifications\sendPasswordLink;
use App\Mail\NewSendreminderCampaignTimeisRunningOut;

class NewreminderCampaignTimeisRunningOut implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
 
 
    /**
     * Create a new job instance.
     *
     * @return void
     */
   public $influencer;
    public $row;

    public function __construct($influencer, $row)
    {
        $this->influencer = $influencer;
        $this->row = $row;
    }



    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \Log::info('Mail dispatch to CancelRefund '.$this->influencer->email);   
        Mail::to($this->influencer->email)->send(new NewSendreminderCampaignTimeisRunningOut($this->influencer,$this->row));
        return 1;
    }
}
