<?php

namespace App\Jobs;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail; 

use Illuminate\Support\Facades\Log;


use App\Notifications\sendPasswordLink;
use App\Mail\NewSendcustomerComplaintwasAccepted;

class NewcustomerComplaintwasAccepted implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
 
 
    /**
     * Create a new job instance.
     *
     * @return void
     */
   public $customer;
    public $row;

    public function __construct($customer, $row)
    {
        $this->customer = $customer;
        $this->row = $row;
    }



    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \Log::info('Mail dispatch to customerComplaintwasAccepted '.$this->customer->email);   
        Mail::to($this->customer->email)->send(new NewSendcustomerComplaintwasAccepted($this->customer,$this->row));
        return 1;
    }
}
