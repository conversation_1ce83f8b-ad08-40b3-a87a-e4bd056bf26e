<?php

namespace App\Jobs;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail; 

use Illuminate\Support\Facades\Log;


use App\Notifications\sendPasswordLink;
use App\Mail\NewSendDisputeContactUser;

class NewDisputeContactUser implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
 
 
    /**
     * Create a new job instance.
     *
     * @return void
     */
 
  
    public $influencer;
    public $user;
    public $support;

    public function __construct($influencer,$user,$support)
    { 
        $this->influencer = $influencer;
        $this->user = $user;
        $this->support = $support;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \Log::info('Mail dispatch to DisputeContactUser '.$this->user->email);   
        Mail::to($this->user->email)->send(new NewSendDisputeContactUser($this->influencer,$this->user,$this->support));
        return 1;
    }
}
