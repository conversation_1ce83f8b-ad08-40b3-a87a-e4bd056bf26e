<?php

namespace App\Jobs;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail; 

use Illuminate\Support\Facades\Log;


use App\Notifications\sendPasswordLink;
use App\Mail\NewSendComplaintInfluencer;

class NewComplaintInfluencer implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
 
 
    /**
     * Create a new job instance.
     *
     * @return void
     */
 
    public $influencer;
    public $complaint;
    public $user;
    public $invoice;

    public function __construct($influencer,$user,$invoice, $complaint)
    {
        $this->user = $user;
        $this->influencer = $influencer;
        $this->invoice = $invoice;
        $this->complaint = $complaint;

    }


    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \Log::info('Mail dispatch to ComplaintInfluencer '.$this->influencer->email);   
        Mail::to($this->influencer->email)->send(new NewSendComplaintInfluencer($this->influencer,$this->user,$this->invoice,$this->complaint));
        return 1;
    }
}
