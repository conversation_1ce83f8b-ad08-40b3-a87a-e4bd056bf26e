<?php

namespace App\Jobs;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail; 

use Illuminate\Support\Facades\Log;


use App\Notifications\sendPasswordLink;
use App\Mail\NewSendcampaignSuccessfullyCompleted;

class NewcampaignSuccessfullyCompleted implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
 
 
    /**
     * Create a new job instance.
     *
     * @return void
     */
 

    public $user;
    public $influencer;
    public $influencer_request;

    public function __construct($influencer,$user,$influencer_request)
    {
        $this->user = $user;
        $this->influencer = $influencer;
        $this->influencer_request = $influencer_request;
    }


    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \Log::info('Mail dispatch to campaignSuccessfullyCompleted '.$this->user->email);   
        Mail::to($this->user->email)->send(new NewSendcampaignSuccessfullyCompleted($this->influencer,$this->user,$this->influencer_request));
        return 1;
    }
}
