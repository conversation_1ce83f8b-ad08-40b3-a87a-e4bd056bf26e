<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Jobs\ComputeAverageReachJob;
use App\Jobs\UpdateInfluencerDataFromSocialJob;
use App\Models\Influencer;
use App\Models\InfluencerPrice;
use App\Services\PricingCalculator;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use romanzipp\QueueMonitor\Traits\IsMonitored;
use Throwable;
use App\Constants\CampaignType;

class RepriceInfluencerJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, IsMonitored;

    public int $influencerId;

    public function __construct(int $influencerId)
    {
        $this->influencerId = $influencerId;
    }

    public function handle(PricingCalculator $calculator): void
    {
        $influencer = Influencer::find($this->influencerId);
        
        if (!$influencer) {
            Log::warning('RepriceInfluencerJob: Influencer not found', [
                'influencer_id' => $this->influencerId
            ]);
            return;
        }

        try {
            Log::info('RepriceInfluencerJob: Starting reprice for influencer', [
                'influencer_id' => $influencer->id
            ]);

            // Run synchronously to ensure social performance is computed before pricing
            (new UpdateInfluencerDataFromSocialJob($influencer->id))->handle();

            // NOTE: Skipping ComputeAverageReachJob because UpdateInfluencerDataFromSocialJob
            // already calculates reach averages from social_posts data. Running ComputeAverageReachJob
            // would overwrite good data with null values if ig_posts table is incomplete.
            // (new ComputeAverageReachJob($influencer->id))->handle(); // DISABLED

            foreach (CampaignType::all() as $campaignType) {
                $data = $calculator->calculatePrice($influencer, $campaignType);

                $priceRecord = InfluencerPrice::updateOrCreate(
                    ['influencer_id' => $influencer->id, 'campaign_type' => $campaignType],
                    [
                        'post_type' => $data['breakdown']['post_type'],
                        'price' => $data['price'],
                        'breakdown' => $data['breakdown'],
                        'priced_at' => now(),
                    ]
                );

                // Detailed structured log for observability / debugging
                $bd = $data['breakdown'];
                Log::info('RepriceInfluencerJob: influencer price stored', [
                    'influencer_id' => $influencer->id,
                    'campaign_type' => $campaignType,
                    'post_type' => $bd['post_type'] ?? null,
                    'followers' => $bd['followers'] ?? null,
                    'tier_cpt' => $bd['tier']['cpt'] ?? null,
                    'tier_rate' => $bd['tier']['rate'] ?? null,
                    'gamification_percentage' => $bd['gamification_percentage'] ?? null,
                    'estimated_reach' => $bd['estimated_reach'] ?? null,
                    'avg_reach' => $bd['avg_reach'] ?? null,
                    'reach_multiplier' => $bd['reach_multiplier'] ?? null,
                    'price' => $data['price'],
                    'record_id' => $priceRecord->id,
                    'created' => $priceRecord->wasRecentlyCreated,
                    'priced_at' => $priceRecord->priced_at?->toDateTimeString(),
                ]);
            }

            Log::info('RepriceInfluencerJob: Successfully completed reprice for influencer', [
                'influencer_id' => $influencer->id
            ]);

        } catch (Throwable $e) {
            Log::error('RepriceInfluencerJob: pricing failed for influencer', [
                'influencer_id' => $influencer->id,
                'followers' => $influencer->followers ?? null,
                'gamification_percentage' => $influencer->gamification_percentage ?? null,
                'flag_story_insight_missing' => $influencer->flag_story_insight_missing ?? null,
                'flag_reel_insight_missing' => $influencer->flag_reel_insight_missing ?? null,
                'flag_feed_insight_missing' => $influencer->flag_feed_insight_missing ?? null,
                'admin_override_show_hidden' => $influencer->admin_override_show_hidden ?? null,
                'exception_class' => get_class($e),
                'exception_message' => $e->getMessage(),
                'exception_code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'stacktrace' => $e->getTraceAsString(),
            ]);
            
            throw $e; // Re-throw to mark job as failed
        }
    }
}
