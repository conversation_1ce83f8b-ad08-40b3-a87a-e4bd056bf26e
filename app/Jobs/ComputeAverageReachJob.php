<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Constants\PostType;
use App\Models\Influencer;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use romanzipp\QueueMonitor\Traits\IsMonitored;
use Illuminate\Support\Carbon;

/**
 * Job to compute average reach for an influencer based on their social media posts.
 *
 * This job analyzes Instagram posts from the last 60 days and calculates trimmed averages
 * to provide more accurate reach metrics by removing statistical outliers.
 *
 * Statistical Outlier Removal (Trimming):
 * - Uses 20% trimming (removes 10% from each end of sorted data)
 * - Helps eliminate viral posts and very low engagement posts from averages
 * - Provides more representative metrics for typical influencer performance
 *
 * Example: For 100 posts, removes 10 lowest + 10 highest, uses middle 80 for average
 *
 * Trimming Process:
 * 1. Sort reach values from lowest to highest
 * 2. Calculate trim amount: floor(count * 0.2)
 * 3. Remove bottom 10% and top 10% of values
 * 4. Calculate average from remaining middle 80% of data
 *
 * This ensures calculated averages represent typical performance rather than
 * being skewed by exceptional viral posts or unusually low engagement.
 *
 * @package App\Jobs
 * <AUTHOR> Development Team
 */
class ComputeAverageReachJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, IsMonitored;

    /**
     * Minimum number of story posts required to calculate average reach
     */
    const STORY_THRESHOLD = 15;

    /**
     * Minimum number of reel posts required to calculate average reach
     */
    const REEL_THRESHOLD = 5;

    /**
     * Minimum number of feed posts required to calculate average reach
     */
    const POST_THRESHOLD = 5;

    /**
     * Number of days to look back for post analysis
     */
    const ANALYSIS_PERIOD_DAYS = 60;

    /**
     * Trimming percentage for outlier removal (0.2 = 20% total, 10% from each end)
     * This removes extreme values to get more representative averages
     */
    const TRIM_PERCENTAGE = 0.2;

    /**
     * Multiplier for calculating total trimmed values (both ends)
     * Used in: $count - (TRIM_MULTIPLIER * $trim)
     */
    const TRIM_MULTIPLIER = 2;

    /**
     * Create a new job instance.
     *
     * @param int $influencerId The ID of the influencer to compute average reach for
     */
    public function __construct(private int $influencerId)
    {
    }

    /**
     * Execute the job to compute average reach for the influencer.
     *
     * Process:
     * 1. Fetch posts from the last 60 days for each post type (story, reel, feed)
     * 2. Check if minimum threshold is met for reliable statistics
     * 3. Apply statistical trimming to remove outliers:
     *    - Sort reach values from lowest to highest
     *    - Calculate trim amount: floor(count * 0.2) = 20% total trimming
     *    - Remove bottom 10% and top 10% of values (outliers)
     *    - Calculate average from remaining middle 80% of data
     * 4. Update influencer's average reach fields
     *
     * Trimming Example:
     * - 100 posts: Remove 10 lowest + 10 highest = Use middle 80 posts
     * - 50 posts: Remove 5 lowest + 5 highest = Use middle 40 posts
     * - 20 posts: Remove 2 lowest + 2 highest = Use middle 16 posts
     *
     * This approach ensures averages represent typical performance rather than
     * being skewed by viral posts (extremely high reach) or failed posts (very low reach).
     *
     * @return void
     */
    public function handle(): void
    {
        $influencerPerformance = Influencer::find($this->influencerId);
        if (!$influencerPerformance) {
            return;
        }

        $since = Carbon::now()->subDays(self::ANALYSIS_PERIOD_DAYS);
        $postTypes = [
            PostType::STORY => ['threshold' => self::STORY_THRESHOLD, 'field' => 'ig_story_avg_reach', 'flag' => 'flag_story_insight_missing'],
            PostType::REEL  => ['threshold' => self::REEL_THRESHOLD, 'field' => 'ig_reel_avg_reach', 'flag' => 'flag_reel_insight_missing'],
            PostType::FEED_POST  => ['threshold' => self::POST_THRESHOLD, 'field' => 'ig_feed_avg_reach', 'flag' => 'flag_feed_insight_missing'],
        ];

        foreach ($postTypes as $type => $meta) {
            $reaches = $influencerPerformance->igPosts()
                ->where('post_type', $type)
                ->where('posted_at', '>=', $since)
                ->pluck('reach')
                ->sort()
                ->values();

            // Debug logging for reel post type specifically
            if ($type === PostType::REEL) {
                \Log::info('ComputeAverageReachJob: Processing reel posts', [
                    'influencer_id' => $this->influencerId,
                    'post_type' => $type,
                    'posts_found' => $reaches->count(),
                    'threshold_required' => $meta['threshold'],
                    'reaches_sample' => $reaches->take(10)->toArray(),
                    'since_date' => $since->toDateTimeString(),
                ]);
            }

            if ($reaches->count() < $meta['threshold']) {
                $influencerPerformance->{$meta['field']} = null;
                $influencerPerformance->{$meta['flag']} = true;

                // Debug logging for insufficient data
                if ($type === PostType::REEL) {
                    \Log::warning('ComputeAverageReachJob: Insufficient reel posts for average calculation', [
                        'influencer_id' => $this->influencerId,
                        'posts_found' => $reaches->count(),
                        'threshold_required' => $meta['threshold'],
                        'field_set_to_null' => $meta['field'],
                        'flag_set_to_true' => $meta['flag'],
                    ]);
                }
                continue;
            }

            $count = $reaches->count();

            // Statistical trimming: Remove outliers (20% total: 10% from each end)
            $trim = (int) floor($count * self::TRIM_PERCENTAGE); // Amount to trim from each end
            if ($trim > 0) {
                // slice($start, $length): Skip first $trim, take middle portion
                // $count - (2 * $trim) = total - (removed_from_start + removed_from_end)
                $reaches = $reaches->slice($trim, $count - (self::TRIM_MULTIPLIER * $trim));
            }

            // Calculate average from trimmed data (middle 80% of values)
            $average = (int) round($reaches->avg());
            $influencerPerformance->{$meta['field']} = $average;
            $influencerPerformance->{$meta['flag']} = false;

            // Debug logging for reel calculations
            if ($type === PostType::REEL) {
                \Log::info('ComputeAverageReachJob: Calculated reel average reach', [
                    'influencer_id' => $this->influencerId,
                    'post_type' => $type,
                    'total_posts' => $count,
                    'trim_amount' => $trim,
                    'posts_after_trimming' => $reaches->count(),
                    'calculated_average' => $average,
                    'field_updated' => $meta['field'],
                    'reaches_after_trim_sample' => $reaches->take(10)->toArray(),
                ]);
            }
        }

        $influencerPerformance->avg_reach_computed_at = Carbon::now();
        $influencerPerformance->save();
    }
}
