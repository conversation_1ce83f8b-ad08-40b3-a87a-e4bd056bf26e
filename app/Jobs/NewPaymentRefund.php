<?php

namespace App\Jobs;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

use Illuminate\Support\Facades\Log;


use App\Notifications\sendPasswordLink;
use App\Mail\NewSendPaymentRefund;

class NewPaymentRefund implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    /**
     * Create a new job instance.
     *
     * @return void
     */

    public $user;
    public $detail;
    public $influencer;

    public function __construct($user,$detail,$influencer)
    {
        $this->user = $user;
        $this->detail = $detail;
        $this->influencer = $influencer;
    }


    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \Log::info('Mail dispatch to PaymentRefund '.$this->user->email);
        Mail::to($this->user->email)->send(new NewSendPaymentRefund($this->user,$this->detail, $this->influencer));
        return 1;
    }
}
