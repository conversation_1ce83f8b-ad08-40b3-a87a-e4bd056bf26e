<?php

namespace App\Jobs;

use App\Mail\NewSubmitPhaseEnded as MailNewSubmitPhaseEnded;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class NewSubmitPhaseEnded implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $influencer;
    public $row;
    public function __construct($influencer, $row)
    {
        $this->influencer = $influencer;
        $this->row = $row;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \Log::info('Mail dispatch to submit phase ended '.$this->influencer->email);   
        Mail::to($this->influencer->email)->send(new MailNewSubmitPhaseEnded($this->influencer,$this->row));
        return 1;
    }
}
