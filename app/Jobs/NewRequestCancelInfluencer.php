<?php

namespace App\Jobs;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use romanzipp\QueueMonitor\Traits\IsMonitored;
use Illuminate\Support\Facades\Mail; 

use Illuminate\Support\Facades\Log;


use App\Notifications\sendPasswordLink;
use App\Mail\NewSendRequestCancelInfluencer;

class NewRequestCancelInfluencer implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, IsMonitored;

    /**
     * Create a new job instance.
     *
     * @return void
     */
 
    public $influencer;
    public $formData;

    public $user;

    public function __construct($influencer,$user,$formData)
    {
        $this->user = $user;
        $this->influencer = $influencer;
        $this->formData = $formData;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Mail::to($this->influencer->email)
            ->send(
                new NewSendRequestCancelInfluencer($this->influencer, $this->user, $this->formData)
            );
        \Log::info('Mail dispatch to RequestCancelInfluencer ' . $this->influencer->email);
        return 1;
    }
}
