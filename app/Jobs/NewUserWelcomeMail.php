<?php

namespace App\Jobs;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail; 

use Illuminate\Support\Facades\Log;

 
use App\Mail\RegistrationUserNew;


class NewUserWelcomeMail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $user;
 
    /**
     * Create a new job instance.
     *
     * @return void
     */
    
    public function __construct($user)
    {
        $this->user = $user;
    }
 

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \Log::info('Mail dispatch to RegistrationUserNew '.$this->user->email);   
        Mail::to($this->user->email)->send(new RegistrationUserNew($this->user));
        return 1;
    }
}
