<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Influencer;
use App\Config\PricingConfig;
use App\Support\CampaignPostType;
use App\Constants\PostType;

class PricingCalculator
{
    /**
     * @return array{price: float, breakdown: array}
     */
    public function calculatePrice(Influencer $inf, string $campaignType): array
    {
        $postType = CampaignPostType::forCampaignType($campaignType);
        $tiers = $postType === PostType::REEL ? PricingConfig::reelTiers() : PricingConfig::storyTiers();

        $tier = collect($tiers)->first(function ($tier) use ($inf) {
            $min = $tier['min'];
            $max = $tier['max'];
            return $inf->followers >= $min && ($max === null || $inf->followers < $max);
        });

        if (!$tier) {
            throw new \RuntimeException("No matching pricing tier found for influencer with {$inf->followers} followers and post type '{$postType}'.");
        }

        $base = $tier['cpt'] * ($inf->followers / 1000);
        $base *= (1 + $inf->gamification_percentage);

        $estimated = $inf->followers * $tier['rate'];
        $avgReach = match ($postType) {
            PostType::STORY => $inf->ig_story_avg_reach,
            PostType::REEL => $inf->ig_reel_avg_reach,
            PostType::FEED_POST => $inf->ig_feed_avg_reach,
        };

        $flagMissing = match ($postType) {
            PostType::STORY => $inf->flag_story_insight_missing,
            PostType::REEL => $inf->flag_reel_insight_missing,
            PostType::FEED_POST => $inf->flag_feed_insight_missing,
        };

        $reachMultiplier = null;
        if (!$flagMissing && $avgReach && $estimated > 0) {
            $reachMultiplier = $avgReach / $estimated;
            $base *= $reachMultiplier;
        }

        return [
            'price' => round($base, 2),
            'breakdown' => [
                'followers' => $inf->followers,
                'tier' => $tier,
                'gamification_percentage' => $inf->gamification_percentage,
                'estimated_reach' => $estimated,
                'avg_reach' => $avgReach,
                'reach_multiplier' => $reachMultiplier,
                'post_type' => $postType,
                'campaign_type' => $campaignType,
            ],
        ];
    }
}
