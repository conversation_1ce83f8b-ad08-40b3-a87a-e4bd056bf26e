<?php

namespace App\Services;

use App\Models\SocialPost;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class AverageReachCalculatorService
{
    /**
     * Campaign type to post type mapping
     */
    const CAMPAIGN_TYPE_TO_POST_TYPE = [
        'Boost me' => 'story',
        'Survey' => 'story', 
        'Reaction video' => 'reel'
    ];

    /**
     * Calculate average reach for an influencer by campaign type
     *
     * @param int $userId
     * @param string $campaignType ('Boost me', 'Survey', 'Reaction video')
     * @return int Average reach value, 0 if no posts found
     */
    public function calculateAverageReachByCampaignType(int $userId, string $campaignType): int
    {
        $postType = self::CAMPAIGN_TYPE_TO_POST_TYPE[$campaignType] ?? null;
        
        if (!$postType) {
            Log::warning("Unknown campaign type: {$campaignType}");
            return 0;
        }

        return $this->calculateAverageReachByPostType($userId, $postType);
    }

    /**
     * Calculate average reach for an influencer by post type
     *
     * @param int $userId
     * @param string $postType ('story', 'reel', 'post')
     * @return int Average reach value, 0 if no posts found
     */
    public function calculateAverageReachByPostType(int $userId, string $postType): int
    {
        // Get posts from last 24 hours
        $since = Carbon::now()->subHours(24);
        
        $posts = SocialPost::where('user_id', $userId)
            ->where('post_category', $postType)
            ->where('published_at', '>=', $since)
            ->get();

        if ($posts->isEmpty()) {
            return 0;
        }

        $totalReach = 0;
        $validPostsCount = 0;

        foreach ($posts as $post) {
            $reach = $post->reach; // Uses the accessor that prioritizes insights data
            
            if ($reach > 0) {
                $totalReach += $reach;
                $validPostsCount++;
            }
        }

        if ($validPostsCount === 0) {
            return 0;
        }

        return (int) round($totalReach / $validPostsCount);
    }

    /**
     * Get average reach for all post types for an influencer
     *
     * @param int $userId
     * @return array ['story' => int, 'reel' => int, 'post' => int]
     */
    public function calculateAverageReachForAllPostTypes(int $userId): array
    {
        return [
            'story' => $this->calculateAverageReachByPostType($userId, 'story'),
            'reel' => $this->calculateAverageReachByPostType($userId, 'reel'),
            'post' => $this->calculateAverageReachByPostType($userId, 'post'),
        ];
    }

    /**
     * Check if influencer has any posts of given type in last 24 hours
     *
     * @param int $userId
     * @param string $postType
     * @return bool
     */
    public function hasRecentPostsOfType(int $userId, string $postType): bool
    {
        $since = Carbon::now()->subHours(24);
        
        return SocialPost::where('user_id', $userId)
            ->where('post_category', $postType)
            ->where('published_at', '>=', $since)
            ->exists();
    }

    /**
     * Get detailed reach statistics for debugging
     *
     * @param int $userId
     * @param string $postType
     * @return array
     */
    public function getReachStatistics(int $userId, string $postType): array
    {
        $since = Carbon::now()->subHours(24);
        
        $posts = SocialPost::where('user_id', $userId)
            ->where('post_category', $postType)
            ->where('published_at', '>=', $since)
            ->get();

        $reaches = [];
        $totalReach = 0;
        $validPostsCount = 0;

        foreach ($posts as $post) {
            $reach = $post->reach;
            $reaches[] = [
                'post_id' => $post->post_id,
                'reach' => $reach,
                'published_at' => $post->published_at->toDateTimeString(),
                'has_insights' => !empty($post->insights),
            ];
            
            if ($reach > 0) {
                $totalReach += $reach;
                $validPostsCount++;
            }
        }

        return [
            'post_type' => $postType,
            'total_posts' => $posts->count(),
            'valid_posts_count' => $validPostsCount,
            'total_reach' => $totalReach,
            'average_reach' => $validPostsCount > 0 ? (int) round($totalReach / $validPostsCount) : 0,
            'posts' => $reaches,
            'time_range' => [
                'since' => $since->toDateTimeString(),
                'until' => Carbon::now()->toDateTimeString(),
            ],
        ];
    }
}
