<?php

declare(strict_types=1);

namespace App\Config;

class PricingConfig
{
    public static function storyTiers(): array
    {
        return [
            ['min' => 1000, 'max' => 10000, 'rate' => 0.06, 'cpt' => 5.00],
            ['min' => 10000, 'max' => 50000, 'rate' => 0.05, 'cpt' => 4.50],
            ['min' => 50000, 'max' => 500000, 'rate' => 0.04, 'cpt' => 3.00],
            ['min' => 500000, 'max' => 1000000, 'rate' => 0.03, 'cpt' => 2.50],
            ['min' => 1000000, 'max' => null, 'rate' => 0.02, 'cpt' => 2.00],
        ];
    }

    public static function reelTiers(): array
    {
        return [
            ['min' => 1000, 'max' => 10000, 'rate' => 0.11, 'cpt' => 14.00],
            ['min' => 10000, 'max' => 50000, 'rate' => 0.10, 'cpt' => 13.00],
            ['min' => 50000, 'max' => 500000, 'rate' => 0.09, 'cpt' => 12.00],
            ['min' => 500000, 'max' => 1000000, 'rate' => 0.08, 'cpt' => 11.00],
            ['min' => 1000000, 'max' => null, 'rate' => 0.07, 'cpt' => 10.00],
        ];
    }
}
