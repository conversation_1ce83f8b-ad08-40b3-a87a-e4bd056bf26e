<?php

declare(strict_types=1);

namespace App\Constants;

/**
 * Campaign Status Constants
 * 
 * Centralized constants for campaign statuses throughout the application.
 * 
 * Usage: CampaignStatus::ACTIVE
 * 
 * @package App\Constants
 */
class CampaignStatus
{
    /**
     * Draft campaign status - campaign is being created
     */
    const DRAFT = 'draft';
    
    /**
     * Active campaign status - campaign is live and accepting applications
     */
    const ACTIVE = 'active';
    
    /**
     * Paused campaign status - temporarily stopped
     */
    const PAUSED = 'paused';
    
    /**
     * Completed campaign status - campaign has finished
     */
    const COMPLETED = 'completed';
    
    /**
     * Cancelled campaign status - campaign was cancelled
     */
    const CANCELLED = 'cancelled';
    
    /**
     * Expired campaign status - campaign deadline passed
     */
    const EXPIRED = 'expired';
    
    /**
     * Get all campaign statuses as an array
     * 
     * @return array<string> Array of all campaign status constants
     */
    public static function all(): array
    {
        return [
            self::DRAFT,
            self::ACTIVE,
            self::PAUSED,
            self::COMPLETED,
            self::CANCELLED,
            self::EXPIRED,
        ];
    }
    
    /**
     * Get active statuses (campaigns that are still running)
     * 
     * @return array<string> Array of active campaign statuses
     */
    public static function active(): array
    {
        return [
            self::ACTIVE,
            self::PAUSED,
        ];
    }
    
    /**
     * Get finished statuses (campaigns that are no longer active)
     * 
     * @return array<string> Array of finished campaign statuses
     */
    public static function finished(): array
    {
        return [
            self::COMPLETED,
            self::CANCELLED,
            self::EXPIRED,
        ];
    }
    
    /**
     * Get status display names with colors for UI
     * 
     * @return array<string, array<string, string>> Array with status info
     */
    public static function displayInfo(): array
    {
        return [
            self::DRAFT => ['name' => 'Draft', 'color' => 'secondary', 'icon' => 'edit'],
            self::ACTIVE => ['name' => 'Active', 'color' => 'success', 'icon' => 'play'],
            self::PAUSED => ['name' => 'Paused', 'color' => 'warning', 'icon' => 'pause'],
            self::COMPLETED => ['name' => 'Completed', 'color' => 'primary', 'icon' => 'check'],
            self::CANCELLED => ['name' => 'Cancelled', 'color' => 'danger', 'icon' => 'times'],
            self::EXPIRED => ['name' => 'Expired', 'color' => 'dark', 'icon' => 'clock'],
        ];
    }
    
    /**
     * Check if a campaign status is valid
     * 
     * @param string $status The campaign status to validate
     * @return bool True if valid, false otherwise
     */
    public static function isValid(string $status): bool
    {
        return in_array($status, self::all(), true);
    }
}
