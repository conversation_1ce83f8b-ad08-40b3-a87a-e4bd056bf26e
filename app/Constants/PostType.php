<?php

declare(strict_types=1);

namespace App\Constants;

/**
 * Social Media Post Type Constants
 * 
 * Centralized constants for Instagram post types used in analytics and reach calculations.
 * 
 * Usage: PostType::STORY
 * 
 * @package App\Constants
 */
class PostType
{
    /**
     * Instagram Story post type
     */
    const STORY = 'story';
    
    /**
     * Instagram Reel post type
     */
    const REEL = 'reel';
    
    /**
     * Instagram Feed Post type (appears in main feed)
     * Using Instagram's official terminology: "post" for feed content
     * Note: This is different from the generic action of "posting" content
     */
    const FEED_POST = 'post';
    const POST = 'post';  // Alias for backward compatibility
    const FEED = 'post';  // Alias for backward compatibility
    
    /**
     * Get all post types as an array
     * 
     * @return array<string> Array of all post type constants
     */
    public static function all(): array
    {
        return [
            self::STORY,
            self::REEL,
            self::FEED_POST,
        ];
    }
    
    /**
     * Get post type display names
     * 
     * @return array<string, string> Array with types as keys and display names as values
     */
    public static function displayNames(): array
    {
        return [
            self::STORY => 'Instagram Story',
            self::REEL => 'Instagram Reel',
            self::FEED_POST => 'Instagram Feed Post',
        ];
    }
    
    /**
     * Get post types that support reach analytics
     * 
     * @return array<string> Array of post types with reach analytics
     */
    public static function withReachAnalytics(): array
    {
        return [
            self::STORY,
            self::REEL,
            self::FEED_POST,
        ];
    }
    
    /**
     * Check if a post type is valid
     * 
     * @param string $type The post type to validate
     * @return bool True if valid, false otherwise
     */
    public static function isValid(string $type): bool
    {
        return in_array($type, self::all(), true);
    }
}
