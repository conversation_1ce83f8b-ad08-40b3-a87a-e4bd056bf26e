<?php

declare(strict_types=1);

namespace App\Constants;

/**
 * User Type Constants
 * 
 * Centralized constants for all user types in the application.
 * 
 * Usage: UserType::ADMIN
 * 
 * @package App\Constants
 */
class UserType
{
    /**
     * Administrator user type
     */
    const ADMIN = 'admin';
    
    /**
     * Regular user type
     */
    const USER = 'user';
    
    /**
     * Influencer user type
     */
    const INFLUENCER = 'influencer';
    
    /**
     * Brand user type
     */
    const BRAND = 'brand';
    
    /**
     * Get all user types as an array
     * 
     * @return array<string> Array of all user type constants
     */
    public static function all(): array
    {
        return [
            self::ADMIN,
            self::USER,
            self::INFLUENCER,
            self::BRAND,
        ];
    }
    
    /**
     * Check if a user type is valid
     * 
     * @param string $type The user type to validate
     * @return bool True if valid, false otherwise
     */
    public static function isValid(string $type): bool
    {
        return in_array($type, self::all(), true);
    }
    
    /**
     * Get user type display names
     * 
     * @return array<string, string> Array with types as keys and display names as values
     */
    public static function displayNames(): array
    {
        return [
            self::ADMIN => 'Administrator',
            self::USER => 'Regular User',
            self::INFLUENCER => 'Influencer',
            self::BRAND => 'Brand',
        ];
    }
}
