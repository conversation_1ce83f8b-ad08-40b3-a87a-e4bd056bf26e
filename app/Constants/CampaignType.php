<?php

declare(strict_types=1);

namespace App\Constants;

/**
 * Campaign Type Constants
 * 
 * Centralized constants for all campaign types used throughout the application.
 * This ensures consistency and makes it easy to update campaign type names.
 * 
 * Usage: CampaignType::BOOST_ME
 * 
 * @package App\Constants
 */
class CampaignType
{
    const BOOST_ME = 'Boost Me';
    const REACTION_VIDEO = 'Reaction Video';
    const SURVEY = 'Survey';

    const CAMPAIGN_TYPE_POST_TYPE_MAP = [
        self::BOOST_ME => 'Story',
        self::REACTION_VIDEO => 'Reel',
        self::SURVEY => 'Post',
    ];
    
    /**
     * Get all campaign types as an array
     * 
     * @return array<string> Array of all campaign type constants
     */
    public static function all(): array
    {
        return [
            self::BOOST_ME,
            self::REACTION_VIDEO,
            self::SURVEY,
        ];
    }
    
    /**
     * Get all campaign types as key-value pairs (for dropdowns)
     * 
     * @return array<string, string> Array with constant names as keys and values as values
     */
    public static function options(): array
    {
        return [
            'BOOST_ME' => self::BOOST_ME,
            'REACTION_VIDEO' => self::REACTION_VIDEO,
            'SURVEY' => self::SURVEY,
        ];
    }
    
    /**
     * Check if a campaign type is valid
     * 
     * @param string $type The campaign type to validate
     * @return bool True if valid, false otherwise
     */
    public static function isValid(string $type): bool
    {
        return in_array($type, self::all(), true);
    }
}
