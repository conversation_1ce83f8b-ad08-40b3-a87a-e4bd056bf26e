<?php

declare(strict_types=1);

namespace App\Constants;

/**
 * Payment Status Constants
 * 
 * Centralized constants for payment statuses in the application.
 * 
 * Usage: PaymentStatus::PENDING
 * 
 * @package App\Constants
 */
class PaymentStatus
{
    /**
     * Payment is pending processing
     */
    const PENDING = 'pending';
    
    /**
     * Payment is being processed
     */
    const PROCESSING = 'processing';
    
    /**
     * Payment completed successfully
     */
    const COMPLETED = 'completed';
    
    /**
     * Payment failed
     */
    const FAILED = 'failed';
    
    /**
     * Payment was cancelled
     */
    const CANCELLED = 'cancelled';
    
    /**
     * Payment was refunded
     */
    const REFUNDED = 'refunded';
    
    /**
     * Payment is on hold for review
     */
    const ON_HOLD = 'on_hold';
    
    /**
     * Get all payment statuses as an array
     * 
     * @return array<string> Array of all payment status constants
     */
    public static function all(): array
    {
        return [
            self::PENDING,
            self::PROCESSING,
            self::COMPLETED,
            self::FAILED,
            self::CANCELLED,
            self::REFUNDED,
            self::ON_HOLD,
        ];
    }
    
    /**
     * Get successful payment statuses
     * 
     * @return array<string> Array of successful payment statuses
     */
    public static function successful(): array
    {
        return [
            self::COMPLETED,
        ];
    }
    
    /**
     * Get failed payment statuses
     * 
     * @return array<string> Array of failed payment statuses
     */
    public static function failed(): array
    {
        return [
            self::FAILED,
            self::CANCELLED,
        ];
    }
    
    /**
     * Get pending payment statuses
     * 
     * @return array<string> Array of pending payment statuses
     */
    public static function pending(): array
    {
        return [
            self::PENDING,
            self::PROCESSING,
            self::ON_HOLD,
        ];
    }
    
    /**
     * Check if a payment status is valid
     * 
     * @param string $status The payment status to validate
     * @return bool True if valid, false otherwise
     */
    public static function isValid(string $status): bool
    {
        return in_array($status, self::all(), true);
    }
}
