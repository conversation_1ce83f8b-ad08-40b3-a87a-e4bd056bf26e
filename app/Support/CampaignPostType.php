<?php

declare(strict_types=1);

namespace App\Support;

use InvalidArgumentException;

class CampaignPostType
{
    public static function forCampaignType(string $campaignType): string
    {
        // Normalize the campaign type to handle case variations
        $normalizedType = trim($campaignType);

        return match ($normalizedType) {
            'Reaction Video', 'Reaction video' => 'reel',
            'Survey', 'Boost Me', 'Boost me' => 'story', // Handle both "Boost Me" and "Boost me"
            default => throw new InvalidArgumentException("Unknown campaign type: {$campaignType}"),
        };
    }
}
