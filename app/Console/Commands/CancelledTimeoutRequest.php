<?php

/**
 * After a campaign has been created and request to influencers are sent, they have 3
 * days to accept the request. If they don't accept the request in 3 days, the campaign
 * shall be automatically cancelled.
 * 
 * This cronjob cancels all campaigns that has 3 days passed after creation and no 
 * influencer has accepted it.
 */

namespace App\Console\Commands;

use App\Models\InfluencerRequestDetail;
use App\Models\InfluencerRequestAccept;
use Socialite;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;
use App\Models\User;

use App\Models\SocialKeys;
use App\Jobs\NewnoInfluencerHasAcceptedYourRequest;
use App\Models\Campaign;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CancelledTimeoutRequest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cancelled:request';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cancelled Timeout Request';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $influencerDataItems = InfluencerRequestDetail::join(
                'influencer_details',
                'influencer_request_details.influencer_detail_id', '=',
                'influencer_details.id'
            )
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->leftjoin(
                'influencer_request_accepts',
                'influencer_request_accepts.influencer_request_detail_id', '=',
                'influencer_request_details.id'
            )
            ->select(
                'influencer_request_details.*',
                'influencer_details.id as i_id',
                'influencer_details.user_id as i_user_id',
                'influencer_request_accepts.request',
                'influencer_request_accepts.request_time',
                'influencer_request_accepts.id as influencer_request_accept_id'
            )
            ->get();

        $campaign_id = [];
        foreach ($influencerDataItems as $influencerData) {
            // for not accepted influencers requests
            $created_date = date('Y-m-d', strtotime($influencerData->created_at));
            $campaignDate = date('Y-m-d', strtotime($created_date . ' + 3 days'));
            $date = date('Y-m-d');
            $seconds = strtotime($campaignDate) - strtotime($date);
            $days = floor($seconds / 86400);

            var_dump($influencerData->compaign_id);
            var_dump($days);
            var_dump($days < 3 && $days >= 0);
            var_dump('======================');
            continue;

            if ($days < 3 && $days >= 0) {
                $accept = InfluencerRequestAccept::where('user_id', $influencerData->i_user_id)->where('influencer_request_detail_id', $influencerData->id)->first();
                if ($accept == null) {
                    InfluencerRequestAccept::create(
                        [
                            'user_id' => $influencerData->i_user_id,
                            'influencer_request_detail_id' =>  $influencerData->id,
                            'status' => 0
                        ]
                    );

                    InfluencerRequestDetail::where('compaign_id', $influencerData->compaign_id)
                        ->where('influencer_detail_id', $influencerData->i_id)
                        ->update(['status' => 'Cancelled']);
                }

                if (!in_array($influencerData->compaign_id, $campaign_id)) {
                    $accept = InfluencerRequestAccept::where('user_id', $influencerData->i_user_id)->where('influencer_request_detail_id', $influencerData->id)->first();
                    if ($accept != null && $accept->staus != 1 && (isset($accept->request) && $accept->request != "") && $accept->request < 1) {
                        $customer =  User::where('id', $accept->user_id)->first();
                        dispatch(new NewnoInfluencerHasAcceptedYourRequest($customer, $influencerData));
                    }
                    array_push($campaign_id, $influencerData->compaign_id);
                }

                // update campaign total amount
                $CampaignInfluencers =  InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')
                    ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
                    ->select(
                        'influencer_request_details.*',
                        'influencer_details.id as i_id',
                        'influencer_details.user_id as i_user_id',
                        'influencer_request_accepts.request',
                        'influencer_request_accepts.id as influencer_request_accept_id',
                    )
                    ->where('influencer_request_details.status', NULL)
                    ->where('influencer_request_details.compaign_id', $influencerData->compaign_id)
                    ->where('influencer_request_accepts.request', 1)
                    ->get();

                $total_VAT = 0;
                $total_amount = 0;
                foreach ($CampaignInfluencers as $CampaignInfluencer) {
                    if (
                        (isset($CampaignInfluencer->influencer_request_accepts->request) &&
                            $CampaignInfluencer->influencer_request_accepts->request == 1) ||
                        !isset($CampaignInfluencer->influencer_request_accepts->request)
                    ) {
                        $total_amount = $total_amount + $CampaignInfluencer->discount_price;

                        $user = User::find(
                            $CampaignInfluencer->influencerdetails->user->id,
                        );

                        $VAT_value = $user->is_small_business_owner
                            ? 0
                            : $CampaignInfluencer->discount_price * 0.19;

                        $total_VAT += $VAT_value;
                    }
                }
                $platform_fee = ($total_amount * 5) / 100;
                if ($platform_fee < 2) {
                    $platform_fee = 2;
                }
                $total_VAT = $total_VAT + $platform_fee * 0.19;

                $total_campaign_amount = $total_amount + $platform_fee + $total_VAT;

                Campaign::where('campaign_id', $influencerData->compaign_id)->update(['total_amount' => $total_campaign_amount]);
            }
        }
    }
}
