<?php

namespace App\Console\Commands;

use App\Models\InfluencerRequestDetail;
use App\Models\InfluencerRequestAccept;
use Socialite;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;
use App\Models\User;

use App\Models\SocialKeys;
use App\Jobs\NewnoInfluencerHasAcceptedYourRequest;
use App\Models\Campaign;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CancelledTimeoutRequest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cancelled:request';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cancelled Timeout Request';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $records =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
            ->select('influencer_request_details.*', 'influencer_details.id as i_id', 'influencer_details.user_id as i_user_id', 'influencer_request_accepts.request', 'influencer_request_accepts.request_time', 'influencer_request_accepts.id as influencer_request_accept_id')
            ->get();
        $campaign_id    = [];
        foreach ($records as $row) {
            //for not accepted influencers requests
            $created_date =  date('Y-m-d', strtotime($row->created_at));
            $campaignDate = date('Y-m-d', strtotime($created_date . ' + 3 days'));
            $date = date('Y-m-d');
            $seconds = strtotime($campaignDate) - strtotime($date);
            $days    = floor($seconds / 86400);

            if ($days < 3 && $days >= 0) {
                $accept =   InfluencerRequestAccept::where('user_id', $row->i_user_id)->where('influencer_request_detail_id', $row->id)->first();
                if ($accept == null) {
                    InfluencerRequestAccept::create(
                        [
                            'user_id' => $row->i_user_id,
                            'influencer_request_detail_id' =>  $row->id,
                            'status' => 0
                        ]
                    );
                    InfluencerRequestDetail::where('compaign_id', $row->compaign_id)->where('influencer_detail_id', $row->i_id)->update(['status' => 'Cancelled']);
                }

                if (!in_array($row->compaign_id, $campaign_id)) {
                    $accept =   InfluencerRequestAccept::where('user_id', $row->i_user_id)->where('influencer_request_detail_id', $row->id)->first();
                    if ($accept != null && $accept->staus != 1 && (isset($accept->request) && $accept->request != "") && $accept->request < 1) {
                        $customer =  User::where('id', $accept->user_id)->first();
                        dispatch(new NewnoInfluencerHasAcceptedYourRequest($customer, $row));
                    }
                    array_push($campaign_id, $row->compaign_id);
                }

                // update campaign total amount
                $CampaignInfluencers =  InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')
                    ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
                    ->select(
                        'influencer_request_details.*',
                        'influencer_details.id as i_id',
                        'influencer_details.user_id as i_user_id',
                        'influencer_request_accepts.request',
                        'influencer_request_accepts.id as influencer_request_accept_id',
                    )
                    ->where('influencer_request_details.status', NULL)
                    ->where('influencer_request_details.compaign_id', $row->compaign_id)
                    ->where('influencer_request_accepts.request', 1)
                    ->get();

                $total_VAT = 0;
                $total_amount = 0;
                // TODO 07.09.2025
                // Why we are calculating the prices here again? We could use the 
                // already calculated price, eg, platform_amount, cash_out_amount and influencer_amount
                foreach ($CampaignInfluencers as $CampaignInfluencer) {
                    if (
                        (isset($CampaignInfluencer->influencer_request_accepts->request) &&
                            $CampaignInfluencer->influencer_request_accepts->request == 1) ||
                        !isset($CampaignInfluencer->influencer_request_accepts->request)
                    ) {
                        if (!isset($CampaignInfluencer->influencer_price)) {
                            throw new \Exception("Influencer price not set for influencer_request_detail_id: {$CampaignInfluencer->id}");
                        }
                        // [example] $CampaignInfluencer->influencer_price = 5
                        $influencerPrice = $CampaignInfluencer->influencer_price;
                        $total_amount = $total_amount + $influencerPrice;

                        $user = User::find(
                            $CampaignInfluencer->influencerdetails->user->id,
                        );

                        $VAT_value = $user->is_small_business_owner
                            ? 0
                            : $influencerPrice * 0.19;

                        $total_VAT += $VAT_value;
                    }
                }
                $platform_fee = ($total_amount * 5) / 100;
                if ($platform_fee < 2) {
                    $platform_fee = 2;
                }
                $total_VAT = $total_VAT + $platform_fee * 0.19;

                $total_campaign_amount = $total_amount + $platform_fee + $total_VAT;

                Campaign::where('campaign_id', $row->compaign_id)->update(['total_amount' => $total_campaign_amount]);
            }
        }
    }
}
