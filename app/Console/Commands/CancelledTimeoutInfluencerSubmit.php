<?php

namespace App\Console\Commands;

use App\Models\InfluencerRequestDetail;
use App\Models\InfluencerRequestAccept;
use Socialite;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;
use App\Models\SocialKeys;
use App\Models\CampaignRequestTime;
use App\Models\User;
use App\Notifications\CancelRefund;

use App\Jobs\NewCancelRefund;
use App\Jobs\NewSubmitPhaseEnded;
use App\Models\AdminGamification;
use App\Models\Statistic;

class CancelledTimeoutInfluencerSubmit extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cancelledinfluencer:submit';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cancelled Timeout Influencer Submit: Influencer did not submit during submit phase';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        \Log::info('CancelledTimeoutInfluencerSubmit command started');

        $campaignRequestTime = CampaignRequestTime::first();
        $influencerRequestDetails = InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
            ->leftjoin('campaigns', 'campaigns.campaign_id', '=', 'influencer_request_details.compaign_id')
            ->with(['influencerdetails', 'invoices']) // Eager load relationships
            ->select('influencer_request_details.*', 'campaigns.has_started', 'campaigns.has_finished', 'influencer_details.id as i_id', 'influencer_details.user_id as i_user_id', 'influencer_request_accepts.request', 'influencer_request_accepts.request_time', 'influencer_request_accepts.id as influencer_request_accept_id', 'influencer_request_accepts.created_at as accept_time')
            ->where('campaigns.has_started', true)
            ->where('campaigns.has_finished', false)
            ->get();

        \Log::info('CancelledTimeoutInfluencerSubmit: Found records to process', [
            'total_records' => $influencerRequestDetails->count()
        ]);
        
        $adminGamification = AdminGamification::where('select_type', 'Point-Rules')->first();

        foreach ($influencerRequestDetails as $influencerRequestDetail) {
            $time = (isset($influencerRequestDetail->request_time_accept) && $influencerRequestDetail->request_time_accept == '1') ? $influencerRequestDetail->request_time + $influencerRequestDetail->time : $influencerRequestDetail->time;

            $campaignDate = date('Y-m-d H:i:s', strtotime($influencerRequestDetail->accept_time ? $influencerRequestDetail->accept_time . ' + ' . $time . ' days' : 0 . ' + ' . $time . ' days'));

            $date = date('Y-m-d H:i:s');
            $seconds = strtotime($campaignDate) - strtotime($date);

            $days = floor($seconds / 86400);
            if ($days < $time && $days > 0) {
                continue;
            }

            if ($influencerRequestDetail->refund_reason  != 'Cancelled' && $influencerRequestDetail->social_post_id == null && $influencerRequestDetail->finish != '0') {
                if (!$influencerRequestDetail->is_paused) {
                    if ($influencerRequestDetail->social_post_id == null) {
                        $influencerRequestDetail->update(['finish' => 0, 'review' => 0]);

                        // Validate required relationships exist before processing
                        if (!$influencerRequestDetail->influencerdetails) {
                            \Log::warning('CancelledTimeoutInfluencerSubmit: Missing influencer details relationship', [
                                'influencer_request_detail_id' => $influencerRequestDetail->id,
                                'campaign_id' => $influencerRequestDetail->compaign_id,
                                'influencer_detail_id' => $influencerRequestDetail->influencer_detail_id
                            ]);
                            continue; // Skip this record and continue with the next one
                        }

                        if (!$influencerRequestDetail->invoices) {
                            \Log::warning('CancelledTimeoutInfluencerSubmit: Missing invoice relationship', [
                                'influencer_request_detail_id' => $influencerRequestDetail->id,
                                'campaign_id' => $influencerRequestDetail->compaign_id,
                                'invoice_id' => $influencerRequestDetail->invoice_id
                            ]);
                            continue; // Skip this record and continue with the next one
                        }

                        $fieldName = $influencerRequestDetail->advertising . '_price';

                        // Safely get the influencer user
                        $influencer = null;
                        if ($influencerRequestDetail->influencerdetails && $influencerRequestDetail->influencerdetails->user_id) {
                            $influencer = User::where('id', $influencerRequestDetail->influencerdetails->user_id)->first();
                        }

                        if (!$influencer) {
                            \Log::warning('CancelledTimeoutInfluencerSubmit: Influencer user not found', [
                                'influencer_request_detail_id' => $influencerRequestDetail->id,
                                'campaign_id' => $influencerRequestDetail->compaign_id,
                                'influencer_detail_id' => $influencerRequestDetail->influencer_detail_id,
                                'expected_user_id' => $influencerRequestDetail->influencerdetails->user_id ?? 'null'
                            ]);
                            continue; // Skip this record and continue with the next one
                        }

                        // Safely get the customer user
                        $customer = User::where('id', $influencerRequestDetail->user_id)->first();
                        if (!$customer) {
                            \Log::warning('CancelledTimeoutInfluencerSubmit: Customer user not found', [
                                'influencer_request_detail_id' => $influencerRequestDetail->id,
                                'campaign_id' => $influencerRequestDetail->compaign_id,
                                'expected_customer_id' => $influencerRequestDetail->user_id
                            ]);
                            continue; // Skip this record and continue with the next one
                        }

                        // Use the new separate charges and transfer system
                        // The original charge ID is stored in refund_txn_id field
                        $originalChargeId = $influencerRequestDetail->refund_txn_id ?? '';

                        // Use the new centralized cancelCampaign method
                        \Log::info('Processing influencer submit timeout using cancelCampaign method', [
                            'influencer_request_detail_id' => $influencerRequestDetail->id,
                            'campaign_id' => $influencerRequestDetail->compaign_id,
                            'original_charge_id' => $originalChargeId,
                            'cash_out_amount' => $influencerRequestDetail->cash_out_amount,
                            'platform_amount' => $influencerRequestDetail->platform_amount,
                            'influencer_amount' => $influencerRequestDetail->influencer_amount,
                            'total_amount' => $influencerRequestDetail->total_amount,
                            'influencer_price' => $influencerRequestDetail->influencer_price,
                            'days_remaining' => $days
                        ]);

                        $result = $influencerRequestDetail->cancelCampaign('submit_timeout', [
                            'process_refund' => true,
                            'adjust_price' => true,
                            'remove_pause' => true,
                            'send_notifications' => false, // We'll send custom notifications below
                            'metadata' => [
                                'days_remaining' => $days,
                                'refund_type' => 'submit_timeout',
                                'command' => 'CancelledTimeoutInfluencerSubmit',
                                'automated' => true
                            ]
                        ]);

                        if ($result['success']) {
                            // Create statistics record for points deduction
                            Statistic::create([
                                'user_id' => $influencerRequestDetail->influencerdetails->user_id,
                                'points' => $adminGamification->points_deadlines,
                                'type' => '0',
                                'title' => '[' . $influencerRequestDetail->compaign_id . ']</br>' . $adminGamification->points_deadlines . ' points lost for not submitting on time',
                                'date' => date('Y-m-d H:i:s'),
                            ]);

                            // Send custom notifications for submit timeout refund
                            dispatch(new NewCancelRefund($customer, $influencerRequestDetail));
                            $customer->notify(new CancelRefund($customer, $influencerRequestDetail));

                            \Log::info('Influencer submit timeout processed successfully via cancelCampaign', [
                                'influencer_request_detail_id' => $influencerRequestDetail->id,
                                'campaign_id' => $influencerRequestDetail->compaign_id,
                                'refund_processed' => $result['refund_processed'],
                                'refund_id' => $result['refund_id'] ?? null,
                                'price_adjusted' => $result['price_adjusted'],
                                'price_adjustment_amount' => $result['price_adjustment_amount'] ?? null,
                                'customer_id' => $customer->id ?? null,
                                'influencer_id' => $influencer->id ?? null,
                                'points_deducted' => $adminGamification->points_deadlines,
                                'days_remaining' => $days
                            ]);
                        } else {
                            \Log::error('Influencer submit timeout failed via cancelCampaign', [
                                'influencer_request_detail_id' => $influencerRequestDetail->id,
                                'campaign_id' => $influencerRequestDetail->compaign_id,
                                'error_code' => $result['error_code'] ?? null,
                                'error_message' => $result['message'] ?? null,
                                'error_details' => $result['error_details'] ?? null,
                                'customer_id' => $customer->id ?? null,
                                'influencer_id' => $influencer->id ?? null,
                                'days_remaining' => $days
                            ]);
                        }

                        dispatch(new NewSubmitPhaseEnded($influencer, $influencerRequestDetail));
                    }
                }
            }
        }

        \Log::info('CancelledTimeoutInfluencerSubmit command completed');
    }
}
