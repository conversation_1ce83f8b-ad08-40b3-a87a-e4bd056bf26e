<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class FixStoragePaths extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'storage:fix-paths';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix storage paths and move files from old locations to public disk';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting storage path fix...');

        // Define directories to fix
        $directories = [
            'social_pics' => [
                'old_path' => public_path('storage/app/social_pics'),
                'new_path' => storage_path('app/public/social_pics')
            ],
            'files' => [
                'old_path' => public_path('storage/app/files'),
                'new_path' => storage_path('app/public/files')
            ]
        ];

        // Process each directory
        foreach ($directories as $dirName => $paths) {
            $this->fixStoragePath($dirName, $paths['old_path'], $paths['new_path']);
        }

        // Verify the storage link exists
        $linkPath = public_path('storage');
        $targetPath = storage_path('app/public');

        if (!File::exists($linkPath)) {
            $this->error('Storage link does not exist. Please run: php artisan storage:link');
            return 1;
        }

        if (!is_link($linkPath)) {
            $this->error('Storage path exists but is not a symbolic link. Please remove it and run: php artisan storage:link');
            return 1;
        }

        $this->info('Storage link verified.');
        $this->info('Storage path fix completed successfully!');

        return 0;
    }

    /**
     * Fix storage path for a specific directory
     *
     * @param string $dirName
     * @param string $oldPath
     * @param string $newPath
     * @return void
     */
    private function fixStoragePath($dirName, $oldPath, $newPath)
    {
        // Ensure the public storage directory exists
        if (!Storage::disk('public')->exists($dirName)) {
            Storage::disk('public')->makeDirectory($dirName);
            $this->info("Created {$dirName} directory in public storage.");
        }

        // Check if old directory exists
        if (File::exists($oldPath)) {
            $this->info("Found old {$dirName} directory at: {$oldPath}");

            $files = File::allFiles($oldPath);
            $movedCount = 0;
            $skippedCount = 0;

            foreach ($files as $file) {
                $relativePath = str_replace($oldPath . '/', '', $file->getPathname());
                $oldFilePath = $file->getPathname();
                $newFilePath = $newPath . '/' . $relativePath;

                // Create subdirectories if they don't exist
                $newFileDir = dirname($newFilePath);
                if (!File::exists($newFileDir)) {
                    File::makeDirectory($newFileDir, 0755, true);
                }

                if (!File::exists($newFilePath)) {
                    File::copy($oldFilePath, $newFilePath);
                    $movedCount++;
                } else {
                    $skippedCount++;
                }
            }

            $this->info("Moved {$movedCount} files from {$dirName} to new location.");
            if ($skippedCount > 0) {
                $this->info("Skipped {$skippedCount} files (already exist in new location).");
            }

            if ($this->confirm("Do you want to remove the old {$dirName} directory?")) {
                File::deleteDirectory($oldPath);
                $this->info("Old {$dirName} directory removed.");
            }
        } else {
            $this->info("No old {$dirName} directory found.");
        }
    }
}
