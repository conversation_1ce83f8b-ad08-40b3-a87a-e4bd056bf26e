<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Jobs\UpdateAllInfluencersDataFromSocialJob;
use App\Jobs\UpdateInfluencerDataFromSocialJob;
use App\Models\Influencer;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * Artisan command to update influencer data from social media sources.
 * 
 * This command provides a convenient way to trigger social data updates
 * for influencers either individually or in bulk.
 * 
 * Usage Examples:
 * - Update all influencers: php artisan influencer:update-social-performance
 * - Update specific influencer: php artisan influencer:update-social-performance --influencer=123
 * - Update with verbose output: php artisan influencer:update-social-performance -v
 * 
 * @package App\Console\Commands
 * <AUTHOR> Development Team
 */
class UpdateInfluencerSocialDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'influencer:update-social-performance
                            {--influencer= : Update specific influencer by <PERSON>}
                            {--user= : Update specific influencer by user ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update influencer data from social media connections and posts';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $this->info('🚀 Starting Influencer Social Data Update');
        $this->newLine();

        // Check for specific influencer options
        $influencerId = $this->option('influencer');
        $userId = $this->option('user');

        if ($influencerId) {
            return $this->updateSpecificInfluencer((int) $influencerId);
        }

        if ($userId) {
            return $this->updateInfluencerByUserId((int) $userId);
        }

        // Update all influencers
        return $this->updateAllInfluencers();
    }

    /**
     * Update a specific influencer by influencer ID.
     *
     * @param int $influencerId
     * @return int
     */
    private function updateSpecificInfluencer(int $influencerId): int
    {
        $influencer = Influencer::find($influencerId);

        if (!$influencer) {
            $this->error("❌ Influencer with ID {$influencerId} not found");
            Log::error('UpdateInfluencerSocialDataCommand: Influencer not found', [
                'influencer_id' => $influencerId,
                'command' => 'update-social-performance'
            ]);
            return Command::FAILURE;
        }

        $this->info("📊 Updating influencer ID: {$influencerId} (User ID: {$influencer->user_id})");
        Log::info('UpdateInfluencerSocialDataCommand: Starting individual influencer update', [
            'influencer_id' => $influencerId,
            'user_id' => $influencer->user_id,
            'command' => 'update-social-performance'
        ]);

        try {
            UpdateInfluencerDataFromSocialJob::dispatch($influencerId);

            $this->info("✅ Job dispatched successfully for influencer {$influencerId}");
            $this->info("🔄 Check queue monitor for job progress");

            Log::info('UpdateInfluencerSocialDataCommand: Job dispatched successfully', [
                'influencer_id' => $influencerId,
                'user_id' => $influencer->user_id,
                'job' => 'UpdateInfluencerDataFromSocialJob'
            ]);

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error("❌ Failed to dispatch job: " . $e->getMessage());
            Log::error('UpdateInfluencerSocialDataCommand: Failed to dispatch job', [
                'influencer_id' => $influencerId,
                'user_id' => $influencer->user_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * Update influencer by user ID.
     *
     * @param int $userId
     * @return int
     */
    private function updateInfluencerByUserId(int $userId): int
    {
        $influencer = Influencer::where('user_id', $userId)->first();

        if (!$influencer) {
            $this->error("❌ Influencer with User ID {$userId} not found");
            Log::error('UpdateInfluencerSocialDataCommand: Influencer not found by user ID', [
                'user_id' => $userId,
                'command' => 'update-social-performance'
            ]);
            return Command::FAILURE;
        }

        $this->info("📊 Updating influencer for User ID: {$userId} (Influencer ID: {$influencer->id})");
        Log::info('UpdateInfluencerSocialDataCommand: Starting influencer update by user ID', [
            'user_id' => $userId,
            'influencer_id' => $influencer->id,
            'command' => 'update-social-performance'
        ]);

        try {
            UpdateInfluencerDataFromSocialJob::dispatch($influencer->id);

            $this->info("✅ Job dispatched successfully for user {$userId}");
            $this->info("🔄 Check queue monitor for job progress");

            Log::info('UpdateInfluencerSocialDataCommand: Job dispatched successfully by user ID', [
                'user_id' => $userId,
                'influencer_id' => $influencer->id,
                'job' => 'UpdateInfluencerDataFromSocialJob'
            ]);

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error("❌ Failed to dispatch job: " . $e->getMessage());
            Log::error('UpdateInfluencerSocialDataCommand: Failed to dispatch job by user ID', [
                'user_id' => $userId,
                'influencer_id' => $influencer->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * Update all influencers.
     *
     * @return int
     */
    private function updateAllInfluencers(): int
    {
        $totalInfluencers = Influencer::count();

        if ($totalInfluencers === 0) {
            $this->warn("⚠️  No influencers found in the database");
            Log::warning('UpdateInfluencerSocialDataCommand: No influencers found', [
                'command' => 'update-social-performance',
                'action' => 'bulk_update'
            ]);
            return Command::SUCCESS;
        }

        $this->info("📊 Found {$totalInfluencers} influencers to update");
        Log::info('UpdateInfluencerSocialDataCommand: Starting bulk update', [
            'total_influencers' => $totalInfluencers,
            'command' => 'update-social-performance',
            'action' => 'bulk_update'
        ]);

        try {
            UpdateAllInfluencersDataFromSocialJob::dispatch();

            $this->info("✅ Bulk update job dispatched successfully");
            $this->info("🔄 This will create {$totalInfluencers} individual update jobs");
            $this->info("📈 Check queue monitor for progress");
            $this->newLine();

            $this->table(
                ['Metric', 'Value'],
                [
                    ['Total Influencers', $totalInfluencers],
                    ['Jobs to be Created', $totalInfluencers + 1], // +1 for the bulk job
                    ['Estimated Duration', $this->estimateDuration($totalInfluencers)],
                ]
            );

            Log::info('UpdateInfluencerSocialDataCommand: Bulk update job dispatched successfully', [
                'total_influencers' => $totalInfluencers,
                'jobs_to_create' => $totalInfluencers + 1,
                'estimated_duration_seconds' => $totalInfluencers * 30,
                'job' => 'UpdateAllInfluencersDataFromSocialJob'
            ]);

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error("❌ Failed to dispatch bulk update job: " . $e->getMessage());
            Log::error('UpdateInfluencerSocialDataCommand: Failed to dispatch bulk update job', [
                'total_influencers' => $totalInfluencers,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * Estimate processing duration based on influencer count.
     *
     * @param int $count
     * @return string
     */
    private function estimateDuration(int $count): string
    {
        // Rough estimate: ~30 seconds per influencer (including queue processing)
        $estimatedSeconds = $count * 30;
        
        if ($estimatedSeconds < 60) {
            return "{$estimatedSeconds} seconds";
        }
        
        $estimatedMinutes = round($estimatedSeconds / 60);
        
        if ($estimatedMinutes < 60) {
            return "{$estimatedMinutes} minutes";
        }
        
        $estimatedHours = round($estimatedMinutes / 60, 1);
        return "{$estimatedHours} hours";
    }
}
