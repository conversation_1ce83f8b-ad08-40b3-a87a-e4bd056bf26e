<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use App\Notifications\adminUserRegistration;

use App\Jobs\NewadminUserRegistration;

class RegisterUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'register:user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send Registered User Email';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $users = User::where('is_email_sent',NULL)->get(); 
        Log::info('cron run mail');
        Log::info($users);
        foreach ($users as $key => $user) {
            //$admin->notify(new newQuestion($admin, $details));

            $formData = $user;  
            $user->update(['is_email_sent'=>'1']);

 
            if($user->lang == 'En'){               
                \Mail::send('emails.mail-template', ['data'=>$user], function($message) use ($user){
                    $message->to($user->email);
                    $message->subject(env('APP_NAME')." - Closed beta Application");
                    $message->from($address = '<EMAIL>', $name = env('MAIL_FROM_NAME', env('APP_NAME')));  
                }); 
            }else{ 
                \Mail::send('emails.mail-template-de', ['data'=>$user], function($message) use ($user){
                    $message->to($user->email);
                    $message->subject(env('APP_NAME')." - Closed beta Application");
                    $message->from($address = '<EMAIL>', $name = env('MAIL_FROM_NAME', env('APP_NAME')));  
                });
            }

            $admin = User::whereUserType('admin')->first();

                
            dispatch(new NewadminUserRegistration($sender_user,$user));

            $admin->notify(new adminUserRegistration ($user,$admin));

            Log::info('cron run mail'.$user->email);
 
        }

        return Command::SUCCESS;
    }
}
