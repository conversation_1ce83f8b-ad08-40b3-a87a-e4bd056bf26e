<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Jobs\ComputeAverageReachJob;
use App\Models\Influencer;
use Illuminate\Console\Command;

class ClearInsightFlagsCommand extends Command
{
    protected $signature = 'influencers:clear-insight-flags';

    protected $description = 'Recompute average reach for influencers missing insights.';

    public function handle(): int
    {
        Influencer::where(function ($q) {
            $q->where('flag_story_insight_missing', true)
              ->orWhere('flag_reel_insight_missing', true)
              ->orWhere('flag_feed_insight_missing', true);
        })->chunkById(100, function ($influencers) {
            foreach ($influencers as $inf) {
                ComputeAverageReachJob::dispatch($inf->id);
            }
        });

        $this->info('Jobs dispatched');
        return self::SUCCESS;
    }
}
