<?php

namespace App\Console\Commands;

use App\Jobs\NewRequestCancelInfluencer;
use App\Models\Campaign;
use App\Models\InfluencerRequestDetail;
use App\Models\InfluencerRequestAccept;
use Socialite;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;
use App\Models\SocialKeys;
use App\Models\CampaignRequestTime;
use App\Models\InfluencerDetail;
use App\Models\User;
use App\Notifications\RequestCancelInfluencer;
use Illuminate\Support\Facades\Auth;

class TimeoutCustomerPayment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'timeoutcustomer:payment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cancelled Timeout Customer Payment';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $campaignRequestTime = CampaignRequestTime::first();
        $influencerRequestDetails = InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->select('influencer_request_details.*', 'influencer_details.id as i_id', 'influencer_details.user_id as i_user_id',)
            ->where('influencer_request_details.created_at', '>=', now()->subDays(10)) // Only process recent records
            ->whereNull('influencer_request_details.status') // Only unprocessed records
            ->whereNull('influencer_request_details.refund_reason') // Not already cancelled/refunded
            ->where('influencer_request_details.is_paused', 0) // Not paused
            ->get();

        foreach ($influencerRequestDetails as $influencerRequestDetail) {
            $created_date = date('Y-m-d H:i:s', strtotime($influencerRequestDetail->created_at));
            // request time for request phase and 3 for payment phase
            $campaignDate = date('Y-m-d H:i:s', strtotime($created_date . ' + ' . ($campaignRequestTime->request_time + 3) . ' days'));
            $date = date('Y-m-d H:i:s');
            $seconds = strtotime($campaignDate) - strtotime($date);

            $days = floor($seconds / 86400);
            if (!($days < 6 && $days >= 0)) {
                $campaign = Campaign::where('campaign_id', $influencerRequestDetail->compaign_id)->first();
                if (isset($campaign->has_started) && $campaign->has_started == 0 && $influencerRequestDetail->status == null) {
                    $detail = InfluencerRequestDetail::where('id', $influencerRequestDetail->id)->where('compaign_id', $influencerRequestDetail->compaign_id)->first();

                    // Use the new centralized cancelCampaign method
                    Log::info('Processing customer payment timeout using cancelCampaign method', [
                        'influencer_request_detail_id' => $detail->id,
                        'campaign_id' => $detail->compaign_id
                    ]);

                    $result = $detail->cancelCampaign('cancelled_by_customer', [
                        'process_refund' => false, // No refund processing needed for this timeout
                        'adjust_price' => false,
                        'remove_pause' => false,
                        'send_notifications' => false, // We'll send custom notifications below
                        'metadata' => [
                            'timeout_type' => 'customer_payment',
                            'command' => 'TimeoutCustomerPayment',
                            'automated' => true
                        ]
                    ]);

                    if ($result['success']) {
                        $InfluencerDetail = InfluencerDetail::where('id', $detail->influencer_detail_id)->first();
                        $influencer = User::whereId($InfluencerDetail->user_id)->first();
                        $customer = User::whereId($detail->user_id)->first();

                        Log::info('Customer payment timeout processed successfully via cancelCampaign', [
                            'campaign_id' => $detail->compaign_id,
                            'influencer_request_detail_id' => $detail->id
                        ]);

                        // Send custom notifications for customer payment timeout
                        dispatch(new NewRequestCancelInfluencer($influencer, $customer, $detail));
                        $influencer->notify(new RequestCancelInfluencer($influencer, $customer, $detail));
                    } else {
                        Log::error('Customer payment timeout failed via cancelCampaign', [
                            'campaign_id' => $detail->compaign_id,
                            'influencer_request_detail_id' => $detail->id,
                            'error_code' => $result['error_code'] ?? null,
                            'error_message' => $result['message'] ?? null
                        ]);
                    }
                }
            }
        }
    }
}
