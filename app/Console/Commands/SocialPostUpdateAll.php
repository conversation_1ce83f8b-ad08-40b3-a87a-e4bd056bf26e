<?php

namespace App\Console\Commands;

use App\Models\SocialConnect;
use App\Models\SocialPost;
use App\Models\InfluencerRequestDetail;
use Socialite;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;
use App\Models\SocialKeys;
use Storage;
use App\Helpers\Instagram\InsightsForTypePost;
use App\Helpers\Instagram\InsightsForTypeStory;

class SocialPostUpdateAll extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:social-posts-all';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update all influencers social posts from cron';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Here we track the social media accounts from which we have pulled the content from,
        // so that we don't do multiple api request for the same account again.
        $socialConnectTracker = [];

        $socialConnects = SocialConnect::where('media', 'instagram')
            ->whereNotNull('token')
            ->whereNotNull('token_secret')
            ->get();

        $this->info("Processing {$socialConnects->count()} Instagram social connects...");

        foreach ($socialConnects as $socialConnect) {
            if (isset($socialConnectTracker[$socialConnect->id])) {
                Log::info('Skipping duplicate social connect', [
                    'media' => $socialConnect->media,
                    'social_connect_id' => $socialConnect->id,
                    'user_id' => $socialConnect->user_id,
                    'file' => __FILE__,
                    'class' => __CLASS__,
                    'function' => __FUNCTION__,
                    'line' => __LINE__
                ]);
                continue;
            }

            $socialConnectTracker[$socialConnect->id] = true;

            // Only instagram is active now
            if ($socialConnect->media == 'instagram') {
                $this->importInstagramPostsAndInsights($socialConnect, $influencerRequestDetail = null);
            }

            // if ($socialConnect->media == 'twitter') {
            //     $this->processTwitter($influencerRequestDetail, $socialConnect);
            // } elseif ($socialConnect->media == 'youtube') {
            //     $this->processYouTube($influencerRequestDetail, $socialConnect);
            // } elseif ($socialConnect->media == 'facebook') {
            //     $this->processFacebook($influencerRequestDetail, $socialConnect);
            // } elseif ($socialConnect->media == 'tiktok') {
            //     $this->processTikTok($influencerRequestDetail, $socialConnect);
            // } elseif ($socialConnect->media == 'twitch') {
            //     $this->processTwitch($influencerRequestDetail, $socialConnect);
            // } elseif ($socialConnect->media == 'instagram') {
            //     $this->processInstagram($influencerRequestDetail, $socialConnect);
            // }
        }
    }

    private function importInstagramPostsAndInsights($socialConnect, $influencerRequestDetail = null) {
        Log::info('Importing Instagram posts', [
            'user_id' => $socialConnect->user_id,
            'social_connect_id' => $socialConnect->id,
            'media' => $socialConnect->media,
            'action' => 'importInstagramPostsAndInsights'
        ]);

        try {
            $postImporter = new InsightsForTypePost($socialConnect, $influencerRequestDetail);
            $postImporter->importSocialMediaPost();

            Log::info('Importing Instagram stories', [
                'user_id' => $socialConnect->user_id,
                'social_connect_id' => $socialConnect->id,
                'media' => $socialConnect->media,
                'action' => 'importInstagramStories'
            ]);

            $storyImporter = new InsightsForTypeStory($socialConnect, $influencerRequestDetail);
            $storyImporter->importSocialMediaStory();

            $this->info("✅ Processed Instagram account for user {$socialConnect->user_id}");
        } catch (\Exception $e) {
            Log::error('Failed to import Instagram data', [
                'user_id' => $socialConnect->user_id,
                'social_connect_id' => $socialConnect->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->error("❌ Failed to process user {$socialConnect->user_id}: {$e->getMessage()}");
        }
    }
}
