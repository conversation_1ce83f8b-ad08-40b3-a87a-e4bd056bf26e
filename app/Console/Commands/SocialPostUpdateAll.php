<?php

namespace App\Console\Commands;

use App\Models\SocialConnect;
use App\Models\SocialPost;
use App\Models\InfluencerRequestDetail;
use Socialite;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;
use App\Models\SocialKeys;
use Storage;
use App\Helpers\Instagram\InsightsForTypePost;
use App\Helpers\Instagram\InsightsForTypeStory;

class SocialPostUpdateAll extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:social-posts-all';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update all influencers social posts from cron';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Filter influencer request details based on payment status, status, and created in last 30 days
        $cutoffDate = Carbon::now()->subDays(60);

        // Here we track the social media accounts from which we have pulled the content from,
        // so that we don't do multiple api request for the same account again. 
        $socialConnectTracker = ['instagram' => []];

        $socialConnects = SocialConnect::all();

        foreach ($socialConnects as $socialConnect) {
            if (isset($socialConnectTracker[$socialConnect->media][$socialConnect->id])) {
                Log::info('Skipping duplicate social connect', [
                    'media' => $socialConnect->media,
                    'social_connect_id' => $socialConnect->id,
                    'user_id' => $socialConnect->user_id,
                    'influencer_request_detail_id' => $influencerRequestDetail->id,
                    'file' => __FILE__,
                    'class' => __CLASS__,
                    'function' => __FUNCTION__,
                    'line' => __LINE__
                ]);
                // We already pulled data from this account. So skip
                continue;
            }

            $socialConnectTracker[$socialConnect->media][$socialConnect->id] = 1;

            // TODO only instagram is active now
            if ($socialConnect->media == 'instagram') {
                $this->importInstagramPostsAndInsights($socialConnect, $influencerRequestDetail);
            }

            // if ($socialConnect->media == 'twitter') {
            //     $this->processTwitter($influencerRequestDetail, $socialConnect);
            // } elseif ($socialConnect->media == 'youtube') {
            //     $this->processYouTube($influencerRequestDetail, $socialConnect);
            // } elseif ($socialConnect->media == 'facebook') {
            //     $this->processFacebook($influencerRequestDetail, $socialConnect);
            // } elseif ($socialConnect->media == 'tiktok') {
            //     $this->processTikTok($influencerRequestDetail, $socialConnect);
            // } elseif ($socialConnect->media == 'twitch') {
            //     $this->processTwitch($influencerRequestDetail, $socialConnect);
            // } elseif ($socialConnect->media == 'instagram') {
            //     $this->processInstagram($influencerRequestDetail, $socialConnect);
            // }
        }
    }

    private function importInstagramPostsAndInsights($socialConnect, $influencerRequestDetail) {
        Log::info('Importing Instagram posts', [
            'user_id' => $socialConnect->user_id,
            'social_connect_id' => $socialConnect->id,
            'influencer_request_detail_id' => $influencerRequestDetail->id,
            'media' => $socialConnect->media,
            'action' => 'importInstagramPostsAndInsights'
        ]);
        $postImporter = new InsightsForTypePost($socialConnect, $influencerRequestDetail);
        $postImporter->importSocialMediaPost();

        Log::info('Importing Instagram stories', [
            'user_id' => $socialConnect->user_id,
            'social_connect_id' => $socialConnect->id,
            'influencer_request_detail_id' => $influencerRequestDetail->id,
            'media' => $socialConnect->media,
            'action' => 'importInstagramPostsAndInsights'
        ]);
        $storyImporter = new InsightsForTypeStory($socialConnect, $influencerRequestDetail);
        $storyImporter->importSocialMediaStory();
    }
}
