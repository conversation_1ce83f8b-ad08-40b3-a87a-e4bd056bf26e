<?php

namespace App\Console\Commands;

use App\Jobs\RepriceAllJob;
use App\Models\Influencer;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RepriceAllCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reprice:all
                            {--sync : Run synchronously instead of queuing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reprice all influencers for all campaign types';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $influencerCount = Influencer::count();

        if ($influencerCount === 0) {
            $this->warn('⚠️  No influencers found to reprice.');
            Log::warning('RepriceAllCommand: No influencers found', [
                'command' => 'reprice:all'
            ]);
            return Command::SUCCESS;
        }

        // Check for running repricing jobs to prevent conflicts
        $runningJobs = \romanzipp\QueueMonitor\Models\Monitor::where('name', 'like', '%RepriceAllJob%')
            ->where('status', \romanzipp\QueueMonitor\Enums\MonitorStatus::RUNNING)
            ->count();

        if ($runningJobs > 0) {
            $this->warn("⚠️  There are {$runningJobs} repricing jobs currently running.");
            $this->info('Skipping to prevent conflicts. Wait for current jobs to complete.');
            Log::warning('RepriceAllCommand: Skipped due to running jobs', [
                'running_jobs' => $runningJobs,
                'command' => 'reprice:all'
            ]);
            return Command::SUCCESS;
        }

        $this->info("🚀 Starting repricing for {$influencerCount} influencers");
        $this->info('📊 Campaign types: Boost Me, Survey, Reaction Video');

        Log::info('RepriceAllCommand: Starting repricing process', [
            'influencer_count' => $influencerCount,
            'sync_mode' => $this->option('sync'),
            'command' => 'reprice:all'
        ]);

        try {
            if ($this->option('sync')) {
                $this->info('⚡ Running repricing synchronously...');
                $this->warn('⚠️  This may take a very long time for large datasets.');

                Log::info('RepriceAllCommand: Starting synchronous execution', [
                    'influencer_count' => $influencerCount
                ]);

                $startTime = microtime(true);

                // Run the job synchronously
                $job = new RepriceAllJob();
                $job->handle(app(\App\Services\PricingCalculator::class));

                $endTime = microtime(true);
                $duration = round($endTime - $startTime, 2);

                $this->info("✅ Repricing completed successfully in {$duration} seconds.");

                Log::info('RepriceAllCommand: Synchronous execution completed', [
                    'duration_seconds' => $duration,
                    'influencer_count' => $influencerCount
                ]);
            } else {
                // Queue the job
                RepriceAllJob::dispatch();

                $this->info('✅ Repricing job queued successfully!');
                $this->info('📈 Monitor progress at: /admin/queue-monitor');
                $this->info('🔄 Or run: php artisan queue:work');

                Log::info('RepriceAllCommand: Job queued successfully', [
                    'influencer_count' => $influencerCount,
                    'job' => 'RepriceAllJob'
                ]);
            }

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('❌ Failed to start repricing: ' . $e->getMessage());
            Log::error('RepriceAllCommand: Execution failed', [
                'influencer_count' => $influencerCount,
                'sync_mode' => $this->option('sync'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return Command::FAILURE;
        }
    }
}
