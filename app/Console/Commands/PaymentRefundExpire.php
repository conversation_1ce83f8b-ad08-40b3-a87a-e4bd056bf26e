<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\InfluencerRequestDetail;
use App\Models\User;
use App\Notifications\PaymentRefund;
use Carbon\Carbon;
use App\Models\StripeAccount;
use App\Models\TransferInfluencer;

use App\Jobs\NewPaymentRefund;

class PaymentRefundExpire extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'paymentrefund:expire';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Refund the brand when campaign expires';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        \Log::info('PaymentRefundExpire command started');

        $influencerRequestDetails = InfluencerRequestDetail::leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )
            ->select('influencer_request_details.*','influencer_request_accepts.request','influencer_request_accepts.request_time','influencer_request_accepts.request_time_accept' )
            ->with(['influencerdetails', 'invoices']) // Eager load the relationships
            ->where('invoice_id','!=',null)
            ->get();

        \Log::info('PaymentRefundExpire: Found records to process', [
            'total_records' => $influencerRequestDetails->count()
        ]);

      foreach ($influencerRequestDetails as $influencerRequestDetail) {
            $time = (isset($influencerRequestDetail->request_time_accept) && $influencerRequestDetail->request_time_accept == '1')?$influencerRequestDetail->request_time+$influencerRequestDetail->time:$influencerRequestDetail->time;

            $created_date = date('Y-m-d H:i:s',strtotime($influencerRequestDetail->created_at));
            $updated_date = date('Y-m-d H:i:s',strtotime($influencerRequestDetail->updated_at));
            $campaignDate = date('Y-m-d H:i:s', strtotime($created_date. ' + '.$time.' days'));
            $date = date('Y-m-d H:i:s');
            $seconds = strtotime($date) - strtotime($campaignDate);

            $days = floor($seconds / 86400);

            // Check if campaign has expired and is eligible for refund
            // refund_txn_id contains the original charge ID from the separate charges and transfer system
            if(
                $days > $time &&
                $days >= 0 &&
                !empty($influencerRequestDetail->refund_txn_id) &&
                empty($influencerRequestDetail->refund_reason)
            ) {
                // Validate required relationships exist before processing
                if (!$influencerRequestDetail->influencerdetails) {
                    \Log::warning('PaymentRefundExpire: Missing influencer details relationship', [
                        'influencer_request_detail_id' => $influencerRequestDetail->id,
                        'campaign_id' => $influencerRequestDetail->compaign_id,
                        'influencer_detail_id' => $influencerRequestDetail->influencer_detail_id
                    ]);
                    continue; // Skip this record and continue with the next one
                }

                if (!$influencerRequestDetail->invoices) {
                    \Log::warning('PaymentRefundExpire: Missing invoice relationship', [
                        'influencer_request_detail_id' => $influencerRequestDetail->id,
                        'campaign_id' => $influencerRequestDetail->compaign_id,
                        'invoice_id' => $influencerRequestDetail->invoice_id
                    ]);
                    continue; // Skip this record and continue with the next one
                }

                $fieldName = $influencerRequestDetail->advertising.'_price';

                // Safely get the influencer user
                $influencer = null;
                if ($influencerRequestDetail->influencerdetails && $influencerRequestDetail->influencerdetails->user_id) {
                    $influencer = User::where('id', $influencerRequestDetail->influencerdetails->user_id)->first();
                }

                if (!$influencer) {
                    \Log::warning('PaymentRefundExpire: Influencer user not found', [
                        'influencer_request_detail_id' => $influencerRequestDetail->id,
                        'campaign_id' => $influencerRequestDetail->compaign_id,
                        'influencer_detail_id' => $influencerRequestDetail->influencer_detail_id,
                        'expected_user_id' => $influencerRequestDetail->influencerdetails->user_id ?? 'null'
                    ]);
                    continue; // Skip this record and continue with the next one
                }

                // Safely get the customer user
                $customer = User::where('id', $influencerRequestDetail->user_id)->first();
                if (!$customer) {
                    \Log::warning('PaymentRefundExpire: Customer user not found', [
                        'influencer_request_detail_id' => $influencerRequestDetail->id,
                        'campaign_id' => $influencerRequestDetail->compaign_id,
                        'expected_customer_id' => $influencerRequestDetail->user_id
                    ]);
                    continue; // Skip this record and continue with the next one
                }

                // Use the new separate charges and transfer system
                // The original charge ID is stored in refund_txn_id field
                $originalChargeId = $influencerRequestDetail->refund_txn_id ?? '';

                // Use the new centralized cancelCampaign method
                \Log::info('Processing payment refund expiry using cancelCampaign method', [
                    'influencer_request_detail_id' => $influencerRequestDetail->id,
                    'campaign_id' => $influencerRequestDetail->compaign_id,
                    'original_charge_id' => $originalChargeId,
                    'cash_out_amount' => $influencerRequestDetail->cash_out_amount,
                    'platform_amount' => $influencerRequestDetail->platform_amount,
                    'influencer_amount' => $influencerRequestDetail->influencer_amount,
                    'total_amount' => $influencerRequestDetail->total_amount,
                    'influencer_price' => $influencerRequestDetail->influencer_price,
                    'days_expired' => $days
                ]);

                $result = $influencerRequestDetail->cancelCampaign('time_expired', [
                    'process_refund' => true,
                    'adjust_price' => true,
                    'remove_pause' => true,
                    'send_notifications' => false, // We'll send custom notifications below
                    'metadata' => [
                        'days_expired' => $days,
                        'refund_type' => 'automatic_expiry',
                        'command' => 'PaymentRefundExpire',
                        'automated' => true
                    ]
                ]);

                if ($result['success']) {
                    // Send custom notifications for payment refund expiry
                    dispatch(new NewPaymentRefund($customer, $influencerRequestDetail, $influencer));
                    $customer->notify(new PaymentRefund($customer, $influencerRequestDetail, $influencer));

                    \Log::info('Payment refund expiry processed successfully via cancelCampaign', [
                        'influencer_request_detail_id' => $influencerRequestDetail->id,
                        'campaign_id' => $influencerRequestDetail->compaign_id,
                        'refund_processed' => $result['refund_processed'],
                        'refund_id' => $result['refund_id'] ?? null,
                        'price_adjusted' => $result['price_adjusted'],
                        'price_adjustment_amount' => $result['price_adjustment_amount'] ?? null,
                        'customer_id' => $customer->id ?? null,
                        'influencer_id' => $influencer->id ?? null,
                        'days_expired' => $days
                    ]);
                } else {
                    \Log::error('Payment refund expiry failed via cancelCampaign', [
                        'influencer_request_detail_id' => $influencerRequestDetail->id,
                        'campaign_id' => $influencerRequestDetail->compaign_id,
                        'error_code' => $result['error_code'] ?? null,
                        'error_message' => $result['message'] ?? null,
                        'error_details' => $result['error_details'] ?? null,
                        'customer_id' => $customer->id ?? null,
                        'influencer_id' => $influencer->id ?? null,
                        'days_expired' => $days
                    ]);
                }
            }
        }

        \Log::info('PaymentRefundExpire command completed');
        return 0;
    }
}
