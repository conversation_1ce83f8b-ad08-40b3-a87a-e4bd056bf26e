<?php

use App\Http\Controllers\Admin\QueueMonitorController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Admin Queue Monitor Routes
|--------------------------------------------------------------------------
|
| These routes are for the admin queue monitoring interface.
| They should be included in your main routes file with proper middleware.
|
*/

Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    
    // Queue Monitor Dashboard
    Route::get('/queue-monitor', [QueueMonitorController::class, 'dashboard'])->name('queue-monitor.dashboard');
    
    // Queue Monitor Jobs List
    Route::get('/queue-monitor/jobs', [QueueMonitorController::class, 'index'])->name('queue-monitor.index');
    
    // Queue Monitor Job Details
    Route::get('/queue-monitor/jobs/{monitor}', [QueueMonitorController::class, 'show'])->name('queue-monitor.show');
    
    // Queue Monitor Actions
    Route::post('/queue-monitor/start-repricing', [QueueMonitorController::class, 'startRepricing'])->name('queue-monitor.start-repricing');
    Route::post('/queue-monitor/jobs/{monitor}/retry', [QueueMonitorController::class, 'retry'])->name('queue-monitor.retry');
    Route::delete('/queue-monitor/jobs/{monitor}', [QueueMonitorController::class, 'delete'])->name('queue-monitor.delete');
    Route::post('/queue-monitor/clear-completed', [QueueMonitorController::class, 'clearCompleted'])->name('queue-monitor.clear-completed');
    
});
