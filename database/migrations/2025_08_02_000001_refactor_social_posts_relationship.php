<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('social_posts', function (Blueprint $table) {
            // Add new fields for proper categorization
            $table->string('post_category', 50)->default('general')->after('influencer_request_accept_id');
            $table->boolean('campaign_deliverable')->default(false)->after('post_category');
            
            // Add index for better query performance
            $table->index(['post_category', 'campaign_deliverable'], 'social_posts_category_deliverable_idx');
            $table->index(['user_id', 'campaign_deliverable'], 'social_posts_user_deliverable_idx');
        });

        // Migrate existing data
        $this->migrateExistingData();
        
        // Now modify the influencer_request_accept_id field type
        // First, set string values to null and update categorization
        Schema::table('social_posts', function (Blueprint $table) {
            // Change to nullable bigint for proper foreign key relationship
            $table->unsignedBigInteger('influencer_request_accept_id')->nullable()->change();
        });
    }

    /**
     * Migrate existing data to new structure
     */
    private function migrateExistingData()
    {
        // Classify posts with string values as non-campaign posts
        DB::table('social_posts')
            ->where('influencer_request_accept_id', 'story')
            ->update([
                'post_category' => 'story',
                'campaign_deliverable' => false,
                'influencer_request_accept_id' => null
            ]);

        DB::table('social_posts')
            ->where('influencer_request_accept_id', 'reel')
            ->update([
                'post_category' => 'reel', 
                'campaign_deliverable' => false,
                'influencer_request_accept_id' => null
            ]);

        DB::table('social_posts')
            ->where('influencer_request_accept_id', 'post')
            ->update([
                'post_category' => 'post',
                'campaign_deliverable' => false,
                'influencer_request_accept_id' => null
            ]);

        // Handle any other string values that might exist
        DB::table('social_posts')
            ->whereRaw('influencer_request_accept_id REGEXP "^[^0-9]"')
            ->whereNotNull('influencer_request_accept_id')
            ->update([
                'post_category' => 'general',
                'campaign_deliverable' => false,
                'influencer_request_accept_id' => null
            ]);

        // Mark posts with numeric IDs as campaign deliverables
        DB::table('social_posts')
            ->whereRaw('influencer_request_accept_id REGEXP "^[0-9]+$"')
            ->whereNotNull('influencer_request_accept_id')
            ->update([
                'post_category' => 'campaign',
                'campaign_deliverable' => true
            ]);

        // Handle any remaining null values
        DB::table('social_posts')
            ->whereNull('influencer_request_accept_id')
            ->where('post_category', 'general')
            ->where('campaign_deliverable', false)
            ->update([
                'post_category' => 'general'
            ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Restore original string values before dropping columns
        DB::table('social_posts')
            ->where('post_category', 'story')
            ->where('campaign_deliverable', false)
            ->update(['influencer_request_accept_id' => 'story']);

        DB::table('social_posts')
            ->where('post_category', 'reel')
            ->where('campaign_deliverable', false)
            ->update(['influencer_request_accept_id' => 'reel']);

        DB::table('social_posts')
            ->where('post_category', 'post')
            ->where('campaign_deliverable', false)
            ->update(['influencer_request_accept_id' => 'post']);

        Schema::table('social_posts', function (Blueprint $table) {
            // Drop indexes
            $table->dropIndex('social_posts_category_deliverable_idx');
            $table->dropIndex('social_posts_user_deliverable_idx');
            
            // Drop new columns
            $table->dropColumn(['post_category', 'campaign_deliverable']);
            
            // Revert field type back to string
            $table->string('influencer_request_accept_id')->nullable()->change();
        });
    }
};
