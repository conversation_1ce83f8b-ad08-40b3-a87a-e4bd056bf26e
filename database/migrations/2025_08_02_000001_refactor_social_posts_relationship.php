<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('social_posts', function (Blueprint $table) {
            // Add new fields for proper categorization
            $table->string('post_category', 50)->nullable()->after('influencer_request_accept_id');
            $table->boolean('campaign_deliverable')->default(false)->after('post_category');
            
            // Add index for better query performance
            $table->index(['post_category', 'campaign_deliverable'], 'social_posts_category_deliverable_idx');
            $table->index(['user_id', 'campaign_deliverable'], 'social_posts_user_deliverable_idx');
        });

        // Migrate existing data
        $this->migrateExistingData();
        
        // Now modify the influencer_request_accept_id field type
        // First, set string values to null and update categorization
        Schema::table('social_posts', function (Blueprint $table) {
            // Change to nullable bigint for proper foreign key relationship
            $table->unsignedBigInteger('influencer_request_accept_id')->nullable()->change();
        });
    }

    /**
     * Migrate existing data to new structure
     */
    private function migrateExistingData()
    {
        // Classify posts with string values as non-campaign posts
        DB::table('social_posts')
            ->where('influencer_request_accept_id', 'story')
            ->update([
                'post_category' => 'story',
                'campaign_deliverable' => false,
                'influencer_request_accept_id' => null
            ]);

        DB::table('social_posts')
            ->where('influencer_request_accept_id', 'reel')
            ->update([
                'post_category' => 'reel',
                'campaign_deliverable' => false,
                'influencer_request_accept_id' => null
            ]);

        DB::table('social_posts')
            ->where('influencer_request_accept_id', 'post')
            ->update([
                'post_category' => 'post',
                'campaign_deliverable' => false,
                'influencer_request_accept_id' => null
            ]);

        // Handle any other string values that might exist (database-agnostic approach)
        // Get all records where influencer_request_accept_id is not null and not numeric
        $nonNumericPosts = DB::table('social_posts')
            ->whereNotNull('influencer_request_accept_id')
            ->whereNotIn('influencer_request_accept_id', ['story', 'reel', 'post'])
            ->get(['id', 'influencer_request_accept_id']);

        foreach ($nonNumericPosts as $post) {
            // Check if the value is numeric (campaign deliverable) or not (general post)
            if (is_numeric($post->influencer_request_accept_id) && ctype_digit($post->influencer_request_accept_id)) {
                // Mark posts with numeric IDs as campaign deliverables - determine proper Instagram post type
                $postCategory = $this->determinePostCategoryFromLegacyData($post);

                DB::table('social_posts')
                    ->where('id', $post->id)
                    ->update([
                        'post_category' => $postCategory,
                        'campaign_deliverable' => true
                    ]);
            } else {
                // Handle non-numeric string values - determine proper Instagram post type
                $postCategory = $this->determinePostCategoryFromLegacyData($post);

                DB::table('social_posts')
                    ->where('id', $post->id)
                    ->update([
                        'post_category' => $postCategory,
                        'campaign_deliverable' => false,
                        'influencer_request_accept_id' => null
                    ]);
            }
        }

        // Handle any remaining null values - set to default 'post' type
        DB::table('social_posts')
            ->whereNull('influencer_request_accept_id')
            ->whereNull('post_category')
            ->where('campaign_deliverable', false)
            ->update([
                'post_category' => 'post'  // Default to Instagram feed post
            ]);

        Log::info('Completed social_posts relationship refactoring migration');
    }

    /**
     * Determine proper Instagram post category from legacy data
     *
     * @param object $post
     * @return string
     */
    private function determinePostCategoryFromLegacyData($post): string
    {
        // Check if the old influencer_request_accept_id gives us a clue
        $oldValue = $post->influencer_request_accept_id;

        if (in_array(strtolower($oldValue), ['story', 'reel', 'post'])) {
            return strtolower($oldValue);
        }

        // Check the 'type' field for hints
        if (!empty($post->type)) {
            switch (strtolower($post->type)) {
                case 'story':
                    return 'story';
                case 'reel':
                case 'video':
                    return 'reel';
                case 'image':
                case 'photo':
                case 'post':
                    return 'post';
            }
        }

        // Default to 'post' (Instagram feed post)
        return 'post';
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Restore original string values before dropping columns
        DB::table('social_posts')
            ->where('post_category', 'story')
            ->where('campaign_deliverable', false)
            ->update(['influencer_request_accept_id' => 'story']);

        DB::table('social_posts')
            ->where('post_category', 'reel')
            ->where('campaign_deliverable', false)
            ->update(['influencer_request_accept_id' => 'reel']);

        DB::table('social_posts')
            ->where('post_category', 'post')
            ->where('campaign_deliverable', false)
            ->update(['influencer_request_accept_id' => 'post']);

        Schema::table('social_posts', function (Blueprint $table) {
            // Drop indexes
            $table->dropIndex('social_posts_category_deliverable_idx');
            $table->dropIndex('social_posts_user_deliverable_idx');
            
            // Drop new columns
            $table->dropColumn(['post_category', 'campaign_deliverable']);
            
            // Revert field type back to string
            $table->string('influencer_request_accept_id')->nullable()->change();
        });
    }
};
