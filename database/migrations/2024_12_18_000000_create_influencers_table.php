<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        // Only create the table if it doesn't exist
        if (!Schema::hasTable('influencers')) {
            Schema::create('influencers', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('user_id')->unique();
                $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
                $table->unsignedInteger('followers');
                $table->decimal('gamification_percentage', 5, 2)->default(0);
                $table->unsignedInteger('ig_story_avg_reach')->nullable();
                $table->unsignedInteger('ig_reel_avg_reach')->nullable();
                $table->unsignedInteger('ig_feed_avg_reach')->nullable();
                $table->boolean('flag_story_insight_missing')->default(false);
                $table->boolean('flag_reel_insight_missing')->default(false);
                $table->boolean('flag_feed_insight_missing')->default(false);
                $table->boolean('admin_override_show_hidden')->default(false);
                $table->timestamp('avg_reach_computed_at')->nullable();
                $table->timestamps();
            });
        } else {
            // Table exists, check if we need to add missing columns
            Schema::table('influencers', function (Blueprint $table) {
                if (!Schema::hasColumn('influencers', 'user_id')) {
                    $table->unsignedBigInteger('user_id')->after('id')->unique();
                    $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
                }
                if (!Schema::hasColumn('influencers', 'ig_story_avg_reach')) {
                    $table->unsignedInteger('ig_story_avg_reach')->nullable();
                }
                if (!Schema::hasColumn('influencers', 'ig_reel_avg_reach')) {
                    $table->unsignedInteger('ig_reel_avg_reach')->nullable();
                }
                if (!Schema::hasColumn('influencers', 'ig_feed_avg_reach')) {
                    $table->unsignedInteger('ig_feed_avg_reach')->nullable();
                }
                if (!Schema::hasColumn('influencers', 'flag_story_insight_missing')) {
                    $table->boolean('flag_story_insight_missing')->default(false);
                }
                if (!Schema::hasColumn('influencers', 'flag_reel_insight_missing')) {
                    $table->boolean('flag_reel_insight_missing')->default(false);
                }
                if (!Schema::hasColumn('influencers', 'flag_feed_insight_missing')) {
                    $table->boolean('flag_feed_insight_missing')->default(false);
                }
                if (!Schema::hasColumn('influencers', 'admin_override_show_hidden')) {
                    $table->boolean('admin_override_show_hidden')->default(false);
                }
                if (!Schema::hasColumn('influencers', 'avg_reach_computed_at')) {
                    $table->timestamp('avg_reach_computed_at')->nullable();
                }
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('influencers');
    }
};
