<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('admin_pricings', function (Blueprint $table) {
            $table->decimal('estimated_reach', 5, 2)->nullable()->after('cpt')->comment('Estimated reach percentage (e.g., 20.00 for 20%)');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('admin_pricings', function (Blueprint $table) {
            $table->dropColumn('estimated_reach');
        });
    }
};
