<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Log::info('Starting social_posts post_category cleanup migration');

        // Get all posts with invalid post_category values
        $invalidPosts = DB::table('social_posts')
            ->whereNotIn('post_category', ['story', 'reel', 'post'])
            ->get();

        Log::info("Found {$invalidPosts->count()} posts with invalid post_category values");

        foreach ($invalidPosts as $post) {
            $newCategory = $this->determineProperPostCategory($post);
            
            DB::table('social_posts')
                ->where('id', $post->id)
                ->update(['post_category' => $newCategory]);

            Log::info("Updated post {$post->id}: '{$post->post_category}' -> '{$newCategory}'");
        }

        // Update the default value for the column to be nullable instead of 'general'
        Schema::table('social_posts', function (Blueprint $table) {
            $table->string('post_category', 50)->nullable()->default(null)->change();
        });

        Log::info('Completed social_posts post_category cleanup migration');
    }

    /**
     * Determine the proper Instagram post category based on available data
     * 
     * @param object $post
     * @return string
     */
    private function determineProperPostCategory($post): string
    {
        // Strategy 1: Check if the 'type' field gives us a clue
        if (!empty($post->type)) {
            switch (strtolower($post->type)) {
                case 'story':
                    return 'story';
                case 'reel':
                case 'video':
                    return 'reel';
                case 'image':
                case 'photo':
                case 'post':
                    return 'post';
            }
        }

        // Strategy 2: Check the link/thumbnail for Instagram URL patterns
        if (!empty($post->link)) {
            if (str_contains($post->link, '/reel/') || str_contains($post->link, 'reel')) {
                return 'reel';
            }
            if (str_contains($post->link, '/stories/') || str_contains($post->link, 'story')) {
                return 'story';
            }
        }

        if (!empty($post->thumbnail)) {
            if (str_contains($post->thumbnail, '/reel/') || str_contains($post->thumbnail, 'reel')) {
                return 'reel';
            }
            if (str_contains($post->thumbnail, '/stories/') || str_contains($post->thumbnail, 'story')) {
                return 'story';
            }
        }

        // Strategy 3: Check if it's a campaign deliverable
        if ($post->campaign_deliverable) {
            // For campaign deliverables, default to 'post' (feed post)
            // This is the most common campaign type
            return 'post';
        }

        // Strategy 4: Default fallback based on media type
        if (!empty($post->type)) {
            if (in_array(strtolower($post->type), ['video', 'mp4'])) {
                return 'reel';  // Videos are likely reels
            }
        }

        // Final fallback: Default to 'post' (Instagram feed post)
        return 'post';
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Revert the default value back to 'general'
        Schema::table('social_posts', function (Blueprint $table) {
            $table->string('post_category', 50)->default('general')->change();
        });

        Log::info('Reverted social_posts post_category cleanup migration');
    }
};
