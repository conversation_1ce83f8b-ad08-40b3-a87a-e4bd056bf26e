<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('admin_pricings', function (Blueprint $table) {
            $table->id();
            $table->string('media')->nullable();
            $table->string('type')->nullable();
            $table->string('country')->nullable();
            $table->string('range')->nullable();
            $table->string('cpt')->nullable();
            $table->decimal('estimated_reach', 5, 2)->nullable()->comment('Estimated reach percentage (e.g., 20.00 for 20%)');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('admin_pricings');
    }
};
