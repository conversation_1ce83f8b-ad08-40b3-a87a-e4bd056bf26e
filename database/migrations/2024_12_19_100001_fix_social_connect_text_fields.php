<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    public function up(): void
    {
        if (Schema::hasTable('social_connects')) {
            // Simply change all columns to TEXT - <PERSON><PERSON> will handle the conversion safely
            Schema::table('social_connects', function (Blueprint $table) {
                $table->text('token')->nullable()->change();
                $table->text('token_secret')->nullable()->change();
                $table->text('picture')->nullable()->change();
                $table->text('url')->nullable()->change();
                $table->text('social_id')->nullable()->change();
            });
            echo "✅ Updated social_connects text fields\n";
        } else {
            echo "⚠️ social_connects table does not exist\n";
        }
    }

    public function down(): void
    {
        if (Schema::hasTable('social_connects')) {
            Schema::table('social_connects', function (Blueprint $table) {
                // Revert back to VARCHAR(191) - be careful with data truncation
                $table->string('token', 191)->nullable()->change();
                $table->string('token_secret', 191)->nullable()->change();
                $table->string('picture', 191)->nullable()->change();
                $table->string('url', 191)->nullable()->change();
                $table->string('social_id', 191)->nullable()->change();
            });
        }
    }
};
