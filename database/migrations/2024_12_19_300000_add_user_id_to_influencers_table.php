<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (Schema::hasTable('influencers')) {
            Schema::table('influencers', function (Blueprint $table) {
                // Add user_id foreign key if it doesn't exist
                if (!Schema::hasColumn('influencers', 'user_id')) {
                    $table->unsignedBigInteger('user_id')->nullable()->after('id');
                    $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
                    $table->unique('user_id'); // One influencer record per user
                }
            });
            
            echo "✅ Added user_id foreign key to influencers table\n";
        } else {
            echo "⚠️ influencers table does not exist\n";
        }
    }

    public function down(): void
    {
        if (Schema::hasTable('influencers')) {
            Schema::table('influencers', function (Blueprint $table) {
                if (Schema::hasColumn('influencers', 'user_id')) {
                    $table->dropForeign(['user_id']);
                    $table->dropUnique(['user_id']);
                    $table->dropColumn('user_id');
                }
            });
        }
    }
};
