<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    public function up(): void
    {
        echo "\n=== DATABASE DIAGNOSTIC REPORT ===\n";
        
        // Check social_connects table
        if (Schema::hasTable('social_connects')) {
            echo "\n✅ social_connects table exists\n";
            $count = DB::table('social_connects')->count();
            echo "   Records: {$count}\n";
            
            $columns = ['token', 'token_secret', 'picture', 'url', 'social_id'];
            foreach ($columns as $column) {
                if (Schema::hasColumn('social_connects', $column)) {
                    try {
                        $type = Schema::getColumnType('social_connects', $column);
                        $needsUpdate = (strtolower($type) !== 'text') ? '❌ NEEDS UPDATE' : '✅ OK';
                        echo "   {$column}: {$type} {$needsUpdate}\n";
                    } catch (Exception $e) {
                        echo "   {$column}: Error checking type\n";
                    }
                } else {
                    echo "   {$column}: ❌ MISSING\n";
                }
            }
        } else {
            echo "\n❌ social_connects table does not exist\n";
        }
        
        // Check influencers table
        if (Schema::hasTable('influencers')) {
            echo "\n✅ influencers table exists\n";
            $count = DB::table('influencers')->count();
            echo "   Records: {$count}\n";
            
            $requiredColumns = [
                'followers', 'gamification_percentage', 'ig_story_avg_reach', 
                'ig_reel_avg_reach', 'ig_feed_avg_reach', 'flag_story_insight_missing',
                'flag_reel_insight_missing', 'flag_feed_insight_missing', 
                'admin_override_show_hidden', 'avg_reach_computed_at'
            ];
            
            foreach ($requiredColumns as $column) {
                $exists = Schema::hasColumn('influencers', $column) ? '✅ EXISTS' : '❌ MISSING';
                echo "   {$column}: {$exists}\n";
            }
        } else {
            echo "\n❌ influencers table does not exist\n";
        }
        
        // Check other tables
        $tables = ['ig_posts', 'influencer_prices'];
        foreach ($tables as $table) {
            if (Schema::hasTable($table)) {
                $count = DB::table($table)->count();
                echo "\n✅ {$table} table exists (Records: {$count})\n";
            } else {
                echo "\n❌ {$table} table does not exist\n";
            }
        }
        
        // Check migration status
        echo "\n=== MIGRATION STATUS ===\n";
        $problematicMigrations = [
            '2024_12_18_000000_create_influencers_table',
            '2024_12_18_000001_create_ig_posts_table',
            '2024_12_18_000002_create_influencer_prices_table',
            '2024_12_19_000000_modify_social_connect_text_fields'
        ];
        
        foreach ($problematicMigrations as $migration) {
            $exists = DB::table('migrations')->where('migration', $migration)->exists();
            $status = $exists ? '✅ MARKED AS RUN' : '❌ NOT RUN';
            echo "{$migration}: {$status}\n";
        }
        
        echo "\n=== RECOMMENDATIONS ===\n";
        echo "This diagnostic migration is READ-ONLY and makes no changes.\n";
        echo "Review the output above to understand your current state.\n";
        echo "No data has been modified.\n\n";
    }

    public function down(): void
    {
        echo "This diagnostic migration made no changes to rollback.\n";
    }
};
