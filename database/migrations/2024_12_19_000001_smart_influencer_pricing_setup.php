<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    public function up(): void
    {
        // 1. Handle influencers table
        if (!Schema::hasTable('influencers')) {
            Schema::create('influencers', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('followers');
                $table->decimal('gamification_percentage', 5, 2)->default(0);
                $table->unsignedInteger('ig_story_avg_reach')->nullable();
                $table->unsignedInteger('ig_reel_avg_reach')->nullable();
                $table->unsignedInteger('ig_feed_avg_reach')->nullable();
                $table->boolean('flag_story_insight_missing')->default(false);
                $table->boolean('flag_reel_insight_missing')->default(false);
                $table->boolean('flag_feed_insight_missing')->default(false);
                $table->boolean('admin_override_show_hidden')->default(false);
                $table->timestamp('avg_reach_computed_at')->nullable();
                $table->timestamps();
            });
            echo "✅ Created influencers table\n";
        } else {
            // Add missing columns to existing table
            Schema::table('influencers', function (Blueprint $table) {
                if (!Schema::hasColumn('influencers', 'followers')) {
                    $table->unsignedInteger('followers');
                }
                if (!Schema::hasColumn('influencers', 'gamification_percentage')) {
                    $table->decimal('gamification_percentage', 5, 2)->default(0);
                }
                if (!Schema::hasColumn('influencers', 'ig_story_avg_reach')) {
                    $table->unsignedInteger('ig_story_avg_reach')->nullable();
                }
                if (!Schema::hasColumn('influencers', 'ig_reel_avg_reach')) {
                    $table->unsignedInteger('ig_reel_avg_reach')->nullable();
                }
                if (!Schema::hasColumn('influencers', 'ig_feed_avg_reach')) {
                    $table->unsignedInteger('ig_feed_avg_reach')->nullable();
                }
                if (!Schema::hasColumn('influencers', 'flag_story_insight_missing')) {
                    $table->boolean('flag_story_insight_missing')->default(false);
                }
                if (!Schema::hasColumn('influencers', 'flag_reel_insight_missing')) {
                    $table->boolean('flag_reel_insight_missing')->default(false);
                }
                if (!Schema::hasColumn('influencers', 'flag_feed_insight_missing')) {
                    $table->boolean('flag_feed_insight_missing')->default(false);
                }
                if (!Schema::hasColumn('influencers', 'admin_override_show_hidden')) {
                    $table->boolean('admin_override_show_hidden')->default(false);
                }
                if (!Schema::hasColumn('influencers', 'avg_reach_computed_at')) {
                    $table->timestamp('avg_reach_computed_at')->nullable();
                }
            });
            echo "✅ Updated influencers table with missing columns\n";
        }

        // 2. Handle ig_posts table
        if (!Schema::hasTable('ig_posts')) {
            Schema::create('ig_posts', function (Blueprint $table) {
                $table->id();
                $table->foreignId('influencer_id')->constrained('influencers')->cascadeOnDelete();
                $table->string('post_type', 16); // story|reel|feed
                $table->unsignedInteger('reach');
                $table->timestamp('posted_at');
                $table->timestamps();
            });
            echo "✅ Created ig_posts table\n";
        } else {
            echo "✅ ig_posts table already exists\n";
        }

        // 3. Handle influencer_prices table
        if (!Schema::hasTable('influencer_prices')) {
            Schema::create('influencer_prices', function (Blueprint $table) {
                $table->id();
                $table->foreignId('influencer_id')->constrained('influencers')->cascadeOnDelete();
                $table->string('campaign_type', 64);
                $table->string('post_type', 16);
                $table->decimal('price', 10, 2);
                $table->json('breakdown');
                $table->timestamp('priced_at');
                $table->timestamps();
                $table->unique(['influencer_id', 'campaign_type']);
            });
            echo "✅ Created influencer_prices table\n";
        } else {
            echo "✅ influencer_prices table already exists\n";
        }

        // 4. Mark the original migrations as run if tables exist
        $this->markMigrationsAsRun();
    }

    private function markMigrationsAsRun(): void
    {
        $migrations = [
            '2024_12_18_000000_create_influencers_table',
            '2024_12_18_000001_create_ig_posts_table', 
            '2024_12_18_000002_create_influencer_prices_table'
        ];

        $currentBatch = DB::table('migrations')->max('batch') ?? 0;
        $nextBatch = $currentBatch + 1;

        foreach ($migrations as $migration) {
            $exists = DB::table('migrations')->where('migration', $migration)->exists();
            if (!$exists) {
                DB::table('migrations')->insert([
                    'migration' => $migration,
                    'batch' => $nextBatch
                ]);
                echo "✅ Marked {$migration} as run\n";
            }
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('influencer_prices');
        Schema::dropIfExists('ig_posts');
        // Don't drop influencers table as it might have existing data
        echo "⚠️  Note: influencers table not dropped to preserve existing data\n";
    }
};
