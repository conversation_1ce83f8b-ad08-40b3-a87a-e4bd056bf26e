<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        // Fix influencers table - add missing columns if they don't exist
        if (Schema::hasTable('influencers')) {
            Schema::table('influencers', function (Blueprint $table) {
                if (!Schema::hasColumn('influencers', 'followers')) {
                    $table->unsignedInteger('followers')->default(0);
                }
                if (!Schema::hasColumn('influencers', 'gamification_percentage')) {
                    $table->decimal('gamification_percentage', 5, 2)->default(0);
                }
                if (!Schema::hasColumn('influencers', 'ig_story_avg_reach')) {
                    $table->unsignedInteger('ig_story_avg_reach')->nullable();
                }
                if (!Schema::hasColumn('influencers', 'ig_reel_avg_reach')) {
                    $table->unsignedInteger('ig_reel_avg_reach')->nullable();
                }
                if (!Schema::hasColumn('influencers', 'ig_feed_avg_reach')) {
                    $table->unsignedInteger('ig_feed_avg_reach')->nullable();
                }
                if (!Schema::hasColumn('influencers', 'flag_story_insight_missing')) {
                    $table->boolean('flag_story_insight_missing')->default(false);
                }
                if (!Schema::hasColumn('influencers', 'flag_reel_insight_missing')) {
                    $table->boolean('flag_reel_insight_missing')->default(false);
                }
                if (!Schema::hasColumn('influencers', 'flag_feed_insight_missing')) {
                    $table->boolean('flag_feed_insight_missing')->default(false);
                }
                if (!Schema::hasColumn('influencers', 'admin_override_show_hidden')) {
                    $table->boolean('admin_override_show_hidden')->default(false);
                }
                if (!Schema::hasColumn('influencers', 'avg_reach_computed_at')) {
                    $table->timestamp('avg_reach_computed_at')->nullable();
                }
            });
        } else {
            // Create the table if it doesn't exist
            Schema::create('influencers', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('followers');
                $table->decimal('gamification_percentage', 5, 2)->default(0);
                $table->unsignedInteger('ig_story_avg_reach')->nullable();
                $table->unsignedInteger('ig_reel_avg_reach')->nullable();
                $table->unsignedInteger('ig_feed_avg_reach')->nullable();
                $table->boolean('flag_story_insight_missing')->default(false);
                $table->boolean('flag_reel_insight_missing')->default(false);
                $table->boolean('flag_feed_insight_missing')->default(false);
                $table->boolean('admin_override_show_hidden')->default(false);
                $table->timestamp('avg_reach_computed_at')->nullable();
                $table->timestamps();
            });
        }

        // Create ig_posts table if it doesn't exist
        if (!Schema::hasTable('ig_posts')) {
            Schema::create('ig_posts', function (Blueprint $table) {
                $table->id();
                $table->foreignId('influencer_id')->constrained('influencers')->cascadeOnDelete();
                $table->string('post_type', 16);
                $table->unsignedInteger('reach');
                $table->timestamp('posted_at');
                $table->timestamps();
            });
        }

        // Create influencer_prices table if it doesn't exist
        if (!Schema::hasTable('influencer_prices')) {
            Schema::create('influencer_prices', function (Blueprint $table) {
                $table->id();
                $table->foreignId('influencer_id')->constrained('influencers')->cascadeOnDelete();
                $table->string('campaign_type', 64);
                $table->string('post_type', 16);
                $table->decimal('price', 10, 2);
                $table->json('breakdown');
                $table->timestamp('priced_at');
                $table->timestamps();
                $table->unique(['influencer_id', 'campaign_type']);
            });
        }
    }

    public function down(): void
    {
        // Only drop tables that were created by this migration
        Schema::dropIfExists('influencer_prices');
        Schema::dropIfExists('ig_posts');
        
        // Don't drop influencers table as it might have existing data
        // Just remove the columns we added
        if (Schema::hasTable('influencers')) {
            Schema::table('influencers', function (Blueprint $table) {
                $columns = [
                    'ig_story_avg_reach', 'ig_reel_avg_reach', 'ig_feed_avg_reach',
                    'flag_story_insight_missing', 'flag_reel_insight_missing', 'flag_feed_insight_missing',
                    'admin_override_show_hidden', 'avg_reach_computed_at'
                ];
                
                foreach ($columns as $column) {
                    if (Schema::hasColumn('influencers', $column)) {
                        $table->dropColumn($column);
                    }
                }
            });
        }
    }
};
