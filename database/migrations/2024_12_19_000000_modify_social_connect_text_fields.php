<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('social_connects', function (Blueprint $table) {
            // Change token columns from VARCHAR(191) to TEXT to handle long social media tokens
            $table->text('token')->nullable()->change();
            $table->text('token_secret')->nullable()->change();

            // Change URL fields to TEXT to handle long URLs with query parameters and CDN paths
            $table->text('picture')->nullable()->change();
            $table->text('url')->nullable()->change();

            // Change social_id to TEXT to handle long platform IDs
            $table->text('social_id')->nullable()->change();
        });
    }

    public function down(): void
    {
        Schema::table('social_connects', function (Blueprint $table) {
            // Revert back to VARCHAR(191) - be careful with data truncation
            $table->string('token', 191)->nullable()->change();
            $table->string('token_secret', 191)->nullable()->change();
            $table->string('picture', 191)->nullable()->change();
            $table->string('url', 191)->nullable()->change();
            $table->string('social_id', 191)->nullable()->change();
        });
    }
};
