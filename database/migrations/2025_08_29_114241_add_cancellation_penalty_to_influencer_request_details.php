<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('influencer_request_details', function (Blueprint $table) {
            // Add new cancellation_penalty field to replace current_price penalty tracking
            $table->decimal('cancellation_penalty', 10, 2)->default(0)->after('current_price')
                ->comment('Tracks penalty amounts applied when campaigns are cancelled');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('influencer_request_details', function (Blueprint $table) {
            $table->dropColumn('cancellation_penalty');
        });
    }
};
