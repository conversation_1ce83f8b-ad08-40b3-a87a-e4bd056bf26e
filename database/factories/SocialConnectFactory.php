<?php

namespace Database\Factories;

use App\Models\SocialConnect;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class SocialConnectFactory extends Factory
{
    protected $model = SocialConnect::class;

    public function definition()
    {
        return [
            'user_id' => User::factory(),
            'media' => $this->faker->randomElement(['instagram', 'facebook', 'twitter', 'tiktok', 'youtube']),
            'social_id' => $this->faker->uuid(),
            'token_secret' => $this->faker->sha256(),
            'name' => $this->faker->userName(),
            'url' => $this->faker->url(),
            'picture' => $this->faker->imageUrl(),
            'followers' => $this->faker->numberBetween(1000, 100000),
            'following' => $this->faker->numberBetween(100, 5000),
            'posts' => $this->faker->numberBetween(50, 1000),
        ];
    }

    /**
     * Create an Instagram connection
     */
    public function instagram()
    {
        return $this->state(function (array $attributes) {
            return [
                'media' => 'instagram',
            ];
        });
    }

    /**
     * Create a Facebook connection
     */
    public function facebook()
    {
        return $this->state(function (array $attributes) {
            return [
                'media' => 'facebook',
            ];
        });
    }

    /**
     * Create a TikTok connection
     */
    public function tiktok()
    {
        return $this->state(function (array $attributes) {
            return [
                'media' => 'tiktok',
            ];
        });
    }

    /**
     * Create a connection with specific follower count
     */
    public function withFollowers(int $followers)
    {
        return $this->state(function (array $attributes) use ($followers) {
            return [
                'followers' => $followers,
            ];
        });
    }

    /**
     * Create a micro-influencer connection (1K-10K followers)
     */
    public function microInfluencer()
    {
        return $this->state(function (array $attributes) {
            return [
                'followers' => $this->faker->numberBetween(1000, 10000),
            ];
        });
    }

    /**
     * Create a macro-influencer connection (100K+ followers)
     */
    public function macroInfluencer()
    {
        return $this->state(function (array $attributes) {
            return [
                'followers' => $this->faker->numberBetween(100000, 1000000),
            ];
        });
    }
}
