<?php

namespace Database\Factories;

use App\Models\SocialPost;
use Illuminate\Database\Eloquent\Factories\Factory;
use App\Constants\PostType;

class SocialPostFactory extends Factory
{
    protected $model = SocialPost::class;

    public function definition()
    {
        return [
            'user_id' => $this->faker->numberBetween(1, 100),
            'post_id' => $this->faker->unique()->numerify('##########'),
            'media' => $this->faker->randomElement(['instagram', 'facebook', 'tiktok']),
            'text' => $this->faker->optional()->sentence(),
            'link' => $this->faker->optional()->randomElement([
                'social_pics/18013584983773358_instagram.mp4',
                'social_pics/17864567004441901_instagram.jpg'
            ]),
            'type' => $this->faker->randomElement([PostType::STORY, PostType::FEED_POST, PostType::REEL]),
            'thumbnail' => $this->faker->optional()->url(),
            'published_at' => $this->faker->optional()->dateTimeBetween('-1 month', 'now'),
            'post_category' => $this->faker->randomElement(['story', 'reel', 'post']),  // Only Instagram post types
            'campaign_deliverable' => $this->faker->boolean(30), // 30% chance of being true
            'insights' => null, // Default to null, can be overridden
        ];
    }

    /**
     * Create a social post with insights data
     */
    public function withInsights()
    {
        return $this->state(function (array $attributes) {
            $timestamp = now()->format('Y_m_d_H_i_s');
            
            return [
                'insights' => [
                    "complete__{$timestamp}" => [
                        'data' => [
                            [
                                'name' => 'views',
                                'period' => 'lifetime',
                                'values' => [
                                    ['value' => $this->faker->numberBetween(10, 1000)]
                                ],
                                'title' => 'Views',
                                'description' => 'Number of views'
                            ],
                            [
                                'name' => 'reach',
                                'period' => 'lifetime',
                                'values' => [
                                    ['value' => $this->faker->numberBetween(5, 500)]
                                ],
                                'title' => 'Reach',
                                'description' => 'Number of accounts reached'
                            ],
                            [
                                'name' => 'shares',
                                'period' => 'lifetime',
                                'values' => [
                                    ['value' => $this->faker->numberBetween(0, 50)]
                                ],
                                'title' => 'Shares',
                                'description' => 'Number of shares'
                            ]
                        ]
                    ],
                    'views' => $this->faker->numberBetween(10, 1000),
                    'reach' => $this->faker->numberBetween(5, 500),
                    'shares' => $this->faker->numberBetween(0, 50),
                    'total_interactions' => $this->faker->numberBetween(0, 100)
                ]
            ];
        });
    }

    /**
     * Create a campaign deliverable social post
     */
    public function campaignDeliverable()
    {
        return $this->state(function (array $attributes) {
            return [
                'post_category' => $this->faker->randomElement(['story', 'reel', 'post']),  // Use proper Instagram post type
                'campaign_deliverable' => true,
                'influencer_request_accept_id' => $this->faker->numberBetween(1, 100),
            ];
        });
    }

    /**
     * Create a social post with media link
     */
    public function withMedia($type = 'image')
    {
        return $this->state(function (array $attributes) use ($type) {
            $extension = $type === 'video' ? 'mp4' : 'jpg';
            
            return [
                'link' => "social_pics/{$this->faker->numerify('##########')}_instagram.{$extension}",
            ];
        });
    }
}
