<?php

namespace Database\Factories;

use App\Models\AdminPricing;
use Illuminate\Database\Eloquent\Factories\Factory;

class AdminPricingFactory extends Factory
{
    protected $model = AdminPricing::class;

    public function definition()
    {
        return [
            'media' => $this->faker->randomElement(['Instagram', 'Facebook', 'Twitter', 'TikTok', 'YouTube']),
            'type' => $this->faker->randomElement(['Boost me', 'Survey', 'Reaction-Video']),
            'country' => $this->faker->randomElement(['Standard', 'Germany', 'USA', 'UK']),
            'range' => $this->faker->randomElement(['All', '1000', '10000', '50000', '100000']),
            'cpt' => $this->faker->randomFloat(2, 1, 20),
            'estimated_reach' => $this->faker->randomFloat(2, 5, 50), // 5% to 50%
        ];
    }

    /**
     * Create Instagram pricing
     */
    public function instagram()
    {
        return $this->state(function (array $attributes) {
            return [
                'media' => 'Instagram',
            ];
        });
    }

    /**
     * Create Facebook pricing
     */
    public function facebook()
    {
        return $this->state(function (array $attributes) {
            return [
                'media' => 'Facebook',
            ];
        });
    }

    /**
     * Create TikTok pricing
     */
    public function tiktok()
    {
        return $this->state(function (array $attributes) {
            return [
                'media' => 'TikTok',
            ];
        });
    }

    /**
     * Create Boost me campaign pricing
     */
    public function boostMe()
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'Boost me',
            ];
        });
    }

    /**
     * Create Survey campaign pricing
     */
    public function survey()
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'Survey',
            ];
        });
    }

    /**
     * Create Reaction Video campaign pricing
     */
    public function reactionVideo()
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'Reaction-Video',
            ];
        });
    }

    /**
     * Create standard country pricing
     */
    public function standard()
    {
        return $this->state(function (array $attributes) {
            return [
                'country' => 'Standard',
            ];
        });
    }

    /**
     * Create pricing for all follower ranges
     */
    public function allRanges()
    {
        return $this->state(function (array $attributes) {
            return [
                'range' => 'All',
            ];
        });
    }

    /**
     * Create pricing with specific CPT
     */
    public function withCpt(float $cpt)
    {
        return $this->state(function (array $attributes) use ($cpt) {
            return [
                'cpt' => $cpt,
            ];
        });
    }

    /**
     * Create pricing with specific estimated reach
     */
    public function withEstimatedReach(float $estimatedReach)
    {
        return $this->state(function (array $attributes) use ($estimatedReach) {
            return [
                'estimated_reach' => $estimatedReach,
            ];
        });
    }

    /**
     * Create pricing without estimated reach (null)
     */
    public function withoutEstimatedReach()
    {
        return $this->state(function (array $attributes) {
            return [
                'estimated_reach' => null,
            ];
        });
    }
}
