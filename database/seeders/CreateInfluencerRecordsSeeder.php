<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\User;
use App\Models\Influencer;
use Illuminate\Database\Seeder;

class CreateInfluencerRecordsSeeder extends Seeder
{
    public function run(): void
    {
        // Get all users with user_type = 'influencer'
        $influencerUsers = User::where('user_type', 'influencer')->get();
        
        echo "Found {$influencerUsers->count()} influencer users\n";
        
        $created = 0;
        foreach ($influencerUsers as $user) {
            // Check if this user already has an influencer record
            if (!Influencer::where('user_id', $user->id)->exists()) {
                Influencer::create([
                    'user_id' => $user->id,
                    'followers' => rand(5000, 100000), // Random for testing
                    'gamification_percentage' => rand(5, 25) / 100, // 5-25%
                ]);
                $created++;
            }
        }
        
        echo "Created {$created} new influencer records\n";
        echo "Total influencer records: " . Influencer::count() . "\n";
    }
}
