<?php

namespace Database\Seeders;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $this->call(UserSeeder::class);
        $this->call(CmsPagesSeeder::class);
        $this->call(CountriesTableSeeder::class);
        $this->call(StatesTableSeeder::class);
        $this->call(CitiesTableSeeder::class);
        $this->call(DialogueSeeder::class);
        $this->call(CampaignRequestTimeSeeder::class);
        $this->call(AdminComissionSeeder::class);

        \App\Models\Influencer::factory()
            ->count(5)
            ->create()
            ->each(function ($inf) {
                foreach (['story' => 20, 'reel' => 8, 'feed' => 8] as $type => $count) {
                    \App\Models\IgPost::factory()
                        ->count($count)
                        ->create([
                            'influencer_id' => $inf->id,
                            'post_type' => $type,
                        ]);
                }
            });
    }
}
