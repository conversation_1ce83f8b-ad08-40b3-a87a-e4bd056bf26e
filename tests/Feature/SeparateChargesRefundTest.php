<?php

namespace Tests\Feature;

use App\Models\InfluencerRequestDetail;
use App\Models\User;
use App\Models\Invoice;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

/**
 * Test suite for the new Separate Charges Architecture
 * 
 * This test validates that individual influencer refunds work correctly
 * without affecting other influencers or platform fees.
 */
class SeparateChargesRefundTest extends TestCase
{
    use RefreshDatabase;
    /**
     * Test that individual influencer refunds don't affect other influencers
     *
     * @return void
     */
    public function testIndividualInfluencerRefundIsolation()
    {
        // Create test data for a multi-influencer campaign
        $campaignId = 'test_campaign_' . uniqid();

        // Create multiple influencers for the same campaign with unique charge IDs
        $influencers = collect();
        for ($i = 1; $i <= 3; $i++) {
            $influencer = InfluencerRequestDetail::create([
                'user_id' => 1,
                'influencer_detail_id' => $i,
                'compaign_id' => $campaignId,
                'compaign_title' => 'Test Campaign',
                'influencer_price' => 90.00,
                'payment_status' => InfluencerRequestDetail::STATUS_COMPLETED,
                'refund_txn_id' => 'ch_test_charge_' . $i . '_' . uniqid(),
                'advertising' => 'Boost Me',
                'media' => 'instagram',
            ]);
            $influencers->push($influencer);
        }

        $this->assertGreaterThan(1, $influencers->count(), 'Need at least 2 influencers to test refund isolation');

        // Verify each influencer has a unique refund_txn_id (charge ID)
        $chargeIds = $influencers->pluck('refund_txn_id')->toArray();
        $uniqueChargeIds = array_unique($chargeIds);

        $this->assertEquals(
            count($chargeIds),
            count($uniqueChargeIds),
            'Each influencer should have a unique charge ID for separate refunds'
        );

        // Test each influencer's refund isolation
        foreach ($influencers as $index => $influencer) {
            // Verify the charge ID is unique to this influencer
            $otherInfluencersWithSameCharge = $influencers->where('refund_txn_id', $influencer->refund_txn_id)
                ->where('id', '!=', $influencer->id)
                ->count();

            $this->assertEquals(0, $otherInfluencersWithSameCharge,
                "Influencer {$influencer->id} should have a unique charge ID");

            // Create a test invoice for this influencer
            $invoice = Invoice::create([
                'influencer_request_detail_id' => $influencer->id,
                'campaign_id' => $campaignId,
                'charge_id' => $influencer->refund_txn_id,
                'payment_amount' => $influencer->influencer_price,
                'payment_type' => 'Influencer_Payment',
                'description' => 'Test Influencer Payment',
                'payment_status' => 'Completed',
            ]);

            $this->assertEquals($influencer->refund_txn_id, $invoice->charge_id,
                "Invoice should use the same individual charge ID");
        }

        $this->assertTrue(true, 'All influencers have unique charge IDs for isolated refunds');
    }

    /**
     * Test the refund process for a single influencer
     *
     * @return void
     */
    public function testSingleInfluencerRefundProcess()
    {
        // Create a test influencer with refund_txn_id
        $influencer = InfluencerRequestDetail::create([
            'user_id' => 1,
            'influencer_detail_id' => 1,
            'compaign_id' => 'test_campaign_single_' . uniqid(),
            'compaign_title' => 'Test Single Campaign',
            'influencer_price' => 135.00,
            'payment_status' => InfluencerRequestDetail::STATUS_COMPLETED,
            'refund_txn_id' => 'ch_test_single_' . uniqid(),
            'advertising' => 'Boost Me',
            'media' => 'instagram',
        ]);

        // Verify the influencer has a unique charge ID
        $this->assertNotNull($influencer->refund_txn_id, 'Influencer should have a charge ID for refunds');

        // Check if this charge ID is used by other influencers (should not be)
        $otherInfluencersWithSameCharge = InfluencerRequestDetail::where('refund_txn_id', $influencer->refund_txn_id)
            ->where('id', '!=', $influencer->id)
            ->count();

        $this->assertEquals(0, $otherInfluencersWithSameCharge,
            'This charge ID should be unique to this influencer');

        // Create a test invoice for this influencer
        $invoice = Invoice::create([
            'influencer_request_detail_id' => $influencer->id,
            'campaign_id' => $influencer->compaign_id,
            'charge_id' => $influencer->refund_txn_id,
            'payment_amount' => $influencer->influencer_price,
            'payment_type' => 'Influencer_Payment',
            'description' => 'Test Single Influencer Payment',
            'payment_status' => 'Completed',
        ]);

        $this->assertEquals($influencer->refund_txn_id, $invoice->charge_id,
            'Invoice should reference the same charge ID');
    }

    /**
     * Test platform fee isolation from influencer refunds
     *
     * @return void
     */
    public function testPlatformFeeIsolation()
    {
        $campaignId = 'test_campaign_platform_' . uniqid();

        // Create a platform fee invoice
        $platformInvoice = Invoice::create([
            'campaign_id' => $campaignId,
            'charge_id' => 'ch_platform_fee_' . uniqid(),
            'payment_amount' => 50.00,
            'payment_type' => 'Platform_Fee',
            'description' => 'Non-refundable platform fee for campaign services',
            'payment_status' => 'Completed',
        ]);

        // Create influencer invoices for the same campaign
        $influencerInvoices = collect();
        for ($i = 1; $i <= 2; $i++) {
            $invoice = Invoice::create([
                'influencer_request_detail_id' => $i,
                'campaign_id' => $campaignId,
                'charge_id' => 'ch_influencer_' . $i . '_' . uniqid(),
                'payment_amount' => 100.00,
                'payment_type' => 'Influencer_Payment',
                'description' => 'Influencer payment for campaign',
                'payment_status' => 'Completed',
            ]);
            $influencerInvoices->push($invoice);
        }

        // Verify platform fee has different charge ID from all influencers
        foreach ($influencerInvoices as $invoice) {
            $this->assertNotEquals(
                $platformInvoice->charge_id,
                $invoice->charge_id,
                "Platform fee should have different charge ID from influencer {$invoice->influencer_request_detail_id}"
            );
        }

        // Verify platform fee is marked as non-refundable in description
        $this->assertStringContainsString(
            'Non-refundable',
            $platformInvoice->description,
            'Platform fee should be marked as non-refundable'
        );
    }
}
