<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\SocialPost;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class AdminSocialPostTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create an admin user
        $this->admin = User::factory()->create([
            'user_type' => 'admin',
            'email' => '<EMAIL>'
        ]);
    }

    /** @test */
    public function admin_can_view_social_posts_list()
    {
        // Create some test social posts
        SocialPost::factory()->count(5)->create();

        $response = $this->actingAs($this->admin, 'admin')
                        ->get('/admin/manage-social-posts');

        $response->assertStatus(200);
        $response->assertViewIs('admin.social-posts.manage');
        $response->assertViewHas('socialPosts');
    }

    /** @test */
    public function admin_can_view_individual_social_post()
    {
        $socialPost = SocialPost::factory()->create([
            'insights' => [
                'complete__2025_08_13_09_00_12' => [
                    'data' => [
                        [
                            'name' => 'views',
                            'period' => 'lifetime',
                            'values' => [['value' => 10]],
                            'title' => 'Views',
                            'description' => 'Number of views'
                        ]
                    ]
                ],
                'views' => 10,
                'reach' => 9
            ]
        ]);

        $response = $this->actingAs($this->admin, 'admin')
                        ->get("/admin/social-posts/{$socialPost->id}");

        $response->assertStatus(200);
        $response->assertViewIs('admin.social-posts.show');
        $response->assertViewHas('socialPost');
        $response->assertViewHas('insightsData');
    }

    /** @test */
    public function admin_can_filter_social_posts_by_type()
    {
        SocialPost::factory()->create(['type' => 'story']);
        SocialPost::factory()->create(['type' => 'post']);

        $response = $this->actingAs($this->admin, 'admin')
                        ->get('/admin/manage-social-posts?type=story');

        $response->assertStatus(200);
        $response->assertViewHas('socialPosts');
    }

    /** @test */
    public function admin_can_search_social_posts()
    {
        SocialPost::factory()->create(['text' => 'Test post content']);
        SocialPost::factory()->create(['text' => 'Another post']);

        $response = $this->actingAs($this->admin, 'admin')
                        ->get('/admin/manage-social-posts?search=Test');

        $response->assertStatus(200);
        $response->assertViewHas('socialPosts');
    }

    /** @test */
    public function non_admin_cannot_access_social_posts()
    {
        $user = User::factory()->create(['user_type' => 'customer']);

        $response = $this->actingAs($user)
                        ->get('/admin/manage-social-posts');

        $response->assertStatus(403);
    }

    /** @test */
    public function insights_data_is_properly_processed()
    {
        $socialPost = SocialPost::factory()->create([
            'insights' => [
                'complete__2025_08_13_09_00_12' => [
                    'data' => [
                        [
                            'name' => 'views',
                            'values' => [['value' => 10]],
                            'title' => 'Views'
                        ]
                    ]
                ],
                'complete__2025_08_12_08_00_00' => [
                    'data' => [
                        [
                            'name' => 'reach',
                            'values' => [['value' => 5]],
                            'title' => 'Reach'
                        ]
                    ]
                ]
            ]
        ]);

        $response = $this->actingAs($this->admin, 'admin')
                        ->get("/admin/social-posts/{$socialPost->id}");

        $response->assertStatus(200);
        
        // Check that insights are ordered by timestamp (descending)
        $insightsData = $response->viewData('insightsData');
        $timestamps = array_keys($insightsData);
        
        $this->assertEquals('2025_08_13_09_00_12', $timestamps[0]);
        $this->assertEquals('2025_08_12_08_00_00', $timestamps[1]);
    }
}
