<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\Influencer;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MarketplaceVisibilityTest extends TestCase
{
    use RefreshDatabase;

    public function testHiddenWhenFlagSet(): void
    {
        Influencer::factory()->create(['flag_story_insight_missing' => true]);
        $visible = Influencer::visibleForCampaign('Boost Me')->get();
        $this->assertCount(0, $visible);
    }

    public function testVisibleWithOverride(): void
    {
        Influencer::factory()->create(['flag_story_insight_missing' => true, 'admin_override_show_hidden' => true]);
        $visible = Influencer::visibleForCampaign('Boost Me')->get();
        $this->assertCount(1, $visible);
    }

    public function testVisibleWhenFlagsCleared(): void
    {
        Influencer::factory()->create(['flag_story_insight_missing' => false]);
        $visible = Influencer::visibleForCampaign('Boost Me')->get();
        $this->assertCount(1, $visible);
    }
}
