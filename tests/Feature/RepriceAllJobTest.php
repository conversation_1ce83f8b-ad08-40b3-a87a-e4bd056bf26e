<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Jobs\RepriceAllJob;
use App\Models\IgPost;
use App\Models\Influencer;
use App\Models\InfluencerPrice;
use App\Services\PricingCalculator;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RepriceAllJobTest extends TestCase
{
    use RefreshDatabase;

    private function seedPosts(Influencer $inf): void
    {
        foreach (['story' => 20, 'reel' => 8, 'feed' => 8] as $type => $count) {
            IgPost::factory()->count($count)->create([
                'influencer_id' => $inf->id,
                'post_type' => $type,
                'reach' => 1000,
            ]);
        }
    }

    public function testCreatesAndUpdatesPrices(): void
    {
        $infls = Influencer::factory()->count(2)->create(['followers' => 10000, 'gamification_percentage' => 0]);
        foreach ($infls as $inf) {
            $this->seedPosts($inf);
        }

        // RepriceAllJob should compute reach metrics and then price using the updated influencer
        (new RepriceAllJob())->handle(new PricingCalculator());

        $this->assertEquals(6, InfluencerPrice::count()); // 2 influencers * 3 campaign types = 6 prices
        $firstPrice = InfluencerPrice::first()->price;

        // The expected price calculation for 'Boost Me' (story type):
        // Base: 4.5 * (10000/1000) = 45, Estimated: 10000 * 0.05 = 500
        // Avg reach: 1000 (from seeded posts), Multiplier: 1000/500 = 2.0
        // Final: 45 * 2.0 = 90.0
        $this->assertEquals(90.0, $firstPrice);

        // run again should be idempotent
        (new RepriceAllJob())->handle(new PricingCalculator());
        $this->assertEquals(6, InfluencerPrice::count());
        $this->assertEquals($firstPrice, InfluencerPrice::first()->price);
    }
}
