<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\SocialConnect;
use App\Models\SocialPost;
use App\Models\AdminPricing;
use App\Models\AdminGamification;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ReachBasedPricingIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create([
            'trophy' => 'Bronze',
            'country' => 'Standard',
            'is_small_business_owner' => false,
        ]);

        // Create gamification data
        AdminGamification::factory()->create([
            'select_type' => 'Pricing & Rank',
            'type' => 'Bronze',
            'pricing' => 100, // 100% multiplier
        ]);

        AdminGamification::factory()->create([
            'select_type' => 'Pricing & Rank',
            'type' => 'Silver',
            'pricing' => 130, // 130% multiplier for next level
        ]);
    }

    /** @test */
    public function it_calculates_reach_based_pricing_for_boost_me_campaign()
    {
        // Setup social media connection
        SocialConnect::factory()->create([
            'user_id' => $this->user->id,
            'media' => 'instagram',
            'followers' => 10000,
        ]);

        // Setup admin pricing with estimated reach
        AdminPricing::factory()->create([
            'media' => 'Instagram',
            'type' => 'Boost me',
            'country' => 'Standard',
            'range' => 'All',
            'cpt' => 4.0,
            'estimated_reach' => 20.00, // 20%
        ]);

        // Create recent story posts with reach data
        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'story',
            'published_at' => Carbon::now()->subHours(12),
            'insights' => ['reach' => 2500]
        ]);

        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'story',
            'published_at' => Carbon::now()->subHours(6),
            'insights' => ['reach' => 3500]
        ]);

        // Make API request
        $response = $this->actingAs($this->user)
            ->postJson('/getAdminPricing', [
                'media' => 'instagram',
                'type' => 'Boost me'
            ]);

        $response->assertStatus(200);
        
        $data = $response->json();
        
        // Verify reach-based pricing was applied
        $this->assertTrue($data['used_reach_pricing']);
        $this->assertEquals(1.5, $data['reach_multiplier']); // 3000 / (10000 * 0.20) = 1.5
        
        // Expected calculation:
        // Base CPT price: 4.0 * (10000/1000) = 40.0
        // After gamification: 40.0 * 1.0 = 40.0
        // After reach adjustment: 40.0 * 1.5 = 60.0
        // After VAT (19%): 60.0 * 1.19 = 71.4
        // After CIF provision (80%): 71.4 * 0.8 = 57.12
        $this->assertEquals('57.12', $data['price']);
    }

    /** @test */
    public function it_falls_back_to_standard_pricing_when_no_recent_posts()
    {
        // Setup social media connection
        SocialConnect::factory()->create([
            'user_id' => $this->user->id,
            'media' => 'instagram',
            'followers' => 10000,
        ]);

        // Setup admin pricing with estimated reach
        AdminPricing::factory()->create([
            'media' => 'Instagram',
            'type' => 'Boost me',
            'country' => 'Standard',
            'range' => 'All',
            'cpt' => 4.0,
            'estimated_reach' => 20.00,
        ]);

        // No recent posts (average reach will be 0)

        // Make API request
        $response = $this->actingAs($this->user)
            ->postJson('/getAdminPricing', [
                'media' => 'instagram',
                'type' => 'Boost me'
            ]);

        $response->assertStatus(200);
        
        $data = $response->json();
        
        // Verify reach-based pricing was NOT applied
        $this->assertFalse($data['used_reach_pricing']);
        $this->assertEquals(1.0, $data['reach_multiplier']);
        
        // Expected calculation (standard pricing):
        // Base CPT price: 4.0 * (10000/1000) = 40.0
        // After gamification: 40.0 * 1.0 = 40.0
        // After VAT (19%): 40.0 * 1.19 = 47.6
        // After CIF provision (80%): 47.6 * 0.8 = 38.08
        $this->assertEquals('38.08', $data['price']);
    }

    /** @test */
    public function it_handles_survey_campaign_with_reach_pricing()
    {
        // Setup social media connection
        SocialConnect::factory()->create([
            'user_id' => $this->user->id,
            'media' => 'instagram',
            'followers' => 5000,
        ]);

        // Setup admin pricing for Survey
        AdminPricing::factory()->create([
            'media' => 'Instagram',
            'type' => 'Survey',
            'country' => 'Standard',
            'range' => 'All',
            'cpt' => 3.0,
            'estimated_reach' => 15.00, // 15%
        ]);

        // Create recent story posts (Survey maps to story)
        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'story',
            'published_at' => Carbon::now()->subHours(8),
            'insights' => ['reach' => 1200]
        ]);

        // Make API request
        $response = $this->actingAs($this->user)
            ->postJson('/getAdminPricing', [
                'media' => 'instagram',
                'type' => 'Survey'
            ]);

        $response->assertStatus(200);
        
        $data = $response->json();
        
        // Verify reach-based pricing was applied
        $this->assertTrue($data['used_reach_pricing']);
        
        // Expected reach multiplier: 1200 / (5000 * 0.15) = 1200 / 750 = 1.6
        $this->assertEquals(1.6, $data['reach_multiplier']);
    }

    /** @test */
    public function it_handles_reaction_video_campaign_with_reach_pricing()
    {
        // Setup social media connection
        SocialConnect::factory()->create([
            'user_id' => $this->user->id,
            'media' => 'instagram',
            'followers' => 20000,
        ]);

        // Setup admin pricing for Reaction Video
        AdminPricing::factory()->create([
            'media' => 'Instagram',
            'type' => 'Reaction-Video',
            'country' => 'Standard',
            'range' => 'All',
            'cpt' => 6.0,
            'estimated_reach' => 25.00, // 25%
        ]);

        // Create recent reel posts (Reaction video maps to reel)
        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'reel',
            'published_at' => Carbon::now()->subHours(4),
            'insights' => ['reach' => 4000]
        ]);

        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'reel',
            'published_at' => Carbon::now()->subHours(10),
            'insights' => ['reach' => 6000]
        ]);

        // Make API request
        $response = $this->actingAs($this->user)
            ->postJson('/getAdminPricing', [
                'media' => 'instagram',
                'type' => 'Reaction video'
            ]);

        $response->assertStatus(200);
        
        $data = $response->json();
        
        // Verify reach-based pricing was applied
        $this->assertTrue($data['used_reach_pricing']);
        
        // Expected reach multiplier: 5000 / (20000 * 0.25) = 5000 / 5000 = 1.0
        $this->assertEquals(1.0, $data['reach_multiplier']);
    }

    /** @test */
    public function it_skips_reach_pricing_when_no_estimated_reach_configured()
    {
        // Setup social media connection
        SocialConnect::factory()->create([
            'user_id' => $this->user->id,
            'media' => 'instagram',
            'followers' => 10000,
        ]);

        // Setup admin pricing WITHOUT estimated reach
        AdminPricing::factory()->create([
            'media' => 'Instagram',
            'type' => 'Boost me',
            'country' => 'Standard',
            'range' => 'All',
            'cpt' => 4.0,
            'estimated_reach' => null, // No estimated reach configured
        ]);

        // Create recent story posts
        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'story',
            'published_at' => Carbon::now()->subHours(12),
            'insights' => ['reach' => 3000]
        ]);

        // Make API request
        $response = $this->actingAs($this->user)
            ->postJson('/getAdminPricing', [
                'media' => 'instagram',
                'type' => 'Boost me'
            ]);

        $response->assertStatus(200);
        
        $data = $response->json();
        
        // Verify reach-based pricing was NOT applied
        $this->assertFalse($data['used_reach_pricing']);
        $this->assertEquals(1.0, $data['reach_multiplier']);
    }

    /** @test */
    public function it_handles_small_business_owner_pricing()
    {
        // Update user to be small business owner
        $this->user->update(['is_small_business_owner' => true]);

        // Setup social media connection
        SocialConnect::factory()->create([
            'user_id' => $this->user->id,
            'media' => 'instagram',
            'followers' => 10000,
        ]);

        // Setup admin pricing with estimated reach
        AdminPricing::factory()->create([
            'media' => 'Instagram',
            'type' => 'Boost me',
            'country' => 'Standard',
            'range' => 'All',
            'cpt' => 4.0,
            'estimated_reach' => 20.00,
        ]);

        // Create recent story posts
        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'story',
            'published_at' => Carbon::now()->subHours(12),
            'insights' => ['reach' => 3000]
        ]);

        // Make API request
        $response = $this->actingAs($this->user)
            ->postJson('/getAdminPricing', [
                'media' => 'instagram',
                'type' => 'Boost me'
            ]);

        $response->assertStatus(200);
        
        $data = $response->json();
        
        // Verify reach-based pricing was applied
        $this->assertTrue($data['used_reach_pricing']);
        $this->assertEquals(1.5, $data['reach_multiplier']);
        
        // Expected calculation for small business owner (no VAT):
        // Base CPT price: 4.0 * (10000/1000) = 40.0
        // After gamification: 40.0 * 1.0 = 40.0
        // After reach adjustment: 40.0 * 1.5 = 60.0
        // No VAT for small business owner
        // After CIF provision (80%): 60.0 * 0.8 = 48.00
        $this->assertEquals('48.00', $data['price']);
    }
}
