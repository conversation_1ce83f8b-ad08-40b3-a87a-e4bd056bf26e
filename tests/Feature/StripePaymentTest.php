<?php

namespace Tests\Feature;

use App\Models\InfluencerRequestDetail;
use App\Models\InfluencerRequestAccept;
use App\Models\InfluencerDetail;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class StripePaymentTest extends TestCase
{
    use RefreshDatabase;
    /**
     * Test the payment calculation logic.
     *
     * @return void
     */
    public function testPaymentCalculations()
    {
        // Create test data
        $campaignId = 'test_campaign_' . uniqid();

        // Create test users (influencers)
        $users = collect();
        for ($i = 1; $i <= 2; $i++) {
            $user = User::create([
                'first_name' => 'Test',
                'last_name' => 'Influencer ' . $i,
                'email' => 'influencer' . $i . '@test.com',
                'user_type' => 'influencer',
                'password' => bcrypt('password'),
                'is_small_business_owner' => $i === 1 ? true : false, // First user is small business, second is not
            ]);
            $users->push($user);
        }

        // Create influencer details
        $influencerDetails = collect();
        foreach ($users as $index => $user) {
            $detail = InfluencerDetail::create([
                'user_id' => $user->id,
                'category_id' => 1,
                'influencer_type' => 'micro',
                'gender' => 'female',
                'ages' => '18-24',
                'content_language' => 'en',
                'content_attracts' => 'lifestyle',
                'publish' => 1,
            ]);
            $influencerDetails->push($detail);
        }

        // Create campaign requests
        $influencers = collect();
        foreach ($influencerDetails as $index => $detail) {
            $requestDetail = InfluencerRequestDetail::create([
                'user_id' => $detail->user_id,
                'influencer_detail_id' => $detail->id,
                'compaign_id' => $campaignId,
                'compaign_title' => 'Test Payment Campaign',
                'influencer_price' => 100.00,
                'payment_status' => InfluencerRequestDetail::STATUS_COMPLETED,
                'advertising' => 'Boost Me',
                'media' => 'instagram',
            ]);

            // Create acceptance record
            InfluencerRequestAccept::create([
                'user_id' => $detail->user_id,
                'influencer_request_detail_id' => $requestDetail->id,
                'status' => 1,
                'request' => 1, // Accepted
            ]);

            $influencers->push($requestDetail);
        }
        
        $this->assertNotEmpty($influencers, 'No influencers found for the campaign');

        // Calculate the total price and platform fee
        $totalAmount = $influencers->sum('influencer_price');
        $platformFee = max($totalAmount * 0.05, 2);
        $platformVatAmount = $platformFee * 0.19;
        $platformTotal = $platformFee + $platformVatAmount;

        $this->assertGreaterThan(0, $totalAmount, 'Total amount should be greater than 0');
        $this->assertEquals(200.00, $totalAmount, 'Total amount should be 200.00 (2 influencers * 100.00 each)');

        // Test calculations for each influencer
        foreach ($influencers as $index => $influencer) {
            // Load the relationships
            $influencer->load(['influencer_request_accepts', 'influencerdetails']);

            if ($influencer->influencer_request_accepts) {
                $originalAmount = $influencer->influencer_price;
                $originalAmountCents = (int) round($originalAmount * 100);

                // Get influencer user
                $influencerUser = User::find($influencer->influencerdetails->user_id);

                // Default VAT amount in cents (0 if small business)
                $influencerVatAmountCents = 0;
                if (!$influencerUser->is_small_business_owner) {
                    $influencerVatAmountCents = (int) round($originalAmountCents * 0.19);
                }

                // Total amount including VAT
                $totalAmountCents = $originalAmountCents + $influencerVatAmountCents;

                // Calculate 20% commission
                $commissionRate = 0.20;
                $commissionAmountCents = (int) round($originalAmountCents * $commissionRate);
                $commissionVatAmountCents = (int) round($commissionAmountCents * 0.19);
                $totalCommissionCents = $commissionAmountCents + $commissionVatAmountCents;

                // Calculate influencer's final amount
                $influencerAmountCents = $totalAmountCents - $totalCommissionCents;

                $this->assertGreaterThan(0, $influencerAmountCents, 'Influencer amount should be greater than 0');
                $this->assertEquals($totalAmountCents - $totalCommissionCents, $influencerAmountCents, 'Influencer amount calculation should be correct');

                // Test specific calculations based on small business status
                if ($index === 0) {
                    // First influencer is small business owner (no VAT)
                    $this->assertEquals(0, $influencerVatAmountCents, 'Small business owner should have no VAT');
                    $this->assertEquals(10000, $originalAmountCents, 'Original amount should be 100.00 EUR');
                } else {
                    // Second influencer is not small business owner (has VAT)
                    $this->assertEquals(1900, $influencerVatAmountCents, 'Non-small business should have 19% VAT');
                    $this->assertEquals(11900, $totalAmountCents, 'Total with VAT should be 119.00 EUR');
                }
            }
        }
    }

    /**
     * Helper method for debugging payment calculations (not a test).
     * This method can be called manually for debugging purposes.
     *
     * @param string|null $campaignId Optional campaign ID to test with
     * @return void
     */
    public function debugPaymentCalculations($campaignId = null)
    {
        // This is a helper method, not a test
        // It can be used for manual debugging but won't be run as a test
        $this->markTestSkipped('This is a debug helper method, not a test');
    }
}
