<?php

namespace Tests\Feature;

use App\Models\InfluencerRequestDetail;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class StripePaymentTest extends TestCase
{
    /**
     * Test the payment calculation logic.
     *
     * @return void
     */
    public function testPaymentCalculations()
    {
        // Find a test campaign
        $testCampaign = InfluencerRequestDetail::whereNotNull('compaign_id')
            ->whereHas('influencer_request_accepts')
            ->first();
        
        if (!$testCampaign) {
            $this->markTestSkipped('No suitable test campaign found. Create a campaign with accepted influencers first.');
        }
        
        // Get influencers for the campaign
        $influencers = InfluencerRequestDetail::where('compaign_id', $testCampaign->compaign_id)->get();
        
        $this->assertNotEmpty($influencers, 'No influencers found for the campaign');
        
        // Calculate the total price and platform fee
        $totalAmount = $influencers->sum('discount_price');
        $platformFee = max($totalAmount * 0.05, 2);
        $platformVatAmount = $platformFee * 0.19;
        $platformTotal = $platformFee + $platformVatAmount;
        
        $this->assertGreaterThan(0, $totalAmount, 'Total amount should be greater than 0');
        
        // Test calculations for each influencer
        foreach ($influencers as $influencer) {
            if (isset($influencer->influencer_request_accepts)) {
                $originalAmount = $influencer->discount_price;
                $originalAmountCents = (int) round($originalAmount * 100);
                
                // Get influencer user
                $influencerUser = User::find($influencer->influencerdetails->user_id);
                
                // Default VAT amount in cents (0 if small business)
                $influencerVatAmountCents = 0;
                if (!$influencerUser->is_small_business_owner) {
                    $influencerVatAmountCents = (int) round($originalAmountCents * 0.19);
                }
                
                // Total amount including VAT
                $totalAmountCents = $originalAmountCents + $influencerVatAmountCents;
                
                // Calculate 20% commission
                $commissionRate = 0.20;
                $commissionAmountCents = (int) round($originalAmountCents * $commissionRate);
                $commissionVatAmountCents = (int) round($commissionAmountCents * 0.19);
                $totalCommissionCents = $commissionAmountCents + $commissionVatAmountCents;
                
                // Calculate influencer's final amount
                $influencerAmountCents = $totalAmountCents - $totalCommissionCents;
                
                $this->assertGreaterThan(0, $influencerAmountCents, 'Influencer amount should be greater than 0');
                $this->assertEquals($totalAmountCents - $totalCommissionCents, $influencerAmountCents, 'Influencer amount calculation should be correct');
            }
        }
    }

    /**
     * Test the payment calculation logic with detailed debugging.
     * 
     * @param string|null $campaignId Optional campaign ID to test with
     * @return void
     */
    public function debugPaymentCalculations($campaignId = null)
    {
        // Find a test campaign
        $query = InfluencerRequestDetail::whereNotNull('compaign_id')
            ->whereHas('influencer_request_accepts');
        
        if ($campaignId) {
            $query->where('compaign_id', $campaignId);
        }
        
        $testCampaign = $query->first();
        
        if (!$testCampaign) {
            echo "No suitable test campaign found. Create a campaign with accepted influencers first.\n";
            return;
        }
        
        echo "Campaign ID: " . $testCampaign->compaign_id . "\n\n";
        
        // Get influencers for the campaign
        $influencers = InfluencerRequestDetail::where('compaign_id', $testCampaign->compaign_id)->get();
        
        echo "Total Influencers: " . $influencers->count() . "\n\n";
        
        if ($influencers->isEmpty()) {
            echo "No influencers found for the campaign.\n";
            return;
        }
        
        // Calculate the total price and platform fee
        $totalAmount = $influencers->sum('discount_price');
        $platformFee = max($totalAmount * 0.05, 2);
        $platformVatAmount = $platformFee * 0.19;
        $platformTotal = $platformFee + $platformVatAmount;
        
        echo "Payment Calculations:\n";
        echo "- Total Amount: " . number_format($totalAmount, 2) . " EUR\n";
        echo "- Platform Fee (5% or min 2 EUR): " . number_format($platformFee, 2) . " EUR\n";
        echo "- Platform Fee VAT (19%): " . number_format($platformVatAmount, 2) . " EUR\n";
        echo "- Platform Total: " . number_format($platformTotal, 2) . " EUR\n\n";
        
        $totalInfluencerAmount = 0;
        $totalInfluencerVat = 0;
        $totalCommission = 0;
        $totalCommissionVat = 0;
        
        // Test calculations for each influencer
        foreach ($influencers as $index => $influencer) {
            if (isset($influencer->influencer_request_accepts)) {
                echo "Influencer #" . ($index + 1) . " (ID: " . $influencer->id . "):\n";
                echo "- Discount Price (🤦🏾): " . number_format($influencer->discount_price, 2) . " EUR\n";
                
                $originalAmount = $influencer->discount_price;
                $originalAmountCents = (int) round($originalAmount * 100);
                
                echo "- Original Amount: " . number_format($originalAmount, 2) . " EUR\n";
                echo "- Original Amount (cents): " . $originalAmountCents . "\n";
                
                // Get influencer user
                $influencerUser = User::find($influencer->influencerdetails->user_id);
                
                echo "- Influencer User ID: " . $influencerUser->id . "\n";
                echo "- Is Small Business Owner: " . ($influencerUser->is_small_business_owner ? "Yes" : "No") . "\n";
                
                // Default VAT amount in cents (0 if small business)
                $influencerVatAmountCents = 0;
                if (!$influencerUser->is_small_business_owner) {
                    $influencerVatAmountCents = (int) round($originalAmountCents * 0.19);
                }
                
                echo "- Influencer VAT Amount (cents): " . $influencerVatAmountCents . "\n";
                
                // Total amount including VAT
                $totalAmountCents = $originalAmountCents + $influencerVatAmountCents;
                
                echo "- Total Amount with VAT (cents): " . $totalAmountCents . "\n";
                
                // Calculate 20% commission
                $commissionRate = 0.20;
                $commissionAmountCents = (int) round($originalAmountCents * $commissionRate);
                $commissionVatAmountCents = (int) round($commissionAmountCents * 0.19);
                $totalCommissionCents = $commissionAmountCents + $commissionVatAmountCents;
                
                echo "- Commission Rate: " . ($commissionRate * 100) . "%\n";
                echo "- Commission Amount (cents): " . $commissionAmountCents . "\n";
                echo "- Commission VAT Amount (cents): " . $commissionVatAmountCents . "\n";
                echo "- Total Commission (cents): " . $totalCommissionCents . "\n";
                
                // Calculate influencer's final amount
                $influencerAmountCents = $totalAmountCents - $totalCommissionCents;
                
                echo "- Influencer Final Amount (cents): " . $influencerAmountCents . "\n";
                echo "- Influencer Final Amount: " . number_format($influencerAmountCents / 100, 2) . " EUR\n";
                
                // Verify calculation
                $calculationCheck = $totalAmountCents - $totalCommissionCents;
                $isCalculationCorrect = ($calculationCheck === $influencerAmountCents);
                
                echo "- Calculation Check: " . $calculationCheck . "\n";
                echo "- Is Calculation Correct: " . ($isCalculationCorrect ? "Yes" : "No") . "\n\n";
                
                // Add to totals
                $totalInfluencerAmount += $originalAmountCents;
                $totalInfluencerVat += $influencerVatAmountCents;
                $totalCommission += $commissionAmountCents;
                $totalCommissionVat += $commissionVatAmountCents;
            }
        }
        
        // Summary
        echo "Summary:\n";
        echo "- Total Influencer Amount: " . number_format($totalInfluencerAmount / 100, 2) . " EUR\n";
        echo "- Total Influencer VAT: " . number_format($totalInfluencerVat / 100, 2) . " EUR\n";
        echo "- Total Commission: " . number_format($totalCommission / 100, 2) . " EUR\n";
        echo "- Total Commission VAT: " . number_format($totalCommissionVat / 100, 2) . " EUR\n";
        echo "- Platform Fee: " . number_format($platformFee, 2) . " EUR\n";
        echo "- Platform Fee VAT: " . number_format($platformVatAmount, 2) . " EUR\n";
        echo "- Grand Total: " . number_format(($totalInfluencerAmount + $totalInfluencerVat + ($platformFee * 100)) / 100, 2) . " EUR\n";
    }
}
