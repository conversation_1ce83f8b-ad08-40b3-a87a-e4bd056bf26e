<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ExampleTest extends TestCase
{
    use RefreshDatabase;

    /**
     * A basic test example.
     *
     * @return void
     */
    public function testBasicTest()
    {
        // Test a simple GET request to the cities endpoint
        // This endpoint should return data without complex dependencies
        $response = $this->get('/get-cities?country=1');

        // Should return a 200 status (even if no cities found)
        $response->assertStatus(200);
    }
}
