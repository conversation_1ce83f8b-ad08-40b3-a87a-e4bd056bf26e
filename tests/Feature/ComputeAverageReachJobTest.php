<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Jobs\ComputeAverageReachJob;
use App\Models\IgPost;
use App\Models\Influencer;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ComputeAverageReachJobTest extends TestCase
{
    use RefreshDatabase;

    public function testThresholdsFlagsAndAverages(): void
    {
        $inf = Influencer::factory()->create();

        foreach (range(1, 15) as $i) {
            IgPost::create([
                'influencer_id' => $inf->id,
                'post_type' => 'story',
                'reach' => $i,
                'posted_at' => now()->subDays($i),
            ]);
        }

        IgPost::factory()->count(4)->create([
            'influencer_id' => $inf->id,
            'post_type' => 'reel',
        ]);

        (new ComputeAverageReachJob($inf->id))->handle();
        $inf->refresh();

        $this->assertEquals(8, $inf->ig_story_avg_reach); // after trimming
        $this->assertFalse($inf->flag_story_insight_missing);
        $this->assertTrue($inf->flag_reel_insight_missing);
        $this->assertNotNull($inf->avg_reach_computed_at);
    }
}
