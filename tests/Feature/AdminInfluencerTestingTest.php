<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\User;
use App\Models\Influencer;
use App\Models\IgPost;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AdminInfluencerTestingTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private User $adminUser;
    private Influencer $influencer;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test admin user
        $this->adminUser = User::create([
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'admin',
        ]);

        // Create test user and influencer
        $this->user = User::create([
            'first_name' => 'Test',
            'last_name' => 'Influencer',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'influencer',
        ]);

        $this->influencer = Influencer::create([
            'followers' => 25000,
            'gamification_percentage' => 0.15,
        ]);
    }

    public function testIndexPageLoads(): void
    {
        $response = $this->actingAs($this->adminUser)->get('/admin/influencer-testing');

        $response->assertStatus(200);
        $response->assertViewIs('admin.influencer-testing.index');
        $response->assertViewHas('influencers');
    }

    public function testShowPageLoads(): void
    {
        $response = $this->actingAs($this->adminUser)->get("/admin/influencer-testing/{$this->influencer->id}");

        $response->assertStatus(200);
        $response->assertViewIs('admin.influencer-testing.show');
        $response->assertViewHas(['influencer', 'igPosts', 'postCounts', 'currentPrices']);
    }

    public function testGenerateFakePosts(): void
    {
        $response = $this->actingAs($this->adminUser)->post("/admin/influencer-testing/{$this->influencer->id}/generate-posts", [
            'post_type' => 'story',
            'count' => 5,
            'min_reach' => 500,
            'max_reach' => 1500,
            'days_back' => 30,
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify posts were created
        $this->assertEquals(5, IgPost::where('influencer_id', $this->influencer->id)->count());

        $posts = IgPost::where('influencer_id', $this->influencer->id)->get();
        foreach ($posts as $post) {
            $this->assertEquals('story', $post->post_type);
            $this->assertGreaterThanOrEqual(500, $post->reach);
            $this->assertLessThanOrEqual(1500, $post->reach);
        }
    }

    public function testCalculatePriceAjax(): void
    {
        // Create some test posts first
        for ($i = 0; $i < 20; $i++) {
            IgPost::create([
                'influencer_id' => $this->influencer->id,
                'post_type' => 'story',
                'reach' => rand(800, 1200),
                'posted_at' => now()->subDays(rand(1, 60)),
            ]);
        }

        // Recalculate reach
        $job = new \App\Jobs\ComputeAverageReachJob($this->influencer->id);
        $job->handle();

        $response = $this->actingAs($this->adminUser)->postJson("/admin/influencer-testing/{$this->influencer->id}/calculate-price", [
            'campaign_type' => 'Boost Me',
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'campaign_type',
            'price',
            'breakdown' => [
                'followers',
                'tier',
                'gamification_percentage',
                'estimated_reach',
                'avg_reach',
                'reach_multiplier',
                'post_type',
                'campaign_type',
            ],
            'influencer',
        ]);

        $data = $response->json();
        $this->assertTrue($data['success']);
        $this->assertEquals('Boost Me', $data['campaign_type']);
        $this->assertIsFloat($data['price']);
        $this->assertGreaterThan(0, $data['price']);
    }

    public function testClearPosts(): void
    {
        // Create some test posts
        IgPost::create([
            'influencer_id' => $this->influencer->id,
            'post_type' => 'story',
            'reach' => 1000,
            'posted_at' => now()->subDays(10),
        ]);

        $this->assertEquals(1, IgPost::where('influencer_id', $this->influencer->id)->count());

        $response = $this->actingAs($this->adminUser)->delete("/admin/influencer-testing/{$this->influencer->id}/clear-posts");

        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        // Verify posts were deleted
        $this->assertEquals(0, IgPost::where('influencer_id', $this->influencer->id)->count());
    }

    public function testUpdateInfluencer(): void
    {
        $response = $this->actingAs($this->adminUser)->put("/admin/influencer-testing/{$this->influencer->id}/update", [
            'followers' => 50000,
            'gamification_percentage' => 20, // Will be converted to 0.20
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->influencer->refresh();
        $this->assertEquals(50000, $this->influencer->followers);
        $this->assertEquals(0.20, $this->influencer->gamification_percentage);
    }

    public function testCreateTestScenario(): void
    {
        $response = $this->actingAs($this->adminUser)->post("/admin/influencer-testing/{$this->influencer->id}/test-scenario", [
            'scenario' => 'high_performer',
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify posts were created
        $storyCount = IgPost::where('influencer_id', $this->influencer->id)
                           ->where('post_type', 'story')
                           ->count();
        $reelCount = IgPost::where('influencer_id', $this->influencer->id)
                          ->where('post_type', 'reel')
                          ->count();

        $this->assertEquals(20, $storyCount);
        $this->assertEquals(10, $reelCount);

        // Verify reach values are higher than estimated (high performer scenario)
        $estimatedStoryReach = $this->influencer->followers * 0.05;
        $avgStoryReach = IgPost::where('influencer_id', $this->influencer->id)
                              ->where('post_type', 'story')
                              ->avg('reach');

        $this->assertGreaterThan($estimatedStoryReach, $avgStoryReach);
    }
}
