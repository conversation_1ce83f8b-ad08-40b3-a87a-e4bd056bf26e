<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\InfluencerRequestDetail;
use App\Models\User;
use App\Models\InfluencerDetail;
use Illuminate\Foundation\Testing\RefreshDatabase;

class InfluencerRequestDetailAmountProtectionTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function amount_fields_are_protected_from_mass_assignment()
    {
        // Create test data
        $user = User::factory()->create();
        $influencerDetail = InfluencerDetail::factory()->create(['user_id' => $user->id]);
        
        $detail = InfluencerRequestDetail::create([
            'user_id' => $user->id,
            'influencer_detail_id' => $influencerDetail->id,
            'compaign_id' => 'TEST123',
            'compaign_title' => 'Test Campaign',
            'payment_status' => 'new',
        ]);

        // Set initial amount values using explicit assignment
        $detail->cash_out_amount = 100.50;
        $detail->platform_amount = 25.75;
        $detail->influencer_amount = 126.25;
        $detail->save();

        // Verify amounts are set
        $this->assertEquals(100.50, $detail->fresh()->cash_out_amount);
        $this->assertEquals(25.75, $detail->fresh()->platform_amount);
        $this->assertEquals(126.25, $detail->fresh()->influencer_amount);

        // Attempt mass assignment with null values (simulating form submission)
        $formData = [
            'payment_status' => 'completed',
            'cash_out_amount' => null,
            'platform_amount' => null,
            'influencer_amount' => null,
            'review' => 1,
        ];

        $detail->update($formData);

        // Verify that amount fields were NOT overwritten with null
        $updatedDetail = $detail->fresh();
        $this->assertEquals(100.50, $updatedDetail->cash_out_amount, 'cash_out_amount should not be overwritten by mass assignment');
        $this->assertEquals(25.75, $updatedDetail->platform_amount, 'platform_amount should not be overwritten by mass assignment');
        $this->assertEquals(126.25, $updatedDetail->influencer_amount, 'influencer_amount should not be overwritten by mass assignment');
        
        // Verify other fields were updated
        $this->assertEquals('completed', $updatedDetail->payment_status);
        $this->assertEquals(1, $updatedDetail->review);
    }

    /** @test */
    public function amount_fields_can_still_be_updated_explicitly()
    {
        // Create test data
        $user = User::factory()->create();
        $influencerDetail = InfluencerDetail::factory()->create(['user_id' => $user->id]);
        
        $detail = InfluencerRequestDetail::create([
            'user_id' => $user->id,
            'influencer_detail_id' => $influencerDetail->id,
            'compaign_id' => 'TEST123',
            'compaign_title' => 'Test Campaign',
            'payment_status' => 'new',
        ]);

        // Set initial amounts
        $detail->cash_out_amount = 50.00;
        $detail->platform_amount = 10.00;
        $detail->influencer_amount = 60.00;
        $detail->save();

        // Update amounts using explicit assignment
        $detail->cash_out_amount = 75.25;
        $detail->platform_amount = 15.50;
        $detail->influencer_amount = 90.75;
        $detail->save();

        // Verify amounts were updated
        $updatedDetail = $detail->fresh();
        $this->assertEquals(75.25, $updatedDetail->cash_out_amount);
        $this->assertEquals(15.50, $updatedDetail->platform_amount);
        $this->assertEquals(90.75, $updatedDetail->influencer_amount);
    }

    /** @test */
    public function amount_fields_can_be_updated_via_specific_update_array()
    {
        // Create test data
        $user = User::factory()->create();
        $influencerDetail = InfluencerDetail::factory()->create(['user_id' => $user->id]);
        
        $detail = InfluencerRequestDetail::create([
            'user_id' => $user->id,
            'influencer_detail_id' => $influencerDetail->id,
            'compaign_id' => 'TEST123',
            'compaign_title' => 'Test Campaign',
            'payment_status' => 'new',
        ]);

        // Update amounts using specific update array (like StripeMarketplaceController does)
        $detail->update([
            'cash_out_amount' => 200.00,
            'platform_amount' => 40.00,
            'influencer_amount' => 240.00,
            'payment_status' => 'completed',
        ]);

        // Verify amounts were updated
        $updatedDetail = $detail->fresh();
        $this->assertEquals(200.00, $updatedDetail->cash_out_amount);
        $this->assertEquals(40.00, $updatedDetail->platform_amount);
        $this->assertEquals(240.00, $updatedDetail->influencer_amount);
        $this->assertEquals('completed', $updatedDetail->payment_status);
    }
}
