<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Models\SocialConnect;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SocialConnectTest extends TestCase
{
    use RefreshDatabase;

    public function testCanStoreLongTokens(): void
    {
        $user = User::create([
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'influencer',
        ]);
        $longToken = str_repeat('a', 500); // 500 character token
        $longTokenSecret = str_repeat('b', 300); // 300 character token secret
        $longSocialId = 'fb_' . str_repeat('**********', 25); // 253 character social ID

        $socialConnect = SocialConnect::create([
            'user_id' => $user->id,
            'media' => 'facebook',
            'name' => 'Test User',
            'token' => $longToken,
            'token_secret' => $longTokenSecret,
            'social_id' => $longSocialId,
        ]);

        $this->assertEquals($longToken, $socialConnect->token);
        $this->assertEquals($longTokenSecret, $socialConnect->token_secret);
        $this->assertEquals($longSocialId, $socialConnect->social_id);
    }

    public function testCanStoreLongUrls(): void
    {
        $user = User::create([
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'influencer',
        ]);
        $longPictureUrl = 'https://scontent-lax3-2.xx.fbcdn.net/v/t39.30808-1/347251818_18348973265056516_4229661093043872055_n.jpg?stp=dst-jpg_p320x320&_nc_cat=1&ccb=1-7&_nc_sid=5f2048&_nc_ohc=example&_nc_ht=scontent-lax3-2.xx&oh=00_AfABC123&oe=6571234A';
        $longProfileUrl = 'https://www.linkedin.com/in/username/?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app&extra_param=value';

        $socialConnect = SocialConnect::create([
            'user_id' => $user->id,
            'media' => 'facebook',
            'name' => 'Test User',
            'picture' => $longPictureUrl,
            'url' => $longProfileUrl,
            'social_id' => '123456789',
        ]);

        $this->assertEquals($longPictureUrl, $socialConnect->picture);
        $this->assertEquals($longProfileUrl, $socialConnect->url);
    }

    public function testTokenValidationMethods(): void
    {
        $socialConnect = new SocialConnect([
            'token' => 'valid_token',
            'token_secret' => 'valid_secret',
        ]);

        $this->assertTrue($socialConnect->hasValidToken());
        $this->assertTrue($socialConnect->isOAuth1());
        $this->assertFalse($socialConnect->isOAuth2());
    }

    public function testUrlValidationMethods(): void
    {
        $socialConnect = new SocialConnect([
            'url' => 'https://example.com/profile',
            'picture' => 'https://example.com/picture.jpg',
        ]);

        $this->assertTrue($socialConnect->hasValidUrl());
        $this->assertTrue($socialConnect->hasValidPicture());
        $this->assertEquals('example.com', $socialConnect->getUrlDomain());
    }

    public function testCanStoreLongSocialIds(): void
    {
        $user = User::create([
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'influencer',
        ]);

        // Test various long social ID formats
        $testCases = [
            'instagram' => 'ig_business_account_' . str_repeat('**********', 20), // 234 chars
            'facebook' => 'fb_page_' . str_repeat('abcdef', 35) . '_' . time(), // ~230 chars
            'tiktok' => 'tt_user_' . str_repeat('xyz123', 35) . '_verified_long', // ~230 chars
            'youtube' => 'yt_channel_UC' . str_repeat('A1B2C3D4E5', 20) . '_official_channel', // ~230 chars
        ];

        foreach ($testCases as $platform => $longSocialId) {
            $socialConnect = SocialConnect::create([
                'user_id' => $user->id,
                'media' => $platform,
                'name' => "Test User {$platform}",
                'social_id' => $longSocialId,
            ]);

            $this->assertEquals($longSocialId, $socialConnect->social_id);
            $this->assertTrue(strlen($socialConnect->social_id) > 191, "Social ID for {$platform} should be longer than 191 chars");
        }
    }

    public function testSocialIdValidationMethods(): void
    {
        $longSocialId = 'very_long_social_id_' . str_repeat('**********', 10);
        $socialConnect = new SocialConnect([
            'social_id' => $longSocialId,
        ]);

        $this->assertTrue($socialConnect->hasValidSocialId());

        // Test default truncation (20 chars)
        $truncated = $socialConnect->getTruncatedSocialId();
        $this->assertEquals(20, strlen($truncated));
        $this->assertStringEndsWith('...', $truncated);

        // Test custom length truncation (30 chars)
        $truncated30 = $socialConnect->getTruncatedSocialId(30);
        $this->assertEquals(30, strlen($truncated30));
        $this->assertStringEndsWith('...', $truncated30);

        $emptySocialConnect = new SocialConnect(['social_id' => '']);
        $this->assertFalse($emptySocialConnect->hasValidSocialId());
        $this->assertEquals('', $emptySocialConnect->getTruncatedSocialId());
    }
}
