<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\ReachBasedPriceCalculatorService;
use App\Services\AverageReachCalculatorService;
use App\Models\AdminPricing;
use App\Models\SocialConnect;
use App\Models\SocialPost;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;

class ReachBasedPriceCalculatorServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $service;
    protected $mockAverageReachCalculator;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->mockAverageReachCalculator = Mockery::mock(AverageReachCalculatorService::class);
        $this->service = new ReachBasedPriceCalculatorService($this->mockAverageReachCalculator);
        $this->user = User::factory()->create();
    }

    /** @test */
    public function it_calculates_reach_adjusted_price_correctly()
    {
        // Setup test data
        $this->mockAverageReachCalculator
            ->shouldReceive('calculateAverageReachByCampaignType')
            ->with($this->user->id, 'Boost me')
            ->andReturn(3000);

        SocialConnect::factory()->create([
            'user_id' => $this->user->id,
            'media' => 'instagram',
            'followers' => 10000
        ]);

        AdminPricing::factory()->create([
            'media' => 'Instagram',
            'type' => 'Boost me',
            'country' => 'Standard',
            'estimated_reach' => 20.00 // 20%
        ]);

        $result = $this->service->calculateReachAdjustedPrice(
            $this->user->id,
            'instagram',
            'Boost me',
            40.0, // Base price after gamification
            'Standard'
        );

        // Expected calculation: 3000 / (10000 * 0.20) = 3000 / 2000 = 1.5
        // Adjusted price: 40.0 * 1.5 = 60.0
        $this->assertEquals(60.0, $result['price']);
        $this->assertEquals(1.5, $result['reach_multiplier']);
        $this->assertTrue($result['used_reach_pricing']);
    }

    /** @test */
    public function it_skips_reach_pricing_when_average_reach_is_zero()
    {
        $this->mockAverageReachCalculator
            ->shouldReceive('calculateAverageReachByCampaignType')
            ->with($this->user->id, 'Boost me')
            ->andReturn(0);

        $result = $this->service->calculateReachAdjustedPrice(
            $this->user->id,
            'instagram',
            'Boost me',
            40.0,
            'Standard'
        );

        $this->assertEquals(40.0, $result['price']);
        $this->assertEquals(1.0, $result['reach_multiplier']);
        $this->assertFalse($result['used_reach_pricing']);
        $this->assertStringContains('Average reach is 0', $result['debug_info']['reason']);
    }

    /** @test */
    public function it_skips_reach_pricing_when_no_estimated_reach_configured()
    {
        $this->mockAverageReachCalculator
            ->shouldReceive('calculateAverageReachByCampaignType')
            ->with($this->user->id, 'Boost me')
            ->andReturn(3000);

        // No AdminPricing record with estimated_reach

        $result = $this->service->calculateReachAdjustedPrice(
            $this->user->id,
            'instagram',
            'Boost me',
            40.0,
            'Standard'
        );

        $this->assertEquals(40.0, $result['price']);
        $this->assertEquals(1.0, $result['reach_multiplier']);
        $this->assertFalse($result['used_reach_pricing']);
        $this->assertStringContains('No estimated reach percentage configured', $result['debug_info']['reason']);
    }

    /** @test */
    public function it_skips_reach_pricing_when_no_social_connect_found()
    {
        $this->mockAverageReachCalculator
            ->shouldReceive('calculateAverageReachByCampaignType')
            ->with($this->user->id, 'Boost me')
            ->andReturn(3000);

        AdminPricing::factory()->create([
            'media' => 'Instagram',
            'type' => 'Boost me',
            'country' => 'Standard',
            'estimated_reach' => 20.00
        ]);

        // No SocialConnect record

        $result = $this->service->calculateReachAdjustedPrice(
            $this->user->id,
            'instagram',
            'Boost me',
            40.0,
            'Standard'
        );

        $this->assertEquals(40.0, $result['price']);
        $this->assertEquals(1.0, $result['reach_multiplier']);
        $this->assertFalse($result['used_reach_pricing']);
        $this->assertStringContains('No valid social media connection', $result['debug_info']['reason']);
    }

    /** @test */
    public function it_handles_different_campaign_types()
    {
        $this->mockAverageReachCalculator
            ->shouldReceive('calculateAverageReachByCampaignType')
            ->with($this->user->id, 'Survey')
            ->andReturn(1500);

        SocialConnect::factory()->create([
            'user_id' => $this->user->id,
            'media' => 'instagram',
            'followers' => 5000
        ]);

        AdminPricing::factory()->create([
            'media' => 'Instagram',
            'type' => 'Survey',
            'country' => 'Standard',
            'estimated_reach' => 15.00 // 15%
        ]);

        $result = $this->service->calculateReachAdjustedPrice(
            $this->user->id,
            'instagram',
            'Survey',
            30.0,
            'Standard'
        );

        // Expected calculation: 1500 / (5000 * 0.15) = 1500 / 750 = 2.0
        // Adjusted price: 30.0 * 2.0 = 60.0
        $this->assertEquals(60.0, $result['price']);
        $this->assertEquals(2.0, $result['reach_multiplier']);
        $this->assertTrue($result['used_reach_pricing']);
    }

    /** @test */
    public function it_handles_reaction_video_campaign_type()
    {
        $this->mockAverageReachCalculator
            ->shouldReceive('calculateAverageReachByCampaignType')
            ->with($this->user->id, 'Reaction video')
            ->andReturn(4000);

        SocialConnect::factory()->create([
            'user_id' => $this->user->id,
            'media' => 'instagram',
            'followers' => 20000
        ]);

        AdminPricing::factory()->create([
            'media' => 'Instagram',
            'type' => 'Reaction-Video',
            'country' => 'Standard',
            'estimated_reach' => 25.00 // 25%
        ]);

        $result = $this->service->calculateReachAdjustedPrice(
            $this->user->id,
            'instagram',
            'Reaction video',
            50.0,
            'Standard'
        );

        // Expected calculation: 4000 / (20000 * 0.25) = 4000 / 5000 = 0.8
        // Adjusted price: 50.0 * 0.8 = 40.0
        $this->assertEquals(40.0, $result['price']);
        $this->assertEquals(0.8, $result['reach_multiplier']);
        $this->assertTrue($result['used_reach_pricing']);
    }

    /** @test */
    public function it_provides_debug_information()
    {
        $this->mockAverageReachCalculator
            ->shouldReceive('calculateAverageReachByCampaignType')
            ->with($this->user->id, 'Boost me')
            ->andReturn(2500);

        SocialConnect::factory()->create([
            'user_id' => $this->user->id,
            'media' => 'instagram',
            'followers' => 10000
        ]);

        AdminPricing::factory()->create([
            'media' => 'Instagram',
            'type' => 'Boost me',
            'country' => 'Standard',
            'estimated_reach' => 20.00
        ]);

        $result = $this->service->calculateReachAdjustedPrice(
            $this->user->id,
            'instagram',
            'Boost me',
            40.0,
            'Standard'
        );

        $debugInfo = $result['debug_info'];
        $this->assertEquals(2500, $debugInfo['average_reach']);
        $this->assertEquals(10000, $debugInfo['followers']);
        $this->assertEquals(20.00, $debugInfo['estimated_reach_percentage']);
        $this->assertEquals(2000, $debugInfo['expected_reach']);
        $this->assertEquals(40.0, $debugInfo['base_price_after_gamification']);
        $this->assertEquals(50.0, $debugInfo['adjusted_price']);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
