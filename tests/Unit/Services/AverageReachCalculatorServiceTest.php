<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\AverageReachCalculatorService;
use App\Models\SocialPost;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

class AverageReachCalculatorServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $service;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new AverageReachCalculatorService();
        $this->user = User::factory()->create();
    }

    /** @test */
    public function it_calculates_average_reach_by_campaign_type()
    {
        // Create posts with reach data
        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'story',
            'published_at' => Carbon::now()->subHours(12),
            'insights' => ['reach' => 1000]
        ]);

        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'story',
            'published_at' => Carbon::now()->subHours(6),
            'insights' => ['reach' => 2000]
        ]);

        $averageReach = $this->service->calculateAverageReachByCampaignType($this->user->id, 'Boost me');
        
        $this->assertEquals(1500, $averageReach);
    }

    /** @test */
    public function it_returns_zero_for_unknown_campaign_type()
    {
        $averageReach = $this->service->calculateAverageReachByCampaignType($this->user->id, 'Unknown Type');
        
        $this->assertEquals(0, $averageReach);
    }

    /** @test */
    public function it_returns_zero_when_no_posts_exist()
    {
        $averageReach = $this->service->calculateAverageReachByCampaignType($this->user->id, 'Boost me');
        
        $this->assertEquals(0, $averageReach);
    }

    /** @test */
    public function it_only_considers_posts_from_last_24_hours()
    {
        // Create old post (should be ignored)
        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'story',
            'published_at' => Carbon::now()->subHours(30),
            'insights' => ['reach' => 5000]
        ]);

        // Create recent post (should be included)
        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'story',
            'published_at' => Carbon::now()->subHours(12),
            'insights' => ['reach' => 1000]
        ]);

        $averageReach = $this->service->calculateAverageReachByCampaignType($this->user->id, 'Boost me');
        
        $this->assertEquals(1000, $averageReach);
    }

    /** @test */
    public function it_ignores_posts_with_zero_reach()
    {
        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'story',
            'published_at' => Carbon::now()->subHours(12),
            'insights' => ['reach' => 0]
        ]);

        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'story',
            'published_at' => Carbon::now()->subHours(6),
            'insights' => ['reach' => 2000]
        ]);

        $averageReach = $this->service->calculateAverageReachByCampaignType($this->user->id, 'Boost me');
        
        $this->assertEquals(2000, $averageReach);
    }

    /** @test */
    public function it_calculates_average_reach_for_all_post_types()
    {
        // Create posts for different types
        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'story',
            'published_at' => Carbon::now()->subHours(12),
            'insights' => ['reach' => 1000]
        ]);

        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'reel',
            'published_at' => Carbon::now()->subHours(6),
            'insights' => ['reach' => 3000]
        ]);

        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'post',
            'published_at' => Carbon::now()->subHours(3),
            'insights' => ['reach' => 2000]
        ]);

        $results = $this->service->calculateAverageReachForAllPostTypes($this->user->id);
        
        $this->assertEquals([
            'story' => 1000,
            'reel' => 3000,
            'post' => 2000,
        ], $results);
    }

    /** @test */
    public function it_checks_if_user_has_recent_posts_of_type()
    {
        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'story',
            'published_at' => Carbon::now()->subHours(12),
        ]);

        $this->assertTrue($this->service->hasRecentPostsOfType($this->user->id, 'story'));
        $this->assertFalse($this->service->hasRecentPostsOfType($this->user->id, 'reel'));
    }

    /** @test */
    public function it_provides_detailed_reach_statistics()
    {
        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'story',
            'post_id' => 'test_post_1',
            'published_at' => Carbon::now()->subHours(12),
            'insights' => ['reach' => 1000]
        ]);

        $stats = $this->service->getReachStatistics($this->user->id, 'story');
        
        $this->assertEquals('story', $stats['post_type']);
        $this->assertEquals(1, $stats['total_posts']);
        $this->assertEquals(1, $stats['valid_posts_count']);
        $this->assertEquals(1000, $stats['total_reach']);
        $this->assertEquals(1000, $stats['average_reach']);
        $this->assertCount(1, $stats['posts']);
    }

    /** @test */
    public function it_handles_posts_without_insights_data()
    {
        // Create post without insights (should fall back to direct attributes)
        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'story',
            'published_at' => Carbon::now()->subHours(12),
            'reach' => 1500, // Direct attribute
            'insights' => null
        ]);

        $averageReach = $this->service->calculateAverageReachByCampaignType($this->user->id, 'Boost me');

        $this->assertEquals(1500, $averageReach);
    }
}
